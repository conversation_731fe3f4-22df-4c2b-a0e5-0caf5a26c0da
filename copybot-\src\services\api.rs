use std::net::SocketAddr;
use std::sync::Arc;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Signer;
use std::str::FromStr;
use axum::{
    extract::{State, Json, Query, Path},
    http::StatusCode,
    response::{IntoResponse, Response, Html},
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use validator::Validate;
use anyhow::{Result, anyhow};
use log::{info, error};
use uuid;
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::time::{SystemTime, UNIX_EPOCH};
use chrono::{DateTime, Utc};
use tower_http::cors::{Any, CorsLayer};
use tower_http::services::ServeDir;
use serde_json::json;
use std::fs;
use std::path::Path as FilePath;

use crate::common::utils::AppState;
use crate::engine::swap::pump_swap;
use crate::common::logger::Logger;
use crate::core::priority_fees::{calculate_tip, get_wallet_price_range, get_wallet_follow_percentage, get_wallet_price_range_emergency};
use crate::common::utils::config_monitor::{self, MonitorAddresses, WalletConfig as MonitorWalletConfig};
use crate::services::wallet_monitor::{WalletMonitor, WalletMonitorConfig, WalletMonitorRecord};
use crate::services::special_wallet_config::{SpecialWalletConfig, SpecialWalletConfigs};
// use crate::services::websocket_status::{
//     create_status_update, get_status_sender,
//     TransactionStatus
// };

use crate::common::globals::PAUSE_FOLLOW;
use std::sync::atomic::Ordering;

// API服务器状态
pub struct ApiServerState {
    pub app_state: Arc<AppState>,
    pub logger: Logger,
    pub wallet_monitor: Arc<WalletMonitor>, // 添加钱包监控服务
}

// 钱包配置数据结构 - 更新为与文档一致的版本
#[derive(Debug, Serialize, Deserialize, Clone, Validate)]
pub struct WalletConfig {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub wallet_address: String,          // 钱包地址
    
    pub is_active: bool,                // 是否激活跟单
    
    #[validate(range(min = 0.0, max = 100.0, message = "跟单比例必须在0-100之间"))]
    pub follow_percentage: f64,         // 跟单比例
    
    #[validate(range(min = 0.0, max = 100.0, message = "手续费增加比例必须在0-100之间"))]
    pub fee_increase_percentage: f64,   // 手续费增加比例
}

// 创建钱包配置请求 - 新增
#[derive(Debug, Deserialize, Validate)]
pub struct CreateWalletConfigRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub wallet_address: String,
    pub config: WalletConfigData,
}

// 卖出请求参数
#[derive(Debug, Deserialize, Validate)]
pub struct SellRequest {
    // 代币地址
    #[validate(length(min = 30, max = 44, message = "代币地址长度无效"))]
    pub token_address: String,
    
    // 卖出数量百分比：25, 50, 75, 100
    #[validate(range(min = 25, max = 100, message = "卖出百分比必须是25、50、75或100"))]
    pub percentage: u8,
    
    // 滑点（以基点为单位，1基点=0.01%）
    #[validate(range(min = 0, max = 5000, message = "滑点必须在0-5000之间(0-50%)"))]
    pub slippage_bps: Option<u64>,
    
    // 优先费（以微lamports为单位）
    #[validate(range(min = 1, max = 70000, message = "优先费必须在1-70000之间"))]
    pub priority_fee: Option<u64>,
}

// 卖出响应
#[derive(Debug, Serialize)]
pub struct SellResponse {
    pub success: bool,
    pub message: String,
    pub signatures: Option<Vec<String>>,
    pub error: Option<String>,
    pub status: String,             // 交易状态
    pub token_address: String,      // 代币地址
    pub token_symbol: Option<String>, // 代币符号
    pub sell_percentage: u8,        // 卖出百分比
    pub sell_amount: f64,           // 卖出数量（UI单位）
    pub sell_amount_raw: u64,       // 卖出数量（原始单位）
    pub tip_amount: Option<f64>,    // 小费数量（SOL）
    pub slippage_bps: u64,          // 滑点（基点）
    pub priority_fee: Option<u64>,  // 优先费
    pub submitted_at: String,       // 提交时间
}

// 更新钱包配置请求
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateWalletConfigRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub wallet_address: String,
    pub config: WalletConfigData,
}

// 钱包配置数据
#[derive(Debug, Serialize, Deserialize, Clone, Validate)]
pub struct WalletConfigData {
    pub is_active: bool,
    
    #[validate(range(min = 0.0, max = 100.0, message = "跟单比例必须在0-100之间"))]
    pub follow_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "手续费增加比例必须在0-100之间"))]
    pub fee_increase_percentage: f64,
}

// 暂停/恢复钱包请求
#[derive(Debug, Deserialize, Validate)]
pub struct WalletActionRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub wallet_address: String,
}

// 钱包配置响应
#[derive(Debug, Serialize)]
pub struct WalletConfigResponse {
    pub success: bool,
    pub message: Option<String>,
    pub data: Option<serde_json::Value>,
    pub error: Option<String>,
}

// 交易历史查询参数
#[derive(Debug, Deserialize)]
pub struct TransactionHistoryQuery {
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub token: Option<String>,  // 代币地址，用于过滤特定代币的交易
}

// 交易历史条目
#[derive(Debug, Serialize, Deserialize)]
pub struct TransactionInfo {
    pub signature: String,
    pub r#type: String,  // 使用 r#type 避免关键字冲突
    pub token_address: String,
    pub amount: f64,
    pub price: f64,
    pub timestamp: String,
    pub status: String,
    pub profit_loss: Option<f64>,         // 盈亏值（以SOL计）
    pub profit_loss_percentage: Option<f64>, // 盈亏百分比
    pub related_transactions: Option<Vec<String>>,  // 关联交易ID
    pub sol_amount: Option<f64>,          // 交易的SOL金额（买入花费的SOL或卖出获得的SOL）
    // 格式化后的字段 - 可选
    #[serde(skip_serializing_if = "Option::is_none")]
    pub formatted_amount: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub formatted_price: Option<String>,
}

// API错误类型
#[derive(Debug)]
pub enum ApiError {
    ValidationError(String),
    InternalError(String),
    NotFoundError(String),
    DuplicateError(String),  // 新增：重复钱包地址错误
    StorageError(String),    // 新增：存储操作错误
}

// 将ApiError转换为HTTP响应
impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            ApiError::ValidationError(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::InternalError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            ApiError::NotFoundError(msg) => (StatusCode::NOT_FOUND, msg),
            ApiError::DuplicateError(msg) => (StatusCode::CONFLICT, msg),
            ApiError::StorageError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
        };

        let body = Json(serde_json::json!({
            "success": false,
            "message": error_message,
            "error": error_message
        }));

        (status, body).into_response()
    }
}

// 添加监控地址请求
#[derive(Debug, Deserialize, Validate)]
pub struct AddMonitorAddressRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub address: String,
    
    #[validate(range(min = 0.0, max = 100.0, message = "跟单比例必须在0-100之间"))]
    pub follow_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "滑点百分比必须在0-100之间"))]
    pub slippage_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "小费百分比必须在0-100之间"))]
    pub tip_percentage: f64,
    
    #[validate(range(min = 0.0, message = "最小价格倍数必须大于0"))]
    pub min_price_multiplier: f64,
    
    #[validate(range(min = 0.0, message = "最大价格倍数必须大于0"))]
    pub max_price_multiplier: f64,
    
    pub priority_fee: Option<u64>,
    pub compute_unit_limit: Option<u32>,
}

// 更新监控地址请求
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateMonitorAddressRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub address: String,
    
    #[validate(range(min = 0.0, max = 100.0, message = "跟单比例必须在0-100之间"))]
    pub follow_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "滑点百分比必须在0-100之间"))]
    pub slippage_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "小费百分比必须在0-100之间"))]
    pub tip_percentage: f64,
    
    #[validate(range(min = 0.0, message = "最小价格倍数必须大于0"))]
    pub min_price_multiplier: f64,
    
    #[validate(range(min = 0.0, message = "最大价格倍数必须大于0"))]
    pub max_price_multiplier: f64,
    
    pub priority_fee: Option<u64>,
    pub compute_unit_limit: Option<u32>,
}

// 删除监控地址请求
#[derive(Debug, Deserialize, Validate)]
pub struct DeleteMonitorAddressRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub address: String,
}

// 更新全局配置请求
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateConfigRequest {
    pub sol_address: Option<String>,
    pub unwanted_key: Option<String>,
}

// 启动API服务器
pub async fn start_api_server(app_state: Arc<AppState>, port: u16) -> Result<()> {
    let logger = Logger::new("ApiServer".to_string());
    logger.log(format!("准备启动API服务器，监听端口: {}", port));
    
    // 初始化钱包监控服务
    let wallet_monitor = Arc::new(WalletMonitor::new());
    if let Err(e) = wallet_monitor.init().await {
        logger.error(format!("初始化钱包监控服务失败: {}", e));
        // 继续启动API服务器，但钱包监控功能可能不可用
    }
    
    let api_state = Arc::new(ApiServerState {
        app_state,
        logger: logger.clone(),
        wallet_monitor,
    });
    
    // 配置CORS
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);
    
    // 读取工作目录下的test_api.html文件
    let api_html = match fs::read_to_string("test_api.html") {
        Ok(content) => content,
        Err(e) => {
            logger.error(format!("读取test_api.html文件失败: {}", e));
            "<!DOCTYPE html><html><body><h1>API管理页面加载失败</h1><p>无法找到test_api.html文件</p></body></html>".to_string()
        }
    };

    // 构建API路由
    let app = Router::new()
        // 根路径提供前端页面
        .route("/", get(move || async move { Html(api_html.clone()) }))
        
        // 健康检查接口
        .route("/health", get(health_check))
        
        // 钱包配置管理接口
        .route("/api/wallet_configs", get(get_wallet_configs))
        .route("/api/wallet_configs", post(create_wallet_config))
        .route("/api/wallet_configs/:wallet_address", get(get_wallet_config))
        .route("/api/wallet_configs/:wallet_address", put(update_wallet_config))
        
        // 钱包操作接口
        .route("/api/wallet_configs/pause", post(pause_wallet))
        .route("/api/wallet_configs/resume", post(resume_wallet))
        
        // 交易接口
        .route("/api/sell", post(sell_token))
        
        // 余额和交易历史接口
        .route("/api/wallet_balance", get(get_wallet_balance))
        .route("/api/transaction_history", get(get_transaction_history))
        
        // 添加监控地址管理接口
        .route("/api/monitor-addresses", get(get_monitor_addresses))
        .route("/api/monitor-addresses", post(update_all_monitor_addresses))
        .route("/api/monitor-addresses/:address", get(get_monitor_address))
        .route("/api/monitor-addresses/add", post(add_monitor_address))
        .route("/api/monitor-addresses/update", put(update_monitor_address))
        .route("/api/monitor-addresses/delete", delete(delete_monitor_address))
        
        // 添加交易跟单暂停/恢复接口
        .route("/api/follow/pause", post(pause_follow))
        .route("/api/follow/resume", post(resume_follow))
        .route("/api/follow/status", get(follow_status))
        
        // 钱包监控接口
        .route("/api/wallet-monitor/config", get(get_wallet_monitor_config))
        .route("/api/wallet-monitor/config", put(update_wallet_monitor_config))
        .route("/api/wallet-monitor/records", get(get_wallet_monitor_records))
        .route("/api/wallet-monitor/records/:wallet_address", get(get_wallet_monitor_record))
        .route("/api/wallet-monitor/records/:wallet_address/reset", post(reset_wallet_monitor_record))
        .route("/api/wallet-monitor/records/:wallet_address", delete(delete_wallet_monitor_record))
        
        // 添加特殊钱包配置接口
        .route("/api/special-wallets", get(get_special_wallet_configs))
        .route("/api/special-wallets", post(add_special_wallet_config))
        .route("/api/special-wallets/:wallet_address", get(get_special_wallet_config))
        .route("/api/special-wallets/:wallet_address", put(update_special_wallet_config))
        .route("/api/special-wallets/:wallet_address", delete(delete_special_wallet_config))
        
        // 在API路由器设置部分添加新的路由
        .route("/api/monitor-addresses/update-note", post(update_monitor_note))
        
        // 静态文件服务
        .nest_service("/static", ServeDir::new("."))
        
        .layer(cors)
        .with_state(api_state);
    
    // 绑定地址
    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    logger.log(format!("API服务器启动，监听地址: {}", addr));
    
    // 使用tokio的TcpListener和axum的serve
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    logger.log("绑定成功，开始监听连接...".to_string());
    
    // 启动服务器 - 直接await，不调用into_future()
    axum::serve(listener, app).await
        .map_err(|e| anyhow!("API服务器启动失败: {}", e))?;
    
    Ok(())
}

// 健康检查
async fn health_check() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "ok",
        "version": "1.0.0",
        "message": "服务运行正常"
    }))
}

// 卖出代币
async fn sell_token(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<SellRequest>,
) -> Result<impl IntoResponse, ApiError> {
    // 记录请求
    state.logger.log(format!("收到卖出请求: token={}, percentage={}%, slippage_bps={:?}", 
                             payload.token_address, payload.percentage, payload.slippage_bps));
    
    // 验证请求参数
    if let Err(e) = payload.validate() {
        state.logger.error(format!("参数验证失败: {}", e));
        return Err(ApiError::ValidationError(e.to_string()));
    }
    
    // 验证百分比是否为 25, 50, 75 或 100
    if ![25, 50, 75, 100].contains(&payload.percentage) {
        return Err(ApiError::ValidationError(
            "卖出百分比必须是25、50、75或100".to_string()
        ));
    }
    
    // 检查代币地址是否有效
    if Pubkey::from_str(&payload.token_address).is_err() {
        return Err(ApiError::ValidationError(
            "代币地址格式无效".to_string()
        ));
    }

    // 获取当前钱包地址
    let wallet_address = state.app_state.wallet.pubkey().to_string();
    
    // 获取专用钱包配置（如果存在）
    let special_config = crate::services::special_wallet_config::get_special_wallet_config(&wallet_address);
    
    // 优先使用专用钱包配置的参数
    let (slippage_percentage, tip_percentage, priority_fee_multiplier) = if let Some(config) = &special_config {
        state.logger.log(format!("使用专用钱包配置进行卖出: 滑点={}%, 小费={}%, 优先费倍数={}",
            config.slippage_percentage, config.tip_percentage, config.priority_fee_multiplier));
        (config.slippage_percentage, config.tip_percentage, config.priority_fee_multiplier)
    } else {
        state.logger.log("未找到专用钱包配置，使用默认参数".to_string());
        (2.0, 1.0, 6.0) // 默认值：2%滑点，1%小费，6倍优先费
    };
    
    // 在钱包缓存中查找代币信息
    let mut sell_amount_ui = 0.0;
    let mut sell_amount_base = 0;
    let mut decimals = 9; // 默认小数位数
    let mut cached_price: Option<f64> = None; // 缓存的价格
    
    {
        let cache = state.app_state.wallet_cache.lock().await;
        if let Some(token_info) = cache.tokens.get(&payload.token_address) {
            // 根据百分比计算卖出数量
            let total_amount = token_info.balance;
            decimals = token_info.decimals;
            let sell_percentage = payload.percentage as f64 / 100.0;
            
            // 记录缓存的价格（如果有）
            cached_price = token_info.last_price;
            
            // 计算要卖出的具体数量
            sell_amount_base = (total_amount as f64 * sell_percentage) as u64;
            sell_amount_ui = sell_amount_base as f64 / 10f64.powi(decimals as i32);
            
            state.logger.log(format!(
                "计算卖出数量: 代币={}, 百分比={}%, 总量={}, 要卖出={}(基础单位={}), 缓存价格={:?}",
                payload.token_address,
                payload.percentage,
                total_amount,
                sell_amount_ui,
                sell_amount_base,
                cached_price
            ));
        } else {
            return Err(ApiError::ValidationError(
                format!("在钱包中未找到代币: {}", payload.token_address)
            ));
        }
    }
    
    // 获取滑点设置 - 优先使用请求参数，然后是专用配置，最后是默认值
    let slippage_bps = match payload.slippage_bps {
        Some(bps) => bps,
        None => (slippage_percentage * 100.0) as u64 // 转换为基点 (1% = 100基点)
    };
    
    // 获取优先费设置 - 根据专用配置计算
    let priority_fee = match payload.priority_fee {
        Some(fee) => Some(fee),
        None => {
            let compute_limit = special_config.as_ref().map_or(70000, |c| c.compute_limit);
            let base_fee = 1000; // 基础优先费
            Some(((base_fee as f64) * priority_fee_multiplier) as u64)
        }
    };
    
    // 确定使用的价格 - 优先使用缓存价格，如果没有则使用紧急价格范围
    let price_estimate = if let Some(price) = cached_price {
        state.logger.log(format!("使用缓存的代币价格: {}", price));
        price
    } else {
        // 未找到缓存价格，使用紧急价格范围
        let (min_price, max_price) = get_wallet_price_range_emergency();
        state.logger.log(format!("未找到缓存价格，使用紧急价格范围: {} - {}", min_price, max_price));
        
        // 使用保守的价格估算
        min_price * 10.0 // 使用最小价格的10倍，但仍远低于max_price(1000.0)
    };
    
    // 卖出时的最小SOL获取量 - 如果有缓存价格，使用较小的滑点；如果没有，使用非常小的值
    let min_sol_out_lamports = if cached_price.is_some() {
        // 计算交易金额（SOL）
        let trade_amount_sol = sell_amount_ui * price_estimate;
        let expected_sol_out_lamports = (trade_amount_sol * 1_000_000_000.0) as u64;
        let min_lamports = (expected_sol_out_lamports as f64 * (1.0 - slippage_bps as f64 / 10000.0)).floor() as u64;
        
        // 确保最小值不为0
        min_lamports.max(1)
    } else {
        // 如果没有价格缓存，设置为极小值，确保任何价格都能成功卖出
        1 // 设置为1 lamport，相当于没有最低价格限制
    };
    
    // 计算交易金额（SOL） - 用于日志和小费计算
    let trade_amount_sol = sell_amount_ui * price_estimate;
    
    // 计算小费金额，使用专用钱包的小费百分比
    let tip_amount = (trade_amount_sol * (tip_percentage / 100.0)).max(0.0005).min(1.0);
    
    // 记录计算后的交易信息
    state.logger.log(format!(
        "执行卖出交易: token={}, 数量={}%, 实际数量={} ({}), 估计价格={}, 交易金额={} SOL, 滑点={} 基点({}%), 小费={} SOL, 优先费={:?}, 最小获得SOL={} lamports",
        payload.token_address, 
        payload.percentage,
        sell_amount_ui,
        sell_amount_base,
        price_estimate,
        trade_amount_sol,
        slippage_bps,
        slippage_bps as f64 / 100.0,
        tip_amount,
        priority_fee,
        min_sol_out_lamports
    ));
    
    // 生成当前时间戳
    let submitted_at = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
    
    // 生成请求ID用于跟踪
    let request_id = format!("req-{}", uuid::Uuid::new_v4());
    
    // 克隆参数用于后台任务
    let app_state_clone = state.app_state.clone();
    let token_address_clone = payload.token_address.clone();
    let logger_clone = state.logger.clone();
    let request_id_clone = request_id.clone();
    
    // 创建一个通道，用于接收交易结果
    let (tx, rx) = tokio::sync::oneshot::channel();
    
    // 在后台启动一个新任务执行交易
    tokio::spawn(async move {
        logger_clone.log(format!("[后台任务 {}] 开始执行卖出交易", request_id_clone));
        
        // 调用pump_swap函数执行卖出
        let result = pump_swap(
            app_state_clone,
            sell_amount_ui,
            "sell",
            "qty",
            slippage_bps,
            false, // 不使用jito
            &token_address_clone,
            None,  // 价格覆盖
            priority_fee,
            Some(sell_amount_base),  // 卖出的代币数量
            Some(min_sol_out_lamports),  // 最小获得SOL数量
            None,  // 固定小费
            Some(tip_amount),  // 百分比小费
            Some(state.app_state.wallet.pubkey().to_string()), // 使用当前钱包地址作为交易发起者
        ).await;
        
        // 处理结果
        let response = match result {
            Ok(signatures) => {
                logger_clone.log(format!("[后台任务 {}] 卖出交易成功完成，签名: {:?}", request_id_clone, signatures));
                
                // 创建成功响应
                SellResponse {
                    success: true,
                    message: format!("卖出交易成功完成 ({}% 的代币 {})", 
                               payload.percentage, token_address_clone),
                    signatures: Some(signatures),
                    error: None,
                    status: "已完成".to_string(),
                    token_address: token_address_clone,
                    token_symbol: None,
                    sell_percentage: payload.percentage,
                    sell_amount: sell_amount_ui,
                    sell_amount_raw: sell_amount_base,
                    tip_amount: Some(tip_amount),
                    slippage_bps,
                    priority_fee,
                    submitted_at: chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
                }
            },
            Err(e) => {
                let error_msg = e.to_string();
                logger_clone.error(format!("[后台任务 {}] 卖出交易失败: {}", request_id_clone, error_msg));
                
                // 创建失败响应
                SellResponse {
                    success: false,
                    message: format!("卖出交易失败: {}", error_msg),
                    signatures: None,
                    error: Some(error_msg),
                    status: "失败".to_string(),
                    token_address: token_address_clone,
                    token_symbol: None,
                    sell_percentage: payload.percentage,
                    sell_amount: sell_amount_ui,
                    sell_amount_raw: sell_amount_base,
                    tip_amount: Some(tip_amount),
                    slippage_bps,
                    priority_fee,
                    submitted_at: chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
                }
            }
        };
        
        // 发送响应结果回主线程
        let _ = tx.send(response);
    });
    
    // 等待后台任务完成并返回结果
    match rx.await {
        Ok(response) => Ok(Json(response).into_response()),
        Err(_) => {
            // 如果通道关闭，返回错误
            Err(ApiError::InternalError("处理交易时发生内部错误".to_string()))
        }
    }
}

// 获取钱包配置列表
async fn get_wallet_configs(
    State(state): State<Arc<ApiServerState>>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理获取所有钱包配置请求".to_string());
    
    let configs = state.app_state.wallet_configs.read().await;
    let response = serde_json::json!({
        "success": true,
        "data": *configs
    });
    
    Ok(Json(response))
}

// 获取单个钱包配置
async fn get_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理获取钱包配置请求: {}", wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&wallet_address)?;
    
    let configs = state.app_state.wallet_configs.read().await;
    if let Some(config) = configs.get(&wallet_address) {
        // 格式化为响应
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let dt = DateTime::from_timestamp(now as i64, 0)
            .unwrap_or_else(|| DateTime::UNIX_EPOCH);
        
        let response = serde_json::json!({
            "success": true,
            "data": {
                "wallet_address": wallet_address,
                "config": {
                    "is_active": config.is_active,
                    "follow_percentage": config.follow_percentage,
                    "fee_increase_percentage": config.fee_increase_percentage
                },
                "last_updated": dt.to_rfc3339()
            }
        });
        
        Ok(Json(response))
    } else {
        Err(ApiError::NotFoundError(format!("钱包配置不存在: {}", wallet_address)))
    }
}

// 创建钱包配置 - 新增
async fn create_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<CreateWalletConfigRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理创建钱包配置请求: {}", payload.wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&payload.wallet_address)?;
    
    // 检查钱包是否已存在
    let mut configs = state.app_state.wallet_configs.write().await;
    
    if configs.contains_key(&payload.wallet_address) {
        return Err(ApiError::DuplicateError(
            format!("钱包配置已存在: {}", payload.wallet_address)
        ));
    }
    
    // 创建新配置
    let config = WalletConfig {
        wallet_address: payload.wallet_address.clone(),
        is_active: payload.config.is_active,
        follow_percentage: payload.config.follow_percentage,
        fee_increase_percentage: payload.config.fee_increase_percentage,
    };
    
    configs.insert(payload.wallet_address.clone(), config.clone());
    
    // 保存配置到文件
    drop(configs); // 释放写锁
    if let Err(e) = state.app_state.save_wallet_configs().await {
        logger.error(format!("保存钱包配置失败: {}", e));
        return Err(ApiError::StorageError("配置保存失败".to_string()));
    }
    
    // 格式化为响应
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let dt = DateTime::from_timestamp(now as i64, 0)
        .unwrap_or_else(|| DateTime::UNIX_EPOCH);
    
    let response = serde_json::json!({
        "success": true,
        "message": "钱包配置创建成功",
        "data": {
            "wallet_address": payload.wallet_address,
            "config": {
                "is_active": config.is_active,
                "follow_percentage": config.follow_percentage,
                "fee_increase_percentage": config.fee_increase_percentage
            },
            "created_at": dt.to_rfc3339()
        }
    });
    
    Ok(Json(response))
}

// 更新钱包配置 - 保持现有实现
async fn update_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<UpdateWalletConfigRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理更新钱包配置请求: {}", payload.wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&payload.wallet_address)?;
    
    // 更新配置
    let mut configs = state.app_state.wallet_configs.write().await;
    
    // 检查钱包是否存在
    if !configs.contains_key(&payload.wallet_address) {
        return Err(ApiError::NotFoundError(
            format!("钱包配置不存在: {}", payload.wallet_address)
        ));
    }
    
    let config = WalletConfig {
        wallet_address: payload.wallet_address.clone(),
        is_active: payload.config.is_active,
        follow_percentage: payload.config.follow_percentage,
        fee_increase_percentage: payload.config.fee_increase_percentage,
    };
    
    configs.insert(payload.wallet_address.clone(), config.clone());
    
    // 保存配置到文件
    drop(configs); // 释放写锁
    if let Err(e) = state.app_state.save_wallet_configs().await {
        logger.error(format!("保存钱包配置失败: {}", e));
        return Err(ApiError::StorageError("配置保存失败".to_string()));
    }
    
    // 格式化为响应
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let dt = DateTime::from_timestamp(now as i64, 0)
        .unwrap_or_else(|| DateTime::UNIX_EPOCH);
    
    let response = serde_json::json!({
        "success": true,
        "message": "钱包配置更新成功",
        "data": {
            "wallet_address": payload.wallet_address,
            "config": {
                "is_active": config.is_active,
                "follow_percentage": config.follow_percentage,
                "fee_increase_percentage": config.fee_increase_percentage
            },
            "updated_at": dt.to_rfc3339()
        }
    });
    
    Ok(Json(response))
}

// 暂停钱包跟单
async fn pause_wallet(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<WalletActionRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理暂停钱包跟单请求: {}", payload.wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&payload.wallet_address)?;
    
    // 更新配置
    let mut configs = state.app_state.wallet_configs.write().await;
    
    if let Some(mut config) = configs.get(&payload.wallet_address).cloned() {
        if !config.is_active {
            return Err(ApiError::ValidationError("钱包已处于暂停状态".to_string()));
        }
        
        // 更新状态
        config.is_active = false;
        configs.insert(payload.wallet_address.clone(), config);
        
        // 保存配置到文件
        drop(configs); // 释放写锁
        if let Err(e) = state.app_state.save_wallet_configs().await {
            logger.error(format!("保存钱包配置失败: {}", e));
            return Err(ApiError::StorageError("配置保存失败".to_string()));
        }
        
        // 格式化为响应
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let dt = DateTime::from_timestamp(now as i64, 0)
            .unwrap_or_else(|| DateTime::UNIX_EPOCH);
        
        let response = serde_json::json!({
            "success": true,
            "message": "钱包跟单已暂停",
            "data": {
                "wallet_address": payload.wallet_address,
                "status": "paused",
                "updated_at": dt.to_rfc3339()
            }
        });
        
        Ok(Json(response))
    } else {
        Err(ApiError::NotFoundError(format!("钱包配置不存在: {}", payload.wallet_address)))
    }
}

// 恢复钱包跟单
async fn resume_wallet(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<WalletActionRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理恢复钱包跟单请求: {}", payload.wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&payload.wallet_address)?;
    
    // 更新配置
    let mut configs = state.app_state.wallet_configs.write().await;
    
    if let Some(mut config) = configs.get(&payload.wallet_address).cloned() {
        if config.is_active {
            return Err(ApiError::ValidationError("钱包已处于激活状态".to_string()));
        }
        
        // 更新状态
        config.is_active = true;
        configs.insert(payload.wallet_address.clone(), config);
        
        // 保存配置到文件
        drop(configs); // 释放写锁
        if let Err(e) = state.app_state.save_wallet_configs().await {
            logger.error(format!("保存钱包配置失败: {}", e));
            return Err(ApiError::StorageError("配置保存失败".to_string()));
        }
        
        // 格式化为响应
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let dt = DateTime::from_timestamp(now as i64, 0)
            .unwrap_or_else(|| DateTime::UNIX_EPOCH);
        
        let response = serde_json::json!({
            "success": true,
            "message": "钱包跟单已恢复",
            "data": {
                "wallet_address": payload.wallet_address,
                "status": "active",
                "updated_at": dt.to_rfc3339()
            }
        });
        
        Ok(Json(response))
    } else {
        Err(ApiError::NotFoundError(format!("钱包配置不存在: {}", payload.wallet_address)))
    }
}

// 获取钱包余额
async fn get_wallet_balance(
    State(state): State<Arc<ApiServerState>>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理获取钱包余额请求".to_string());
    
    // 获取钱包缓存中的余额信息
    let cache = state.app_state.wallet_cache.lock().await;
    
    // 将 SOL 余额从 lamports 转换为 SOL
    let sol_balance = (cache.sol_balance as f64) / 1_000_000_000.0;
    
    // 格式化代币余额
    let tokens = cache.tokens.clone();
    let token_balances = tokens.iter().map(|(address, token_info)| {
        (
            address.clone(),
            serde_json::json!({
                "balance": token_info.balance,
                "decimals": token_info.decimals,
                "mint": token_info.mint
            })
        )
    }).collect::<HashMap<String, serde_json::Value>>();
    
    // 格式化为响应
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let dt = DateTime::from_timestamp(now as i64, 0)
        .unwrap_or_else(|| DateTime::UNIX_EPOCH);
    
    let response = serde_json::json!({
        "success": true,
        "data": {
            "sol_balance": sol_balance,
            "tokens": token_balances,
            "last_updated": dt.to_rfc3339()
        }
    });
    
    Ok(Json(response))
}

// 计算交易历史统计
fn calculate_transaction_statistics(transactions: &mut Vec<TransactionInfo>) {
    // 按代币地址分组
    let mut token_transactions: std::collections::HashMap<String, Vec<&mut TransactionInfo>> = std::collections::HashMap::new();
    
    // 第一步：将交易按代币分组
    for tx in transactions.iter_mut() {
        token_transactions
            .entry(tx.token_address.clone())
            .or_insert_with(Vec::new)
            .push(tx);
    }
    
    // 第二步：对每个代币的交易进行处理
    for (_, txs) in token_transactions.iter_mut() {
        // 按时间戳排序（从早到晚）
        txs.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));
        
        // 跟踪买入和卖出记录，所有交易都参与计算
        let mut buy_records: Vec<(&mut TransactionInfo, f64)> = Vec::new(); // (交易, SOL成本)
        
        // 处理每一笔交易
        for tx in txs.iter_mut() {
            match tx.r#type.as_str() {
                "buy" => {
                    // 买入交易：SOL成本 = 数量 * 价格
                    let sol_cost = tx.amount * tx.price;
                    
                    // 设置SOL金额
                    tx.sol_amount = Some(sol_cost);
                    
                    // 买入交易没有盈亏
                    tx.profit_loss = Some(0.0);
                    tx.profit_loss_percentage = Some(0.0);
                    
                    buy_records.push((*tx, sol_cost));
                },
                "sell" => {
                    if buy_records.is_empty() {
                        // 如果没有买入记录，无法计算盈亏
                        tx.profit_loss = None;
                        tx.profit_loss_percentage = None;
                        
                        // 但仍然可以计算卖出的SOL金额
                        let sol_received = tx.amount * tx.price;
                        tx.sol_amount = Some(sol_received);
                        continue;
                    }
                    
                    // 卖出记录：获得的SOL = 数量 * 价格
                    let sol_received = tx.amount * tx.price;
                    tx.sol_amount = Some(sol_received);
                    
                    // 采用先进先出（FIFO）原则匹配买入记录
                    let mut remaining_sell_amount = tx.amount;
                    let mut total_buy_cost = 0.0;
                    let mut matched_buys: Vec<String> = Vec::new();
                    
                    // 临时存储用于移除的索引
                    let mut to_remove_indices = Vec::new();
                    
                    for (i, (buy_tx, _)) in buy_records.iter().enumerate() {
                        if remaining_sell_amount <= 0.0 {
                            break;
                        }
                        
                        let buy_amount = buy_tx.amount;
                        let buy_price = buy_tx.price;
                        
                        // 确定这笔卖出要消耗多少买入量
                        let used_amount = buy_amount.min(remaining_sell_amount);
                        let used_cost = used_amount * buy_price;
                        
                        total_buy_cost += used_cost;
                        remaining_sell_amount -= used_amount;
                        
                        // 记录匹配的买入交易
                        matched_buys.push(buy_tx.signature.clone());
                        
                        // 如果买入记录被完全消耗，标记为移除
                        if used_amount >= buy_amount {
                            to_remove_indices.push(i);
                        }
                    }
                    
                    // 从最高索引开始移除，避免索引偏移
                    to_remove_indices.sort_by(|a, b| b.cmp(a));
                    for idx in to_remove_indices {
                        buy_records.remove(idx);
                    }
                    
                    // 计算盈亏
                    let profit_loss = sol_received - total_buy_cost;
                    let profit_loss_percentage = if total_buy_cost > 0.0 {
                        profit_loss / total_buy_cost * 100.0
                    } else {
                        0.0
                    };
                    
                    // 更新交易记录
                    tx.profit_loss = Some(profit_loss);
                    tx.profit_loss_percentage = Some(profit_loss_percentage);
                    tx.related_transactions = Some(matched_buys);
                },
                _ => {
                    // 其他类型交易不计算盈亏
                    tx.profit_loss = None;
                    tx.profit_loss_percentage = None;
                }
            }
        }
    }
}

// 计算交易历史统计，并更新钱包监控（用于API调用）
async fn calculate_transaction_statistics_with_monitoring(
    transactions: &mut Vec<TransactionInfo>,
    wallet_monitor: Option<&WalletMonitor>,
) {
    // 调用原有的统计计算
    calculate_transaction_statistics(transactions);
    
    // 如果提供了钱包监控服务，更新钱包亏损记录
    if let Some(monitor) = wallet_monitor {
        // 从已排序的交易中找出最近的卖出交易
        for tx in transactions.iter() {
            if tx.r#type == "sell" && tx.profit_loss.is_some() {
                // 从关联交易中提取钱包地址
                if let Some(ref related_txs) = tx.related_transactions {
                    if !related_txs.is_empty() {
                        // 仅处理有关联买入交易的卖出交易
                        let signature = &tx.signature;
                        let profit_loss = tx.profit_loss.unwrap_or(0.0);
                        
                        // 尝试从交易历史中获取买入交易的钱包地址
                        let buy_tx = transactions.iter()
                            .find(|t| related_txs.contains(&t.signature));
                        
                        if let Some(buy_tx) = buy_tx {
                            let wallet_address = extract_wallet_from_signature(&buy_tx.signature);
                            
                            if let Some(wallet_address) = wallet_address {
                                // 记录到钱包监控服务
                                if let Err(e) = monitor.record_sell_transaction(
                                    &wallet_address,
                                    signature,
                                    profit_loss
                                ).await {
                                    error!("记录钱包监控失败: {}", e);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 从交易签名中提取钱包地址 (实际应用中这里需要根据实际情况获取钱包地址)
fn extract_wallet_from_signature(signature: &str) -> Option<String> {
    // 这里是一个简化的示例，实际应用中可能需要查询交易详情获取钱包地址
    // 返回None或一个有效钱包地址
    None
}

// 获取交易历史
async fn get_transaction_history(
    State(state): State<Arc<ApiServerState>>,
    Query(params): Query<TransactionHistoryQuery>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理获取交易历史请求".to_string());
    
    // 设置默认分页参数
    let limit = params.limit.unwrap_or(10);
    let offset = params.offset.unwrap_or(0);
    let token_filter = params.token.clone();
    
    // 获取交易历史文件路径
    let history_dir = std::env::var("TRANSACTION_HISTORY_DIR").unwrap_or_else(|_| "data/tx_history".to_string());
    let history_path = std::path::Path::new(&history_dir).join("transactions.json");
    
    // 记录请求详情
    logger.log(format!(
        "获取交易历史，参数: limit={}, offset={}, token={}",
        limit,
        offset,
        token_filter.clone().unwrap_or_else(|| "所有代币".to_string())
    ));
    
    // 读取交易历史文件
    let mut transactions = if history_path.exists() {
        match fs::read_to_string(&history_path) {
            Ok(content) => {
                if content.trim().is_empty() {
                    Vec::new()
                } else {
                    // 使用更通用的方式读取历史记录
                    match serde_json::from_str::<serde_json::Value>(&content) {
                        Ok(json_value) => {
                            if let Some(json_array) = json_value.as_array() {
                                // 手动解析历史记录
                                let mut result = Vec::new();
                                for item in json_array {
                                    // 尝试构造TransactionInfo
                                    if let Ok(tx) = parse_transaction_item(item) {
                                        result.push(tx);
                                    } else {
                                        // 解析失败的条目跳过
                                        logger.warn(format!("无法解析交易历史条目: {:?}", item));
                                    }
                                }
                                result
                            } else {
                                logger.error("交易历史不是有效的JSON数组");
                                Vec::new()
                            }
                        },
                        Err(e) => {
                            logger.error(format!("解析交易历史失败: {}", e));
                            Vec::new()
                        }
                    }
                }
            },
            Err(e) => {
                logger.error(format!("读取交易历史文件失败: {}", e));
                Vec::new()
            }
        }
    } else {
        logger.debug(format!("交易历史文件不存在: {:?}", history_path));
        Vec::new()
    };
    
    // 计算每个代币的交易历史统计，同时更新钱包监控
    calculate_transaction_statistics_with_monitoring(&mut transactions, Some(&state.wallet_monitor)).await;
    
    // 应用代币过滤
    if let Some(token) = token_filter {
        transactions.retain(|tx| tx.token_address == token);
    }
    
    // 按时间戳倒序排序
    transactions.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    
    // 应用分页
    let total = transactions.len();
    
    // 创建包含格式化后数值的响应数据
    let formatted_transactions: Vec<serde_json::Value> = transactions.into_iter()
        .skip(offset)
        .take(limit)
        .map(|tx| {
            // 创建一个新的JSON对象，包含所有字段
            let mut tx_json = serde_json::json!({
                "signature": tx.signature,
                "type": tx.r#type,
                "token_address": tx.token_address,
                "amount": tx.amount,
                "price": tx.price,
                "timestamp": tx.timestamp,
                "status": tx.status,
                "profit_loss": tx.profit_loss,
                "profit_loss_percentage": tx.profit_loss_percentage,
                "related_transactions": tx.related_transactions,
                "sol_amount": tx.sol_amount,
                
                // 添加格式化的数值 - 使用自定义格式化函数
                "formatted_amount": tx.formatted_amount.unwrap_or_else(|| format_amount_custom(tx.amount)),
                "formatted_price": tx.formatted_price.unwrap_or_else(|| format_price_custom(tx.price)),
                "formatted_sol_amount": tx.sol_amount.map(|amount| format!("{:.4} SOL", amount))
            });
            
            tx_json
        })
        .collect();
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "transactions": formatted_transactions,
            "total": total,
            "offset": offset,
            "limit": limit
        }
    })))
}

// 手动解析交易历史条目
fn parse_transaction_item(item: &serde_json::Value) -> Result<TransactionInfo, anyhow::Error> {
    // 提取必需字段
    let signature = item.get("signature")
        .and_then(|v| v.as_str())
        .ok_or_else(|| anyhow!("无效的签名"))?
        .to_string();
    
    let tx_type = item.get("type")
        .and_then(|v| v.as_str())
        .ok_or_else(|| anyhow!("无效的交易类型"))?
        .to_string();
    
    let token_address = item.get("token_address")
        .and_then(|v| v.as_str())
        .ok_or_else(|| anyhow!("无效的代币地址"))?
        .to_string();
    
    let timestamp = item.get("timestamp")
        .and_then(|v| v.as_str())
        .ok_or_else(|| anyhow!("无效的时间戳"))?
        .to_string();
    
    let status = item.get("status")
        .and_then(|v| v.as_str())
        .ok_or_else(|| anyhow!("无效的状态"))?
        .to_string();
    
    // 提取可选字段
    let profit_loss = item.get("profit_loss")
        .and_then(|v| v.as_f64());
    
    let profit_loss_percentage = item.get("profit_loss_percentage")
        .and_then(|v| v.as_f64());
    
    let related_transactions = item.get("related_transactions")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect::<Vec<_>>()
        });
    
    // 处理格式化的金额和价格，可能是字符串或数字
    let mut amount: f64 = 0.0;
    let mut price: f64 = 0.0;
    let mut formatted_amount: Option<String> = None;
    let mut formatted_price: Option<String> = None;
    
    // 处理金额 - 可能是数字或字符串
    if let Some(amount_val) = item.get("amount") {
        if amount_val.is_f64() || amount_val.is_u64() {
            amount = amount_val.as_f64().unwrap_or(0.0);
        } else if amount_val.is_string() {
            // 如果是字符串，保存格式化的版本，并尝试从格式化字符串中提取数值
            let amount_str = amount_val.as_str().unwrap_or("0");
            formatted_amount = Some(amount_str.to_string());
            // 尝试解析数字部分，忽略单位后缀
            amount = amount_str.trim_matches(|c: char| !c.is_digit(10) && c != '.' && c != '-')
                .parse::<f64>().unwrap_or(0.0);
            
            // 如果有使用k、m、b等后缀，需要调整数值
            if amount_str.to_lowercase().contains('k') {
                amount *= 1_000.0;
            } else if amount_str.to_lowercase().contains('m') {
                amount *= 1_000_000.0;
            } else if amount_str.to_lowercase().contains('b') {
                amount *= 1_000_000_000.0;
            }
        }
    }
    
    // 处理价格 - 可能是数字或字符串
    if let Some(price_val) = item.get("price") {
        if price_val.is_f64() || price_val.is_u64() {
            price = price_val.as_f64().unwrap_or(0.0);
        } else if price_val.is_string() {
            // 如果是字符串，保存格式化的版本，并尝试提取数值
            let price_str = price_val.as_str().unwrap_or("0");
            formatted_price = Some(price_str.to_string());
            // 尝试解析数字部分
            price = price_str.trim_matches(|c: char| !c.is_digit(10) && c != '.' && c != '-')
                .parse::<f64>().unwrap_or(0.0);
        }
    }
    
    // 获取或计算SOL金额
    let sol_amount = item.get("sol_amount")
        .and_then(|v| v.as_f64())
        .or_else(|| {
            if tx_type == "buy" || tx_type == "sell" {
                // 如果没有显式的SOL金额，计算它
                Some(amount * price)
            } else {
                None
            }
        });
    
    // 构造结果
    let tx_info = TransactionInfo {
        signature,
        r#type: tx_type,
        token_address,
        amount,
        price,
        timestamp,
        status,
        profit_loss,
        profit_loss_percentage,
        related_transactions,
        sol_amount,
        formatted_amount,
        formatted_price,
    };
    
    Ok(tx_info)
}

// 获取所有监控地址
async fn get_monitor_addresses(
    State(state): State<Arc<ApiServerState>>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log("获取所有监控地址".to_string());
    
    // 使用解耦的内存缓存获取监控地址列表，而不是直接读取文件
    let monitor_config = config_monitor::get_cached_monitor_addresses().await;
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "成功获取监控地址",
        "data": monitor_config
    })))
}

// 更新所有监控地址
async fn update_all_monitor_addresses(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<MonitorAddresses>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log("更新所有监控地址".to_string());
    
    // 验证配置
    if payload.sol_address.is_empty() {
        return Err(ApiError::ValidationError("SOL地址不能为空".to_string()));
    }
    
    if payload.unwanted_key.is_empty() {
        return Err(ApiError::ValidationError("排除地址不能为空".to_string()));
    }
    
    // 更新配置
    if let Err(e) = config_monitor::update_monitor_addresses(payload).await {
        state.logger.error(format!("更新监控地址失败: {}", e));
        return Err(ApiError::InternalError("更新监控地址失败".to_string()));
    }
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "监控地址更新成功"
    })))
}

// 获取指定监控地址的配置
async fn get_monitor_address(
    State(state): State<Arc<ApiServerState>>,
    Path(address): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log(format!("获取监控地址配置: {}", address));
    
    // 使用解耦的内存缓存获取监控地址列表
    let monitor_config = config_monitor::get_cached_monitor_addresses().await;
    
    // 检查地址是否在监控列表中
    if !monitor_config.targets.contains(&address) {
        return Err(ApiError::NotFoundError(format!("地址 {} 不在监控列表中", address)));
    }
    
    // 获取钱包配置
    let wallet_config = monitor_config.wallets.get(&address).cloned();
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "成功获取监控地址配置",
        "data": {
            "address": address,
            "is_monitored": true,
            "config": wallet_config
        }
    })))
}

// 添加监控地址
async fn add_monitor_address(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<AddMonitorAddressRequest>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log(format!("添加监控地址: {}", payload.address));
    
    // 验证地址格式
    if let Err(e) = parse_pubkey(&payload.address) {
        return Err(e);
    }
    
    // 获取当前配置
    let mut monitor_config = config_monitor::get_monitor_addresses().await;
    
    // 检查地址是否已存在
    if monitor_config.targets.contains(&payload.address) {
        return Err(ApiError::DuplicateError(format!("地址 {} 已在监控列表中", payload.address)));
    }
    
    // 添加到监控列表
    monitor_config.targets.push(payload.address.clone());
    
    // 添加钱包配置
    monitor_config.wallets.insert(payload.address.clone(), MonitorWalletConfig {
        follow_percentage: payload.follow_percentage,
        slippage_percentage: payload.slippage_percentage,
        tip_percentage: payload.tip_percentage,
        min_price_multiplier: payload.min_price_multiplier,
        max_price_multiplier: payload.max_price_multiplier,
        priority_fee: payload.priority_fee,
        compute_unit_limit: payload.compute_unit_limit,
        note: None,  // 添加 note 字段，默认为 None
    });
    
    // 更新配置
    if let Err(e) = config_monitor::update_monitor_addresses(monitor_config).await {
        state.logger.error(format!("添加监控地址失败: {}", e));
        return Err(ApiError::InternalError("添加监控地址失败".to_string()));
    }
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": format!("成功添加监控地址: {}", payload.address)
    })))
}

// 更新监控地址
async fn update_monitor_address(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<UpdateMonitorAddressRequest>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log(format!("更新监控地址: {}", payload.address));
    
    // 验证地址格式
    if let Err(e) = parse_pubkey(&payload.address) {
        return Err(e);
    }
    
    // 获取当前配置
    let mut monitor_config = config_monitor::get_monitor_addresses().await;
    
    // 检查地址是否存在
    if !monitor_config.targets.contains(&payload.address) {
        return Err(ApiError::NotFoundError(format!("地址 {} 不在监控列表中", payload.address)));
    }
    
    // 更新钱包配置
    monitor_config.wallets.insert(payload.address.clone(), MonitorWalletConfig {
        follow_percentage: payload.follow_percentage,
        slippage_percentage: payload.slippage_percentage,
        tip_percentage: payload.tip_percentage,
        min_price_multiplier: payload.min_price_multiplier,
        max_price_multiplier: payload.max_price_multiplier,
        priority_fee: payload.priority_fee,
        compute_unit_limit: payload.compute_unit_limit,
        note: None,  // 添加 note 字段，默认为 None
    });
    
    // 更新配置
    if let Err(e) = config_monitor::update_monitor_addresses(monitor_config).await {
        state.logger.error(format!("更新监控地址失败: {}", e));
        return Err(ApiError::InternalError("更新监控地址失败".to_string()));
    }
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": format!("成功更新监控地址: {}", payload.address)
    })))
}

// 删除监控地址
async fn delete_monitor_address(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<DeleteMonitorAddressRequest>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log(format!("删除监控地址: {}", payload.address));
    
    // 获取当前配置
    let mut monitor_config = config_monitor::get_monitor_addresses().await;
    
    // 检查地址是否存在
    if !monitor_config.targets.contains(&payload.address) {
        return Err(ApiError::NotFoundError(format!("地址 {} 不在监控列表中", payload.address)));
    }
    
    // 从监控列表中移除
    monitor_config.targets.retain(|addr| addr != &payload.address);
    
    // 删除钱包配置
    monitor_config.wallets.remove(&payload.address);
    
    // 更新配置
    if let Err(e) = config_monitor::update_monitor_addresses(monitor_config).await {
        state.logger.error(format!("删除监控地址失败: {}", e));
        return Err(ApiError::InternalError("删除监控地址失败".to_string()));
    }
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": format!("成功删除监控地址: {}", payload.address)
    })))
}

// 辅助函数：解析公钥
fn parse_pubkey(pubkey_str: &str) -> Result<Pubkey, ApiError> {
    Pubkey::from_str(pubkey_str)
        .map_err(|_| ApiError::ValidationError(format!("无效的公钥格式: {}", pubkey_str)))
}

// 暂停跟单操作
async fn pause_follow() -> impl IntoResponse {
    PAUSE_FOLLOW.store(true, Ordering::Relaxed);
    info!("跟单功能已暂停");
    Json(json!({
        "success": true,
        "message": "已暂停跟单",
        "status": "paused"
    }))
}

// 恢复跟单操作
async fn resume_follow() -> impl IntoResponse {
    PAUSE_FOLLOW.store(false, Ordering::Relaxed);
    info!("跟单功能已恢复");
    Json(json!({
        "success": true,
        "message": "已恢复跟单",
        "status": "running"
    }))
}

// 获取跟单状态
async fn follow_status() -> impl IntoResponse {
    let is_paused = PAUSE_FOLLOW.load(Ordering::Relaxed);
    Json(json!({
        "success": true,
        "status": if is_paused { "paused" } else { "running" }
    }))
}

// 获取钱包监控配置
async fn get_wallet_monitor_config(
    State(state): State<Arc<ApiServerState>>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理获取钱包监控配置请求".to_string());
    
    let config = state.wallet_monitor.get_config().await;
    
    let response = serde_json::json!({
        "success": true,
        "data": config
    });
    
    Ok(Json(response))
}

// 钱包监控配置请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct WalletMonitorConfigRequest {
    #[validate(range(min = 1, max = 10, message = "连续亏损次数阈值必须在1-10之间"))]
    pub consecutive_loss_threshold: u32,
    
    #[validate(range(min = 0.01, max = 100.0, message = "亏损金额阈值必须在0.01-100.0之间"))]
    pub loss_amount_threshold: f64,
    
    pub auto_remove_wallet: bool,
}

// 更新钱包监控配置
async fn update_wallet_monitor_config(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<WalletMonitorConfigRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理更新钱包监控配置请求".to_string());
    
    // 验证请求参数
    if let Err(e) = payload.validate() {
        logger.error(format!("参数验证失败: {}", e));
        return Err(ApiError::ValidationError(e.to_string()));
    }
    
    let config = WalletMonitorConfig {
        consecutive_loss_threshold: payload.consecutive_loss_threshold,
        loss_amount_threshold: payload.loss_amount_threshold,
        auto_remove_wallet: payload.auto_remove_wallet,
    };
    
    if let Err(e) = state.wallet_monitor.update_config(config).await {
        logger.error(format!("更新钱包监控配置失败: {}", e));
        return Err(ApiError::InternalError("更新钱包监控配置失败".to_string()));
    }
    
    let response = serde_json::json!({
        "success": true,
        "message": "钱包监控配置更新成功"
    });
    
    Ok(Json(response))
}

// 获取所有钱包监控记录
async fn get_wallet_monitor_records(
    State(state): State<Arc<ApiServerState>>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug("处理获取所有钱包监控记录请求".to_string());
    
    let records = state.wallet_monitor.get_all_records().await;
    
    let response = serde_json::json!({
        "success": true,
        "data": records
    });
    
    Ok(Json(response))
}

// 获取指定钱包的监控记录
async fn get_wallet_monitor_record(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理获取钱包监控记录请求: {}", wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&wallet_address)?;
    
    let record = state.wallet_monitor.get_wallet_record(&wallet_address).await;
    
    if let Some(record) = record {
        let response = serde_json::json!({
            "success": true,
            "data": record
        });
        
        Ok(Json(response))
    } else {
        Err(ApiError::NotFoundError(format!("找不到钱包 {} 的监控记录", wallet_address)))
    }
}

// 重置钱包监控记录
async fn reset_wallet_monitor_record(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理重置钱包监控记录请求: {}", wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&wallet_address)?;
    
    match state.wallet_monitor.reset_wallet_record(&wallet_address).await {
        Ok(_) => {
            let response = serde_json::json!({
                "success": true,
                "message": format!("已重置钱包 {} 的监控记录", wallet_address)
            });
            
            Ok(Json(response))
        },
        Err(e) => {
            Err(ApiError::NotFoundError(format!("重置钱包监控记录失败: {}", e)))
        }
    }
}

// 删除钱包监控记录
async fn delete_wallet_monitor_record(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    let logger = &state.logger;
    logger.debug(format!("处理删除钱包监控记录请求: {}", wallet_address));
    
    // 验证钱包地址格式
    let _ = parse_pubkey(&wallet_address)?;
    
    match state.wallet_monitor.delete_wallet_record(&wallet_address).await {
        Ok(_) => {
            let response = serde_json::json!({
                "success": true,
                "message": format!("已删除钱包 {} 的监控记录", wallet_address)
            });
            
            Ok(Json(response))
        },
        Err(e) => {
            Err(ApiError::NotFoundError(format!("删除钱包监控记录失败: {}", e)))
        }
    }
}

// 格式化价格
fn format_price_custom(price: f64) -> String {
    if price == 0.0 {
        return "0.0".to_string();
    }
    
    // 对于非常小的数字，使用特殊格式显示
    if price < 0.0001 {
        let s = format!("{:.10}", price); // 保留10位小数确保精度
        if let Some(dot_pos) = s.find('.') {
            let decimals = &s[dot_pos+1..];
            let mut zero_count = 0;
            
            // 计算前导0的数量
            for c in decimals.chars() {
                if c == '0' {
                    zero_count += 1;
                } else {
                    break;
                }
            }
            
            // 取非零部分
            let non_zero_part = &decimals[zero_count..];
            // 去掉末尾的0
            let non_zero_part = non_zero_part.trim_end_matches('0');
            
            // 使用新格式：0.(零的个数)非零部分
            return format!("0.({}){}",  zero_count, non_zero_part);
        }
    }
    
    // 对于普通大小的数字，使用常规格式
    if price >= 1.0 {
        format!("{:.2}", price)
    } else if price >= 0.1 {
        format!("{:.3}", price)
    } else if price >= 0.01 {
        format!("{:.4}", price)
    } else if price >= 0.001 {
        format!("{:.5}", price)
    } else if price >= 0.0001 {
        format!("{:.6}", price)
    } else {
        format!("{:.8}", price) // 这个分支理论上不会再被执行到，因为小于0.0001的已经用特殊格式处理
    }
}

// 格式化数量
fn format_amount_custom(amount: f64) -> String {
    if amount >= 1_000_000.0 {
        format!("{:.2}M", amount / 1_000_000.0)
    } else if amount >= 1_000.0 {
        format!("{:.2}K", amount / 1_000.0)
    } else if amount >= 1.0 {
        format!("{:.2}", amount)
    } else if amount >= 0.1 {
        format!("{:.3}", amount)
    } else if amount >= 0.01 {
        format!("{:.4}", amount)
    } else if amount >= 0.001 {
        format!("{:.5}", amount)
    } else if amount >= 0.0001 {
        format!("{:.6}", amount)
    } else if amount >= 0.00001 {
        format!("{:.7}", amount)
    } else if amount >= 0.000001 {
        format!("{:.8}", amount)
    } else {
        format!("{:.9}", amount)
    }
}

// 特殊钱包配置请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct SpecialWalletConfigRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub wallet_address: String,
    
    #[validate(range(min = 0.0, max = 100.0, message = "滑点百分比必须在0-100之间"))]
    pub slippage_percentage: f64,
    
    #[validate(range(min = 0.0, max = 100.0, message = "小费百分比必须在0-100之间"))]
    pub tip_percentage: f64,
    
    #[validate(range(min = 0.0, max = 20.0, message = "优先费倍数必须在0-20之间"))]
    pub priority_fee_multiplier: f64,
    
    #[validate(range(min = 1000, max = 1400000, message = "计算单元限制必须在1000-1400000之间"))]
    pub compute_limit: u64,
    
    pub note: Option<String>,
}

// 获取所有特殊钱包配置
async fn get_special_wallet_configs(
    State(_state): State<Arc<ApiServerState>>
) -> Result<impl IntoResponse, ApiError> {
    let configs = SpecialWalletConfigs::load()
        .map_err(|e| ApiError::InternalError(format!("加载特殊钱包配置失败: {}", e)))?;
    
    Ok(Json(serde_json::json!({
        "success": true,
        "data": configs
    })))
}

// 获取单个特殊钱包配置
async fn get_special_wallet_config(
    State(_state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>
) -> Result<impl IntoResponse, ApiError> {
    let configs = SpecialWalletConfigs::load()
        .map_err(|e| ApiError::InternalError(format!("加载特殊钱包配置失败: {}", e)))?;
    
    if let Some(config) = configs.get_wallet_config(&wallet_address) {
        Ok(Json(serde_json::json!({
            "success": true,
            "data": config
        })))
    } else {
        Err(ApiError::NotFoundError(format!("未找到特殊钱包配置: {}", wallet_address)))
    }
}

// 添加特殊钱包配置
async fn add_special_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<SpecialWalletConfigRequest>
) -> Result<impl IntoResponse, ApiError> {
    // 验证参数
    if let Err(e) = payload.validate() {
        return Err(ApiError::ValidationError(e.to_string()));
    }
    
    // 解析钱包地址
    if Pubkey::from_str(&payload.wallet_address).is_err() {
        return Err(ApiError::ValidationError("钱包地址格式无效".to_string()));
    }
    
    let mut configs = SpecialWalletConfigs::load()
        .map_err(|e| ApiError::InternalError(format!("加载特殊钱包配置失败: {}", e)))?;
    
    // 创建新的配置
    let new_config = SpecialWalletConfig {
        slippage_percentage: payload.slippage_percentage,
        tip_percentage: payload.tip_percentage,
        priority_fee_multiplier: payload.priority_fee_multiplier,
        compute_limit: payload.compute_limit,
        note: payload.note,
    };
    
    // 添加配置
    configs.add_wallet_config(payload.wallet_address.clone(), new_config)
        .map_err(|e| ApiError::InternalError(format!("保存特殊钱包配置失败: {}", e)))?;
    
    state.logger.log(format!("添加特殊钱包配置: {}", payload.wallet_address));
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "特殊钱包配置已添加"
    })))
}

// 更新特殊钱包配置
async fn update_special_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>,
    Json(payload): Json<SpecialWalletConfigRequest>
) -> Result<impl IntoResponse, ApiError> {
    // 验证参数
    if let Err(e) = payload.validate() {
        return Err(ApiError::ValidationError(e.to_string()));
    }
    
    // 验证路径参数和请求体中的钱包地址是否一致
    if wallet_address != payload.wallet_address {
        return Err(ApiError::ValidationError("路径参数中的钱包地址与请求体中的不一致".to_string()));
    }
    
    let mut configs = SpecialWalletConfigs::load()
        .map_err(|e| ApiError::InternalError(format!("加载特殊钱包配置失败: {}", e)))?;
    
    // 检查配置是否存在
    if configs.get_wallet_config(&wallet_address).is_none() {
        return Err(ApiError::NotFoundError(format!("未找到特殊钱包配置: {}", wallet_address)));
    }
    
    // 创建更新后的配置
    let updated_config = SpecialWalletConfig {
        slippage_percentage: payload.slippage_percentage,
        tip_percentage: payload.tip_percentage,
        priority_fee_multiplier: payload.priority_fee_multiplier,
        compute_limit: payload.compute_limit,
        note: payload.note,
    };
    
    // 更新配置
    configs.add_wallet_config(wallet_address.clone(), updated_config)
        .map_err(|e| ApiError::InternalError(format!("保存特殊钱包配置失败: {}", e)))?;
    
    state.logger.log(format!("更新特殊钱包配置: {}", wallet_address));
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "特殊钱包配置已更新"
    })))
}

// 删除特殊钱包配置
async fn delete_special_wallet_config(
    State(state): State<Arc<ApiServerState>>,
    Path(wallet_address): Path<String>
) -> Result<impl IntoResponse, ApiError> {
    let mut configs = SpecialWalletConfigs::load()
        .map_err(|e| ApiError::InternalError(format!("加载特殊钱包配置失败: {}", e)))?;
    
    // 删除配置
    let removed = configs.remove_wallet_config(&wallet_address)
        .map_err(|e| ApiError::InternalError(format!("删除特殊钱包配置失败: {}", e)))?;
    
    if !removed {
        return Err(ApiError::NotFoundError(format!("未找到特殊钱包配置: {}", wallet_address)));
    }
    
    state.logger.log(format!("删除特殊钱包配置: {}", wallet_address));
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "特殊钱包配置已删除"
    })))
}

// 添加更新备注请求结构体
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateMonitorNoteRequest {
    #[validate(length(min = 32, max = 44, message = "钱包地址长度无效"))]
    pub address: String,
    pub note: String,
}

// 添加更新备注的API函数
async fn update_monitor_note(
    State(state): State<Arc<ApiServerState>>,
    Json(payload): Json<UpdateMonitorNoteRequest>,
) -> Result<impl IntoResponse, ApiError> {
    state.logger.log(format!("更新钱包备注: {}", payload.address));
    
    // 验证地址格式
    if let Err(e) = parse_pubkey(&payload.address) {
        return Err(e);
    }
    
    // 获取当前配置
    let mut monitor_config = config_monitor::get_monitor_addresses().await;
    
    // 检查地址是否存在
    if !monitor_config.targets.contains(&payload.address) {
        return Err(ApiError::NotFoundError(format!("地址 {} 不在监控列表中", payload.address)));
    }
    
    // 更新钱包配置
    if let Some(wallet_config) = monitor_config.wallets.get_mut(&payload.address) {
        // 在钱包配置中添加备注字段
        wallet_config.note = Some(payload.note);
    } else {
        // 如果钱包配置不存在，创建一个新的配置
        monitor_config.wallets.insert(payload.address.clone(), MonitorWalletConfig {
            follow_percentage: 30.0, // 默认跟单比例
            slippage_percentage: 2.0, // 默认滑点
            tip_percentage: 1.0,     // 默认小费
            min_price_multiplier: 0.00000001, // 默认最小价格
            max_price_multiplier: 999999999.0, // 默认最大价格
            priority_fee: Some(50000), // 默认优先费
            compute_unit_limit: Some(200000), // 默认计算单元限制
            note: Some(payload.note),  // 设置备注
        });
    }
    
    // 更新配置
    if let Err(e) = config_monitor::update_monitor_addresses(monitor_config).await {
        state.logger.error(format!("更新钱包备注失败: {}", e));
        return Err(ApiError::InternalError("更新钱包备注失败".to_string()));
    }
    
    Ok(Json(serde_json::json!({
        "success": true,
        "message": format!("成功更新钱包备注: {}", payload.address)
    })))
}