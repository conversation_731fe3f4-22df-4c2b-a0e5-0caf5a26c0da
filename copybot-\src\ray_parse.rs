use std::sync::Arc;

use anyhow::{Result, anyhow};
use solana_sdk::{pubkey::Pubkey, signature::Keypair};
use serde_json::Value;
use std::env;

// Raydium交易解析相关功能
pub struct RayTxParser {
    // 添加必要的字段
}

impl RayTxParser {
    pub fn new() -> Self {
        Self {
            // 初始化字段
        }
    }
    
    // 解析Raydium交易
    pub async fn parse_transaction(
        &self,
        _tx_data: Vec<u8>,
    ) -> Result<()> {
        // 实现交易解析逻辑
        todo!("Implement Raydium transaction parsing")
    }
}

// 添加tx_parse模块
pub mod tx_parse {
    use anyhow::{Result, anyhow};
    use solana_sdk::pubkey::Pubkey;
    use serde_json::Value;
    use std::env;
    use crate::common::utils::ParseTx;

    // 解析交易的函数
    pub async fn tx_parse(
        tx_data: &Value,
        target_wallet: &Pubkey,
    ) -> Result<ParseTx> {
        // 初始化解析结果
        let mut parse_tx = ParseTx {
            type_tx: String::new(),
            direction: None,
            amount_in: 0.0,
            amount_out: 0.0,
            mint: String::new(),
            price: None, // 初始化价格字段为None
        };
        
        // 获取交易元数据和指令
        let meta = tx_data.get("meta").ok_or_else(|| anyhow!("交易缺少meta字段"))?;
        
        // 打印日志消息以帮助调试
        println!("尝试解析交易数据...");
        
        // 检查日志消息中是否包含"Instruction: Sell"或"Instruction: Buy"
        if let Some(log_messages) = meta.get("logMessages").and_then(|l| l.as_array()) {
            for log in log_messages {
                if let Some(log_str) = log.as_str() {
                    if log_str.contains("Instruction: Sell") {
                        parse_tx.type_tx = "pump_fun".to_string();
                        parse_tx.direction = Some("sell".to_string());
                        println!("检测到Sell指令");
                        break;
                    } else if log_str.contains("Instruction: Buy") {
                        parse_tx.type_tx = "pump_fun".to_string();
                        parse_tx.direction = Some("buy".to_string());
                        println!("检测到Buy指令");
                        break;
                    }
                }
            }
        }
        
        // 检查是否存在Pump.fun程序ID
        let pump_fun_id = env::var("PUMP_FUN_PROGRAM_ID")
            .unwrap_or_else(|_| "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string());
            
        // 如果从日志中没有识别出类型，尝试从指令中识别
        if parse_tx.type_tx.is_empty() {
            let inner_instructions = meta.get("innerInstructions").and_then(|i| i.as_array());
            let message = tx_data.get("transaction").and_then(|t| t.get("message"));
            let instructions = message.and_then(|m| m.get("instructions")).and_then(|i| i.as_array());
            
            // 检查主指令中是否有Pump.fun调用
            if let Some(insts) = instructions {
                for inst in insts {
                    if let Some(program_id) = inst.get("programId").and_then(|p| p.as_str()) {
                        if program_id == pump_fun_id {
                            parse_tx.type_tx = "pump_fun".to_string();
                            println!("从主指令中检测到Pump.fun程序");
                            break;
                        }
                    }
                }
            }
            
            // 如果主指令没有Pump.fun调用，检查内部指令
            if parse_tx.type_tx.is_empty() && inner_instructions.is_some() {
                for inst_group in inner_instructions.unwrap() {
                    if let Some(insts) = inst_group.get("instructions").and_then(|i| i.as_array()) {
                        for inst in insts {
                            if let Some(program_id) = inst.get("programId").and_then(|p| p.as_str()) {
                                if program_id == pump_fun_id {
                                    parse_tx.type_tx = "pump_fun".to_string();
                                    println!("从内部指令中检测到Pump.fun程序");
                                    break;
                                }
                            }
                        }
                        if !parse_tx.type_tx.is_empty() {
                            break;
                        }
                    }
                }
            }
        }
        
        // 从代币余额变化中提取代币mint和金额
        if let (Some(pre_balances), Some(post_balances)) = (
            meta.get("preTokenBalances").and_then(|b| b.as_array()),
            meta.get("postTokenBalances").and_then(|b| b.as_array())
        ) {
            println!("找到代币余额变化信息");
            
            // 尝试找到mint地址
            for pre_balance in pre_balances {
                if let Some(mint) = pre_balance.get("mint").and_then(|m| m.as_str()) {
                    parse_tx.mint = mint.to_string();
                    println!("找到代币mint: {}", mint);
                    break;
                }
            }
            
            // 计算代币数量变化
            for pre_balance in pre_balances {
                let account_index = pre_balance.get("accountIndex").and_then(|i| i.as_u64());
                let owner = pre_balance.get("owner").and_then(|o| o.as_str());
                let mint = pre_balance.get("mint").and_then(|m| m.as_str());
                
                // 如果找到了目标钱包
                if let Some(owner_str) = owner {
                    if owner_str == target_wallet.to_string() {
                        let pre_amount = pre_balance.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()).unwrap_or(0.0);
                        
                        // 查找相同账户索引在交易后的余额
                        let post_amount = if let Some(idx) = account_index {
                            let post_balance = post_balances.iter()
                                .find(|b| b.get("accountIndex").and_then(|i| i.as_u64()) == Some(idx));
                                
                            if let Some(post) = post_balance {
                                post.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()).unwrap_or(0.0)
                            } else {
                                0.0
                            }
                        } else {
                            0.0
                        };
                        
                        // 目标钱包的代币减少，是卖出
                        if pre_amount > post_amount && (pre_amount - post_amount) > 0.0001 {
                            parse_tx.direction = Some("sell".to_string());
                            parse_tx.amount_in = pre_amount - post_amount;
                            if let Some(m) = mint {
                                parse_tx.mint = m.to_string();
                            }
                            println!("目标钱包卖出代币: {} -> {}, 差额: {}", pre_amount, post_amount, parse_tx.amount_in);
                        }
                        // 目标钱包的代币增加，是买入
                        else if post_amount > pre_amount && (post_amount - pre_amount) > 0.0001 {
                            parse_tx.direction = Some("buy".to_string());
                            parse_tx.amount_out = post_amount - pre_amount;
                            if let Some(m) = mint {
                                parse_tx.mint = m.to_string();
                            }
                            println!("目标钱包买入代币: {} -> {}, 差额: {}", pre_amount, post_amount, parse_tx.amount_out);
                        }
                    }
                }
            }
            
            // 如果找不到目标钱包的代币变化，检查所有代币变化
            if parse_tx.direction.is_none() {
                for pre_balance in pre_balances {
                    let account_index = pre_balance.get("accountIndex").and_then(|i| i.as_u64());
                    let mint = pre_balance.get("mint").and_then(|m| m.as_str());
                    let pre_amount = pre_balance.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()).unwrap_or(0.0);
                    
                    // 查找相同账户索引在交易后的余额
                    let post_amount = if let Some(idx) = account_index {
                        let post_balance = post_balances.iter()
                            .find(|b| b.get("accountIndex").and_then(|i| i.as_u64()) == Some(idx));
                            
                        if let Some(post) = post_balance {
                            post.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()).unwrap_or(0.0)
                        } else {
                            0.0
                        }
                    } else {
                        0.0
                    };
                    
                    // 如果代币数量有明显变化
                    if (pre_amount - post_amount).abs() > 0.0001 {
                        // 假设这是代币发送，判断为卖出
                        if pre_amount > post_amount {
                            parse_tx.direction = Some("sell".to_string());
                            parse_tx.amount_in = pre_amount - post_amount;
                            if let Some(m) = mint {
                                parse_tx.mint = m.to_string();
                            }
                            println!("账户卖出代币: {} -> {}, 差额: {}", pre_amount, post_amount, parse_tx.amount_in);
                        }
                        // 假设这是代币接收，判断为买入
                        else {
                            parse_tx.direction = Some("buy".to_string());
                            parse_tx.amount_out = post_amount - pre_amount;
                            if let Some(m) = mint {
                                parse_tx.mint = m.to_string();
                            }
                            println!("账户买入代币: {} -> {}, 差额: {}", pre_amount, post_amount, parse_tx.amount_out);
                        }
                        break;
                    }
                }
            }
        }
        
        // 如果还是无法确定方向或金额，尝试从innerInstructions中的转账指令获取
        if parse_tx.direction.is_none() || (parse_tx.amount_in == 0.0 && parse_tx.amount_out == 0.0) {
            if let Some(inner_instructions) = meta.get("innerInstructions").and_then(|i| i.as_array()) {
                for inst_group in inner_instructions {
                    if let Some(insts) = inst_group.get("instructions").and_then(|i| i.as_array()) {
                        for inst in insts {
                            // 检查是否是SPL Token转账
                            if inst.get("programId").and_then(|p| p.as_str()) == Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") {
                                // 尝试从parsed字段获取
                                if let Some(parsed) = inst.get("parsed") {
                                    if parsed.get("type").and_then(|t| t.as_str()) == Some("transfer") {
                                        if let Some(info) = parsed.get("info") {
                                            let source = info.get("source").and_then(|s| s.as_str()).unwrap_or("");
                                            let destination = info.get("destination").and_then(|d| d.as_str()).unwrap_or("");
                                            let amount_str = info.get("amount").and_then(|a| a.as_str()).unwrap_or("0");
                                            
                                            // 解析金额
                                            if let Ok(amount) = amount_str.parse::<u64>() {
                                                let amount_f64 = amount as f64 / 1_000_000.0; // 假设代币精度为6
                                                
                                                // 判断交易方向
                                                if source == target_wallet.to_string() {
                                                    parse_tx.direction = Some("sell".to_string());
                                                    parse_tx.amount_in = amount_f64;
                                                    println!("从SPL转账指令检测到卖出: {}", amount_f64);
                                                } else if destination == target_wallet.to_string() {
                                                    parse_tx.direction = Some("buy".to_string());
                                                    parse_tx.amount_out = amount_f64;
                                                    println!("从SPL转账指令检测到买入: {}", amount_f64);
                                                } else {
                                                    // 如果没有直接与目标钱包相关，但需要确定金额
                                                    if parse_tx.direction.is_some() && parse_tx.amount_in == 0.0 && parse_tx.amount_out == 0.0 {
                                                        if parse_tx.direction == Some("sell".to_string()) {
                                                            parse_tx.amount_in = amount_f64;
                                                            println!("根据已知方向，设置卖出金额: {}", amount_f64);
                                                        } else {
                                                            parse_tx.amount_out = amount_f64;
                                                            println!("根据已知方向，设置买入金额: {}", amount_f64);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    // 尝试从accounts和data字段获取
                                    if let Some(accounts) = inst.get("accounts").and_then(|a| a.as_array()) {
                                        if accounts.len() >= 3 {
                                            let source = accounts.get(0).and_then(|a| a.as_str()).unwrap_or("");
                                            let destination = accounts.get(1).and_then(|a| a.as_str()).unwrap_or("");
                                            
                                            // 判断交易方向
                                            if source == target_wallet.to_string() {
                                                parse_tx.direction = Some("sell".to_string());
                                                println!("从SPL账户列表检测到卖出");
                                            } else if destination == target_wallet.to_string() {
                                                parse_tx.direction = Some("buy".to_string());
                                                println!("从SPL账户列表检测到买入");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 如果类型为空但找到了方向，设置为pump_fun
        if parse_tx.type_tx.is_empty() && parse_tx.direction.is_some() {
            parse_tx.type_tx = "pump_fun".to_string();
        }
        
        // 如果已经找到mint和方向，但金额仍为0，尝试从preTokenBalances和postTokenBalances重新计算
        if !parse_tx.mint.is_empty() && parse_tx.direction.is_some() && parse_tx.amount_in == 0.0 && parse_tx.amount_out == 0.0 {
            if let (Some(pre_balances), Some(post_balances)) = (
                meta.get("preTokenBalances").and_then(|b| b.as_array()),
                meta.get("postTokenBalances").and_then(|b| b.as_array())
            ) {
                for pre_balance in pre_balances {
                    if pre_balance.get("mint").and_then(|m| m.as_str()) == Some(&parse_tx.mint) {
                        let pre_amount = pre_balance.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()).unwrap_or(0.0);
                        
                        // 找到交易后的余额
                        let account_index = pre_balance.get("accountIndex").and_then(|i| i.as_u64());
                        let post_amount = if let Some(idx) = account_index {
                            post_balances.iter()
                                .find(|b| b.get("accountIndex").and_then(|i| i.as_u64()) == Some(idx))
                                .and_then(|b| b.get("uiTokenAmount").and_then(|u| u.get("uiAmount")).and_then(|a| a.as_f64()))
                                .unwrap_or(0.0)
                        } else {
                            0.0
                        };
                        
                        // 如果代币减少，认为是卖出
                        if pre_amount > post_amount {
                            if parse_tx.direction == Some("sell".to_string()) {
                                parse_tx.amount_in = pre_amount - post_amount;
                                println!("重新计算卖出金额: {}", parse_tx.amount_in);
                            }
                        }
                        // 如果代币增加，认为是买入
                        else if post_amount > pre_amount {
                            if parse_tx.direction == Some("buy".to_string()) {
                                parse_tx.amount_out = post_amount - pre_amount;
                                println!("重新计算买入金额: {}", parse_tx.amount_out);
                            }
                        }
                    }
                }
            }
        }
        
        // 输出最终解析结果
        println!("最终解析结果: type_tx={}, direction={:?}, amount_in={}, amount_out={}, mint={}",
            parse_tx.type_tx, parse_tx.direction, parse_tx.amount_in, parse_tx.amount_out, parse_tx.mint);
        
        // 计算价格
        if let Some(direction) = &parse_tx.direction {
            if parse_tx.amount_in > 0.0 || parse_tx.amount_out > 0.0 {
                // 尝试从inner instructions中解析转账信息
                let mut sol_transfer_amount = 0.0;
                let mut token_transfer_amount = 0.0;
                
                // 获取钱包索引，用于判断转账方向
                let target_pubkey_str = target_wallet.to_string();
                
                // 从inner instructions中寻找SOL转账和代币转账
                if let Some(inner_instructions) = meta.get("innerInstructions").and_then(|i| i.as_array()) {
                    println!("解析内部指令中的转账信息...");
                    
                    for inst_group in inner_instructions {
                        if let Some(insts) = inst_group.get("instructions").and_then(|i| i.as_array()) {
                            for inst in insts {
                                // 检查System Program的SOL转账
                                if inst.get("programId").and_then(|p| p.as_str()) == Some("********************************") {
                                    if let Some(parsed) = inst.get("parsed") {
                                        if parsed.get("type").and_then(|t| t.as_str()) == Some("transfer") {
                                            if let Some(info) = parsed.get("info") {
                                                let source = info.get("source").and_then(|s| s.as_str()).unwrap_or("");
                                                let destination = info.get("destination").and_then(|d| d.as_str()).unwrap_or("");
                                                let lamports = info.get("lamports").and_then(|l| l.as_u64()).unwrap_or(0);
                                                
                                                // 转换为SOL
                                                let sol_amount = lamports as f64 / 1_000_000_000.0;
                                                
                                                // 判断转账方向
                                                if source == target_pubkey_str {
                                                    // 钱包发送SOL，是买入操作的支付
                                                    println!("检测到SOL转出: {} -> {}, 金额: {} SOL", source, destination, sol_amount);
                                                    sol_transfer_amount = sol_amount;
                                                } else if destination == target_pubkey_str {
                                                    // 钱包接收SOL，是卖出操作的收款
                                                    println!("检测到SOL转入: {} -> {}, 金额: {} SOL", source, destination, sol_amount);
                                                    sol_transfer_amount = sol_amount;
                                                }
                                            }
                                        }
                                    }
                                }
                                // 检查SPL Token Program的代币转账
                                else if inst.get("programId").and_then(|p| p.as_str()) == Some("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") {
                                    if let Some(parsed) = inst.get("parsed") {
                                        if parsed.get("type").and_then(|t| t.as_str()) == Some("transfer") ||
                                           parsed.get("type").and_then(|t| t.as_str()) == Some("transferChecked") {
                                            if let Some(info) = parsed.get("info") {
                                                let source = info.get("source").and_then(|s| s.as_str()).unwrap_or("");
                                                let destination = info.get("destination").and_then(|d| d.as_str()).unwrap_or("");
                                                let amount_str = info.get("amount").and_then(|a| a.as_str()).unwrap_or("0");
                                                let token_decimals = info.get("tokenAmount").and_then(|t| t.get("decimals")).and_then(|d| d.as_u64()).unwrap_or(6);
                                                
                                                // 解析金额
                                                if let Ok(amount) = amount_str.parse::<u64>() {
                                                    let divisor = 10_u64.pow(token_decimals as u32) as f64;
                                                    let token_amount = amount as f64 / divisor;
                                                    
                                                    // 如果源地址或目标地址是ATA账户，需要解析其所有者
                                                    // 这里简化处理，直接比较源地址和目标地址，因为完整解析ATA所有者需要额外查询
                                                    if source == target_pubkey_str || source.contains(&target_pubkey_str) {
                                                        // 钱包发送代币，是卖出操作
                                                        if direction == "sell" {
                                                            println!("检测到代币转出: {} -> {}, 金额: {} tokens", source, destination, token_amount);
                                                            token_transfer_amount = token_amount;
                                                        }
                                                    } else if destination == target_pubkey_str || destination.contains(&target_pubkey_str) {
                                                        // 钱包接收代币，是买入操作
                                                        if direction == "buy" {
                                                            println!("检测到代币转入: {} -> {}, 金额: {} tokens", source, destination, token_amount);
                                                            token_transfer_amount = token_amount;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } else if let Some(data) = inst.get("data").and_then(|d| d.as_str()) {
                                        // 如果没有parsed字段，尝试从原始数据解析
                                        // 这里需要根据SPL Token指令的格式解析data字段
                                        // 简化处理：如果已经从metadata获取了代币数量，就不再从这里解析
                                        if token_transfer_amount == 0.0 {
                                            println!("检测到SPL Token原始指令，但无法解析");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 从accountKeys中找到target_wallet的索引位置（作为备用方法）
                let message = tx_data.get("transaction").and_then(|t| t.get("message"));
                let wallet_index = if let Some(account_keys) = message.and_then(|m| m.get("accountKeys")).and_then(|a| a.as_array()) {
                    account_keys.iter().position(|key| 
                        key.as_str() == Some(&target_wallet.to_string())
                    )
                } else {
                    None
                };
                
                // 根据钱包索引获取SOL余额变化（作为备用方法）
                let mut sol_change = 0.0;
                if sol_transfer_amount == 0.0 {
                    if let Some(idx) = wallet_index {
                        let pre_sol_balance = meta.get("preBalances").and_then(|b| b.as_array())
                            .and_then(|arr| arr.get(idx)).and_then(|v| v.as_u64()).unwrap_or(0);
                        let post_sol_balance = meta.get("postBalances").and_then(|b| b.as_array())
                            .and_then(|arr| arr.get(idx)).and_then(|v| v.as_u64()).unwrap_or(0);
                        
                        // 计算SOL变化，正值表示增加，负值表示减少
                        let sol_diff = (post_sol_balance as i128) - (pre_sol_balance as i128);
                        sol_change = (sol_diff as f64) / 1_000_000_000.0; // 转换为SOL单位
                        
                        println!("从余额变化检测到SOL变动: {} -> {}, 差额: {} SOL",
                            pre_sol_balance as f64 / 1_000_000_000.0,
                            post_sol_balance as f64 / 1_000_000_000.0,
                            sol_change);
                    }
                }
                
                // 如果通过inner instructions获取到了转账金额，优先使用
                if direction == "buy" && sol_transfer_amount > 0.0 {
                    parse_tx.amount_in = sol_transfer_amount;
                    println!("从内部指令设置买入SOL金额: {}", sol_transfer_amount);
                } else if direction == "buy" && sol_change < 0.0 {
                    // 备用方法：从余额变化设置
                    parse_tx.amount_in = -sol_change;
                    println!("从余额变化设置买入SOL金额: {}", -sol_change);
                }
                
                // 如果通过inner instructions获取到了代币转账金额，更新amount_in或amount_out
                if token_transfer_amount > 0.0 {
                    if direction == "buy" {
                        // 如果parse_tx.amount_out已经有值，但不等于token_transfer_amount，打印警告
                        if parse_tx.amount_out > 0.0 && (parse_tx.amount_out - token_transfer_amount).abs() > 0.001 {
                            println!("警告：买入代币数量不匹配，元数据: {}，转账指令: {}", parse_tx.amount_out, token_transfer_amount);
                        }
                        parse_tx.amount_out = token_transfer_amount;
                        println!("从内部指令设置买入代币数量: {}", token_transfer_amount);
                    } else if direction == "sell" {
                        // 如果parse_tx.amount_in已经有值，但不等于token_transfer_amount，打印警告
                        if parse_tx.amount_in > 0.0 && (parse_tx.amount_in - token_transfer_amount).abs() > 0.001 {
                            println!("警告：卖出代币数量不匹配，元数据: {}，转账指令: {}", parse_tx.amount_in, token_transfer_amount);
                        }
                        parse_tx.amount_in = token_transfer_amount;
                        println!("从内部指令设置卖出代币数量: {}", token_transfer_amount);
                    }
                }
                
                // 根据方向和获取的数据计算价格
                let price = match direction.as_str() {
                    "buy" => {
                        // 买入：SOL支出 / 代币获得量
                        if parse_tx.amount_out > 0.0 && parse_tx.amount_in > 0.0 {
                            Some(parse_tx.amount_in / parse_tx.amount_out)
                        } else if parse_tx.amount_out > 0.0 && sol_transfer_amount > 0.0 {
                            Some(sol_transfer_amount / parse_tx.amount_out)
                        } else if parse_tx.amount_out > 0.0 && sol_change < 0.0 {
                            Some(-sol_change / parse_tx.amount_out)
                        } else {
                            None
                        }
                    },
                    "sell" => {
                        // 卖出：SOL获得量 / 代币支出量
                        if parse_tx.amount_in > 0.0 && sol_transfer_amount > 0.0 {
                            Some(sol_transfer_amount / parse_tx.amount_in)
                        } else if parse_tx.amount_in > 0.0 && sol_change > 0.0 {
                            Some(sol_change / parse_tx.amount_in)
                        } else if parse_tx.amount_in > 0.0 && parse_tx.amount_out > 0.0 {
                            Some(parse_tx.amount_out / parse_tx.amount_in)
                        } else {
                            None
                        }
                    },
                    _ => None,
                };
                
                if let Some(p) = price {
                    parse_tx.price = Some(p);
                    println!("推算成交价：{} SOL/token", p);
                }
            }
        }
        
        Ok(parse_tx)
    }
} 