<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卖出交易状态WebSocket客户端示例</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 25px;
        }
        .control-panel {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .status-panel {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-connected {
            background-color: #2ecc71;
        }
        .status-disconnected {
            background-color: #e74c3c;
        }
        #status-logs {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .log-entry-time {
            color: #7f8c8d;
            font-size: 12px;
        }
        .log-entry-status-submitted {
            color: #3498db;
        }
        .log-entry-status-processing {
            color: #f39c12;
        }
        .log-entry-status-confirming {
            color: #9b59b6;
        }
        .log-entry-status-completed {
            color: #2ecc71;
        }
        .log-entry-status-failed {
            color: #e74c3c;
        }
        .tx-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        .tx-details.visible {
            display: block;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table td, table th {
            padding: 8px;
            border: 1px solid #ddd;
        }
        table th {
            background-color: #f2f2f2;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卖出交易状态WebSocket客户端示例</h1>
        
        <div class="control-panel">
            <h2>WebSocket连接</h2>
            <div class="form-group">
                <label for="ws-url">WebSocket服务器地址</label>
                <input type="text" id="ws-url" value="ws://localhost:3001" />
            </div>
            <div class="form-group">
                <button id="connect-btn">连接</button>
                <button id="disconnect-btn" disabled>断开连接</button>
            </div>
        </div>
        
        <div class="control-panel">
            <h2>卖出API调用</h2>
            <div class="form-group">
                <label for="api-url">API服务器地址</label>
                <input type="text" id="api-url" value="http://localhost:3000/api/sell" />
            </div>
            <div class="form-group">
                <label for="token-address">代币地址</label>
                <input type="text" id="token-address" value="9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump" />
            </div>
            <div class="form-group">
                <label for="percentage">卖出百分比</label>
                <select id="percentage">
                    <option value="25">25%</option>
                    <option value="50">50%</option>
                    <option value="75">75%</option>
                    <option value="100">100%</option>
                </select>
            </div>
            <div class="form-group">
                <label for="slippage-bps">滑点 (基点)</label>
                <input type="number" id="slippage-bps" value="50" />
            </div>
            <div class="form-group">
                <label for="priority-fee">优先费 (微lamports)</label>
                <input type="number" id="priority-fee" value="20000" />
            </div>
            <div class="form-group">
                <label for="tip-fixed">固定小费 (SOL)</label>
                <input type="number" id="tip-fixed" value="0.001" step="0.0001" />
            </div>
            <div class="form-group">
                <button id="sell-btn" disabled>执行卖出</button>
            </div>
        </div>
        
        <div class="status-panel">
            <div class="status-header">
                <h2>状态日志</h2>
                <div>
                    <span class="status-indicator status-disconnected" id="status-indicator"></span>
                    <span id="connection-status">未连接</span>
                </div>
            </div>
            <div id="status-logs"></div>
        </div>
        
        <div class="tx-details" id="tx-details">
            <h2>最新交易详情</h2>
            <table>
                <tr>
                    <th>字段</th>
                    <th>值</th>
                </tr>
                <tbody id="tx-details-content">
                    <!-- 动态填充内容 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // 全局变量
        let ws = null;
        let statusLogs = document.getElementById('status-logs');
        let statusIndicator = document.getElementById('status-indicator');
        let connectionStatus = document.getElementById('connection-status');
        let connectBtn = document.getElementById('connect-btn');
        let disconnectBtn = document.getElementById('disconnect-btn');
        let sellBtn = document.getElementById('sell-btn');
        let txDetails = document.getElementById('tx-details');
        let txDetailsContent = document.getElementById('tx-details-content');
        
        // 连接WebSocket
        connectBtn.addEventListener('click', function() {
            const wsUrl = document.getElementById('ws-url').value;
            connectWebSocket(wsUrl);
        });
        
        // 断开WebSocket连接
        disconnectBtn.addEventListener('click', function() {
            if (ws) {
                ws.close();
                addStatusLog('用户手动断开连接');
            }
        });
        
        // 发送卖出API请求
        sellBtn.addEventListener('click', function() {
            const apiUrl = document.getElementById('api-url').value;
            const tokenAddress = document.getElementById('token-address').value;
            const percentage = document.getElementById('percentage').value;
            const slippageBps = document.getElementById('slippage-bps').value;
            const priorityFee = document.getElementById('priority-fee').value;
            const tipFixed = document.getElementById('tip-fixed').value;
            
            // 请求参数
            const params = {
                token_address: tokenAddress,
                percentage: parseInt(percentage),
                slippage_bps: parseInt(slippageBps),
                priority_fee: parseInt(priorityFee),
                tip_fixed: parseFloat(tipFixed),
                follow_system_defaults: true
            };
            
            addStatusLog(`发送卖出请求: 代币=${tokenAddress}, 百分比=${percentage}%`);
            
            // 发送API请求
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            })
            .then(response => response.json())
            .then(data => {
                addStatusLog(`收到API响应: ${data.success ? '成功' : '失败'} - ${data.message}`);
                updateTxDetails(data);
            })
            .catch(error => {
                addStatusLog(`API请求错误: ${error.message}`);
            });
        });
        
        // 连接WebSocket
        function connectWebSocket(url) {
            try {
                ws = new WebSocket(url);
                
                // 连接打开
                ws.onopen = function(event) {
                    updateConnectionStatus(true);
                    addStatusLog('WebSocket连接已建立');
                };
                
                // 接收消息
                ws.onmessage = function(event) {
                    handleWebSocketMessage(event.data);
                };
                
                // 连接关闭
                ws.onclose = function(event) {
                    updateConnectionStatus(false);
                    addStatusLog(`WebSocket连接已关闭 (代码: ${event.code})`);
                };
                
                // 连接错误
                ws.onerror = function(error) {
                    updateConnectionStatus(false);
                    addStatusLog(`WebSocket错误: ${error.message || '未知错误'}`);
                };
            } catch (error) {
                addStatusLog(`创建WebSocket连接失败: ${error.message}`);
            }
        }
        
        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            try {
                const message = JSON.parse(data);
                
                // 处理欢迎消息
                if (message.type === 'welcome') {
                    addStatusLog(`收到欢迎消息: ${message.message}, 客户端ID: ${message.client_id}`);
                    return;
                }
                
                // 处理交易状态更新
                let statusClass = '';
                let statusText = '';
                
                switch (message.status) {
                    case 'Submitted':
                        statusClass = 'log-entry-status-submitted';
                        statusText = '已提交';
                        break;
                    case 'Processing':
                        statusClass = 'log-entry-status-processing';
                        statusText = '处理中';
                        break;
                    case 'ConfirmingOnchain':
                        statusClass = 'log-entry-status-confirming';
                        statusText = '链上确认中';
                        break;
                    case 'Completed':
                        statusClass = 'log-entry-status-completed';
                        statusText = '已完成';
                        break;
                    default:
                        if (message.status && typeof message.status === 'object' && message.status.Failed) {
                            statusClass = 'log-entry-status-failed';
                            statusText = `失败: ${message.status.Failed}`;
                        } else {
                            statusClass = '';
                            statusText = '未知';
                        }
                }
                
                // 添加到日志
                addStatusLog(`收到交易状态更新: ${statusText} - ${message.message}`, statusClass);
                
                // 更新交易详情
                if (message.transaction_type === 'sell') {
                    updateTxDetailsFromWs(message);
                }
            } catch (error) {
                addStatusLog(`解析WebSocket消息失败: ${error.message}`);
                console.error('WebSocket消息解析错误:', error);
                console.log('原始消息:', data);
            }
        }
        
        // 更新连接状态显示
        function updateConnectionStatus(isConnected) {
            if (isConnected) {
                statusIndicator.className = 'status-indicator status-connected';
                connectionStatus.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sellBtn.disabled = false;
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                connectionStatus.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sellBtn.disabled = true;
                ws = null;
            }
        }
        
        // 添加状态日志
        function addStatusLog(message, className = '') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${className}`;
            
            const timeSpan = document.createElement('span');
            timeSpan.className = 'log-entry-time';
            timeSpan.textContent = `[${timeStr}] `;
            
            const messageSpan = document.createElement('span');
            messageSpan.textContent = message;
            
            logEntry.appendChild(timeSpan);
            logEntry.appendChild(messageSpan);
            
            statusLogs.appendChild(logEntry);
            
            // 自动滚动到底部
            statusLogs.scrollTop = statusLogs.scrollHeight;
        }
        
        // 更新交易详情（API响应）
        function updateTxDetails(data) {
            // 清空现有内容
            txDetailsContent.innerHTML = '';
            
            // 添加详情行
            addDetailRow('请求状态', data.success ? '成功' : '失败');
            addDetailRow('消息', data.message);
            addDetailRow('代币地址', data.token_address);
            addDetailRow('卖出百分比', `${data.sell_percentage}%`);
            addDetailRow('卖出数量', data.sell_amount);
            addDetailRow('小费', data.tip_amount ? `${data.tip_amount} SOL` : '无');
            addDetailRow('滑点', `${data.slippage_bps} 基点 (${data.slippage_bps / 100}%)`);
            addDetailRow('优先费', data.priority_fee ? `${data.priority_fee} 微lamports` : '无');
            addDetailRow('提交时间', data.submitted_at);
            addDetailRow('交易状态', data.status);
            
            if (data.signatures && data.signatures.length > 0) {
                const sigLinks = data.signatures.map(sig => 
                    `<a href="https://solscan.io/tx/${sig}" target="_blank">${sig.substr(0, 8)}...${sig.substr(-8)}</a>`
                ).join('<br>');
                addDetailRow('交易签名', sigLinks, true);
            } else {
                addDetailRow('交易签名', '无');
            }
            
            if (data.error) {
                addDetailRow('错误', data.error);
            }
            
            // 显示详情面板
            txDetails.classList.add('visible');
        }
        
        // 更新交易详情（WebSocket消息）
        function updateTxDetailsFromWs(data) {
            // 清空现有内容
            txDetailsContent.innerHTML = '';
            
            // 解析状态文本
            let statusText = '';
            if (data.status === 'Submitted') statusText = '已提交';
            else if (data.status === 'Processing') statusText = '处理中';
            else if (data.status === 'ConfirmingOnchain') statusText = '链上确认中';
            else if (data.status === 'Completed') statusText = '已完成';
            else if (data.status && typeof data.status === 'object' && data.status.Failed) {
                statusText = `失败: ${data.status.Failed}`;
            } else {
                statusText = '未知';
            }
            
            // 添加详情行
            addDetailRow('请求ID', data.request_id);
            addDetailRow('交易类型', data.transaction_type === 'sell' ? '卖出' : data.transaction_type);
            addDetailRow('代币地址', data.token_address);
            addDetailRow('卖出数量', data.amount || '未知');
            addDetailRow('交易状态', statusText);
            addDetailRow('消息', data.message);
            addDetailRow('时间戳', data.timestamp);
            
            if (data.signatures && data.signatures.length > 0) {
                const sigLinks = data.signatures.map(sig => 
                    `<a href="https://solscan.io/tx/${sig}" target="_blank">${sig.substr(0, 8)}...${sig.substr(-8)}</a>`
                ).join('<br>');
                addDetailRow('交易签名', sigLinks, true);
            } else {
                addDetailRow('交易签名', '无');
            }
            
            // 显示详情面板
            txDetails.classList.add('visible');
        }
        
        // 添加详情行
        function addDetailRow(label, value, isHtml = false) {
            const row = document.createElement('tr');
            
            const labelCell = document.createElement('td');
            labelCell.textContent = label;
            
            const valueCell = document.createElement('td');
            if (isHtml) {
                valueCell.innerHTML = value;
            } else {
                valueCell.textContent = value;
            }
            
            row.appendChild(labelCell);
            row.appendChild(valueCell);
            
            txDetailsContent.appendChild(row);
        }
    </script>
</body>
</html> 