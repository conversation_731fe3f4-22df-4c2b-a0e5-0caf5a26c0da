use std::sync::Arc;
use std::time::{Instant, Duration};
use std::path::Path;
use std::fs;
use tokio::sync::Mutex;
use std::collections::HashMap;
use log::{debug, info, error, warn};

use anyhow::{Result, anyhow};
use solana_client::nonblocking::rpc_client::RpcClient as NonblockingRpcClient;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    transaction::Transaction,
    instruction::{Instruction, AccountMeta},
    hash::Hash,
    commitment_config::CommitmentConfig,
    program_option::COption,
    pubkey,
    system_program,
    sysvar::rent,
    program_pack::Pack,
    system_instruction,
    native_token::LAMPORTS_PER_SOL,
};
use spl_token;
use spl_associated_token_account;
use spl_associated_token_account::get_associated_token_address;
use std::str::FromStr;
use sha2::{Sha256, Digest};
use hex;

// 移除 Redis 依赖
// use redis::{Client as RedisClient, AsyncCommands};

use crate::engine::swap::{SwapDirection};
use crate::common::logger::Logger;
use crate::common::error_translator;
use crate::common::utils::{LatestBlockhash, WalletCache};
use crate::core::priority_fees::{calculate_tip, build_compute_budget_instructions};

// 从IDL加载Discriminator (返回固定大小数组)
fn load_discriminators_fixed() -> Result<([u8; 8], [u8; 8])> {
    let buy_key = "global:buy";
    let sell_key = "global:sell";
    
    let buy_discriminator = sha256_hash(buy_key);
    let sell_discriminator = sha256_hash(sell_key);
    
    println!("使用sha256计算的Buy Discriminator: {:?}", buy_discriminator);
    println!("Buy 十六进制: {}", hex::encode(buy_discriminator));
    
    println!("使用sha256计算的Sell Discriminator: {:?}", sell_discriminator);
    println!("Sell 十六进制: {}", hex::encode(sell_discriminator));
    
    Ok((buy_discriminator, sell_discriminator))
}

// SHA256哈希函数，取前8字节作为discriminator
fn sha256_hash(key: &str) -> [u8; 8] {
    let mut hasher = Sha256::new();
    hasher.update(key.as_bytes());
    let result = hasher.finalize();
    
    let mut discriminator = [0u8; 8];
    discriminator.copy_from_slice(&result[0..8]);
    discriminator
}

// 回退常量 - 使用与新计算方法一致的值
pub const FALLBACK_PUMP_BUY_METHOD: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234]; // global:buy的sha256哈希前8字节
pub const FALLBACK_PUMP_SELL_METHOD: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173]; // global:sell的sha256哈希前8字节

// --- Define Required Constants Locally ---
const PUMP_PROGRAM_ID: Pubkey = pubkey!("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");
const GLOBAL_ACCOUNT_ADDRESS: Pubkey = pubkey!("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf");
const FEE_RECIPIENT_ADDRESS: Pubkey = pubkey!("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
const EVENT_AUTHORITY_ADDRESS: Pubkey = pubkey!("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1");
// Use IDs directly from SDKs/crates where possible
const SYSTEM_PROGRAM_ID: Pubkey = system_program::ID;
const TOKEN_PROGRAM_ID: Pubkey = spl_token::ID;
const ASSOCIATED_TOKEN_PROGRAM_ID: Pubkey = spl_associated_token_account::ID;
const RENT_SYSVAR_ID: Pubkey = rent::ID;
// --- End Local Constants ---

use lazy_static::lazy_static;

// --- 常量和静态变量 ---

lazy_static! {
    // 从IDL加载或回退的Discriminators
    static ref DISCRIMINATORS: ([u8; 8], [u8; 8]) = {
        match load_discriminators_fixed() { // 现在函数已定义
            Ok((buy, sell)) => (buy, sell),
            Err(e) => {
                eprintln!("警告：无法加载IDL discriminator ({} 字节)，使用回退值: {}", std::mem::size_of::<[u8; 8]>(), e);
                (FALLBACK_PUMP_BUY_METHOD, FALLBACK_PUMP_SELL_METHOD)
            }
        }
    };
    static ref PUMP_BUY_DISCRIMINATOR: [u8; 8] = DISCRIMINATORS.0;
    static ref PUMP_SELL_DISCRIMINATOR: [u8; 8] = DISCRIMINATORS.1;

    // 通用的固定账户列表 (Buy/Sell 相同)
    static ref COMMON_FIXED_ACCOUNTS: Vec<AccountMeta> = build_common_fixed_accounts();
}

// 获取正确的Buy方法discriminator (返回 Vec<u8>)
pub fn get_pump_buy_method() -> Vec<u8> {
    PUMP_BUY_DISCRIMINATOR.to_vec() // 添加 .to_vec()
}

// 获取正确的Sell方法discriminator (返回 Vec<u8>)
pub fn get_pump_sell_method() -> Vec<u8> {
    PUMP_SELL_DISCRIMINATOR.to_vec() // 添加 .to_vec()
}

// PumpFun曲线状态结构
#[derive(Debug)]
pub struct PumpCurveState {
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub token_total_supply: u64,
    pub complete: bool,
}

// 辅助函数：构建通用的固定账户列表 (Buy/Sell 相同)
fn build_common_fixed_accounts() -> Vec<AccountMeta> {
    vec![
        // 顺序严格按照IDL和原版代码，保持空位给动态账户
        AccountMeta::new_readonly(GLOBAL_ACCOUNT_ADDRESS, false), // 1. global
        AccountMeta::new(FEE_RECIPIENT_ADDRESS, false),         // 2. fee_recipient (writable)
        // 3. mint (动态) - 在合并时添加
        // 4. bonding_curve (动态) - 在合并时添加
        // 5. curve_token_account (动态) - 在合并时添加
        // 6. user_token_account (动态) - 在合并时添加
        // 7. user_wallet (动态) - 在合并时添加
        AccountMeta::new_readonly(SYSTEM_PROGRAM_ID, false),      // 8. system_program
        AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),       // 9. token_program
        // 10. creator_vault (动态) - 在合并时根据mint地址计算并添加
        AccountMeta::new_readonly(EVENT_AUTHORITY_ADDRESS, false), // 11. event_authority
        // 注意：Buy和Sell指令中creator_vault位置不同，在merge_common_accounts处理
    ]
}

// 新的固定指令模板结构 (Make public)
#[derive(Clone)]
pub struct InstructionFixedTemplate {
    discriminator: [u8; 8],
    fixed_accounts: Vec<AccountMeta>,
    program_id: Pubkey,
}

/// 交易重试配置
#[derive(Debug, Clone)]
pub struct RetryConfig {
    /// 最大重试次数
    pub max_retries: u32,
    /// 每次重试之间的延迟（毫秒）
    pub delay_ms: u64,
    /// 是否使用指数退避算法（每次重试增加延迟）
    pub use_exponential_backoff: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            delay_ms: 500,
            use_exponential_backoff: true,
        }
    }
}

// --- Pump 结构体定义 ---
#[derive(Clone)]
pub struct Pump {
    pub nonblocking_client: Arc<NonblockingRpcClient>,
    pub client: Arc<RpcClient>,
    pub wallet: Arc<Keypair>,
    pub logger: Logger,
    // 添加钱包缓存引用
    pub wallet_cache: Arc<Mutex<WalletCache>>,

    // 新的固定模板
    pub buy_fixed_template: InstructionFixedTemplate,
    pub sell_fixed_template: InstructionFixedTemplate,
}

impl Pump {
    pub fn new(
        nonblocking_client: Arc<NonblockingRpcClient>,
        client: Arc<RpcClient>,
        wallet: Arc<Keypair>,
        // 添加 wallet_cache 参数
        wallet_cache: Arc<Mutex<WalletCache>>,
    ) -> Self {
        let logger = Logger::new("PumpFun".to_string());
        logger.log("Initializing PumpFun service...".to_string());

        // 初始化固定模板
        // 触发 lazy_static 初始化
        let _ = &*PUMP_BUY_DISCRIMINATOR;
        let _ = &*PUMP_SELL_DISCRIMINATOR;
        let _ = &*COMMON_FIXED_ACCOUNTS;
        logger.log("Discriminators and fixed accounts loaded/initialized.".to_string());

        let buy_fixed_template = InstructionFixedTemplate {
            discriminator: *PUMP_BUY_DISCRIMINATOR,
            fixed_accounts: COMMON_FIXED_ACCOUNTS.clone(),
            program_id: PUMP_PROGRAM_ID, // 直接使用导入的常量
        };
        let sell_fixed_template = InstructionFixedTemplate {
            discriminator: *PUMP_SELL_DISCRIMINATOR,
            fixed_accounts: COMMON_FIXED_ACCOUNTS.clone(), // 使用相同的固定账户
            program_id: PUMP_PROGRAM_ID, // 直接使用导入的常量
        };
        logger.log("Fixed instruction templates created.".to_string());

        Self {
            nonblocking_client,
            client,
            wallet,
            logger,
            wallet_cache, // 存储传入的 wallet_cache
            buy_fixed_template,
            sell_fixed_template,
        }
    }

    // 计算Pump.fun Bonding Curve账户地址
    pub fn find_pump_curve_address(token_mint: Pubkey) -> Pubkey {
        let seeds = &[b"bonding-curve", token_mint.as_ref()];
        let (pubkey, _bump) = Pubkey::find_program_address(seeds, &PUMP_PROGRAM_ID);
        pubkey
    }

    // 获取Pump.fun Curve状态
    pub fn get_pump_curve_state(client: &Arc<RpcClient>, curve_address: Pubkey) -> Result<PumpCurveState> {
        let response = client.get_account_data(&curve_address)?;
        
        // Pump.fun曲线状态数据结构的常量
        const PUMP_CURVE_STATE_SIGNATURE: [u8; 8] = [0x17, 0xb7, 0xf8, 0x37, 0x60, 0xd8, 0xac, 0x60];
        const PUMP_CURVE_STATE_SIZE: usize = 0x29;
        const VIRTUAL_TOKEN_RESERVES_OFFSET: usize = 0x08;
        const VIRTUAL_SOL_RESERVES_OFFSET: usize = 0x10;
        const REAL_TOKEN_RESERVES_OFFSET: usize = 0x18;
        const REAL_SOL_RESERVES_OFFSET: usize = 0x20;
        const TOKEN_TOTAL_SUPPLY_OFFSET: usize = 0x28;
        const COMPLETE_OFFSET: usize = 0x30;
        
        if response.len() < PUMP_CURVE_STATE_SIGNATURE.len() + PUMP_CURVE_STATE_SIZE {
            return Err(anyhow!("unexpected curve state"));
        }
        
        // 验证IDL签名
        let idl_signature = &response[0..PUMP_CURVE_STATE_SIGNATURE.len()];
        if idl_signature != PUMP_CURVE_STATE_SIGNATURE {
            return Err(anyhow!("unexpected curve state IDL signature"));
        }
        
        // 读取状态字段
        let virtual_token_reserves = u64::from_le_bytes(
            response[VIRTUAL_TOKEN_RESERVES_OFFSET..VIRTUAL_TOKEN_RESERVES_OFFSET+8]
                .try_into()
                .unwrap()
        );
        
        let virtual_sol_reserves = u64::from_le_bytes(
            response[VIRTUAL_SOL_RESERVES_OFFSET..VIRTUAL_SOL_RESERVES_OFFSET+8]
                .try_into()
                .unwrap()
        );
        
        let real_token_reserves = u64::from_le_bytes(
            response[REAL_TOKEN_RESERVES_OFFSET..REAL_TOKEN_RESERVES_OFFSET+8]
                .try_into()
                .unwrap()
        );
        
        let real_sol_reserves = u64::from_le_bytes(
            response[REAL_SOL_RESERVES_OFFSET..REAL_SOL_RESERVES_OFFSET+8]
                .try_into()
                .unwrap()
        );
        
        let token_total_supply = u64::from_le_bytes(
            response[TOKEN_TOTAL_SUPPLY_OFFSET..TOKEN_TOTAL_SUPPLY_OFFSET+8]
                .try_into()
                .unwrap()
        );
        
        let complete = response[COMPLETE_OFFSET] != 0;
        
        Ok(PumpCurveState {
            virtual_token_reserves,
            virtual_sol_reserves,
            real_token_reserves,
            real_sol_reserves,
            token_total_supply,
            complete,
        })
    }

    // 新版本的构建Buy指令数据函数，适配交换后的参数顺序
    // min_token_output: 买入时最少获得的代币数量(最小单位)
    // max_sol_cost: 买入时花费的SOL数量(lamports)
    fn build_buy_instruction_data_new(&self, min_token_output: u64, max_sol_cost: u64) -> Result<Vec<u8>> {
        // 记录构建指令数据的参数
        self.logger.log(format!(
            "【DEBUG-BUILD-NEW】构建买入指令数据 - min_token_output={}, max_sol_cost={}",
            min_token_output,
            max_sol_cost
        ));
        
        // 参数检查
        if min_token_output == u64::MAX {
            self.logger.warn("【警告-BUILD】min_token_output是u64::MAX，可能是由于浮点数计算溢出");
        }
        
        if max_sol_cost == u64::MAX {
            self.logger.warn("【警告-BUILD】max_sol_cost是u64::MAX，可能是由于浮点数计算溢出");
        }
        
        // 滑点验证 - 确保参数合理配对
        if max_sol_cost > 0 && min_token_output == 0 {
            self.logger.error("错误：买入时指定了SOL数量，但最小获得代币数量为0");
        }
        
        // 获取分类器(discriminator)
        let discriminator = get_pump_buy_method();
        self.logger.log(format!("【DEBUG-BUILD】使用Buy方法discriminator: {:?}", discriminator));
        
        // 构建买入指令数据
        // 买入指令数据格式：
        // discriminator (8 bytes) + min_token_output (8 bytes) + max_sol_cost (8 bytes)
        let mut data = Vec::with_capacity(8 + 8 + 8);
        
        // 添加分类器
        data.extend_from_slice(&discriminator);
        
        // 添加min_token_output参数 (u64，小端字节序)
        data.extend_from_slice(&min_token_output.to_le_bytes());
        
        // 添加max_sol_cost参数 (u64，小端字节序)
        data.extend_from_slice(&max_sol_cost.to_le_bytes());
        
        self.logger.log(format!(
            "【DEBUG-BUILD】买入指令数据构建完成，总长度: {} 字节, 正在进行交易的2%滑点保护...",
            data.len()
        ));
        
        Ok(data)
    }

    // 新版本的构建Sell指令数据函数，适配交换后的参数顺序
    // token_amount_in: 卖出代币数量(最小单位)
    // min_sol_output: 最少获得的SOL数量(lamports)
    fn build_sell_instruction_data_new(&self, token_amount_in: u64, min_sol_output: u64) -> Result<Vec<u8>> {
        // 获取分类器(discriminator)
        let discriminator = get_pump_sell_method();
        
        self.logger.log(format!(
            "【DEBUG-BUILD-NEW】构建卖出指令数据 - token_amount_in={}, min_sol_output={}",
            token_amount_in,
            min_sol_output
        ));
        
        // 滑点验证 - 确保参数合理配对
        if token_amount_in > 0 && min_sol_output == 0 {
            self.logger.error("错误：卖出时指定了代币数量，但最小获得SOL数量为0");
        }
        
        // 构建卖出指令数据
        // 卖出指令数据格式：
        // discriminator (8 bytes) + token_amount_in (8 bytes) + min_sol_output (8 bytes)
        let mut data = Vec::with_capacity(8 + 8 + 8);
        
        // 添加分类器
        data.extend_from_slice(&discriminator);
        
        // 添加token_amount_in参数 (u64，小端字节序)
        data.extend_from_slice(&token_amount_in.to_le_bytes());
        
        // 添加min_sol_output参数 (u64，小端字节序)
        data.extend_from_slice(&min_sol_output.to_le_bytes());
        
        self.logger.log(format!(
            "【DEBUG-BUILD】卖出指令数据构建完成，总长度: {} 字节, 正在进行交易的2%滑点保护...",
            data.len()
        ));
        
        Ok(data)
    }

    // 通用的账户合并函数 (Buy/Sell 使用不同的顺序)
    fn merge_common_accounts(
        &self,
        _fixed_metas: &[AccountMeta],
        mint: &Pubkey,
        bonding_curve: &Pubkey,
        curve_token_account: &Pubkey,
        user_token_account: &Pubkey,
        user_wallet: &Pubkey,
        direction: &SwapDirection,
    ) -> Result<Vec<AccountMeta>> {
        // 从环境变量获取当前交易的创作者金库地址
        let creator_vault_str = std::env::var("CURRENT_TRANSACTION_CREATOR_VAULT")
            .map_err(|_| anyhow!("未设置当前交易的创作者金库地址环境变量"))?;
        
        let creator_vault = Pubkey::from_str(&creator_vault_str)
            .map_err(|_| anyhow!("创作者金库地址格式无效"))?;
        
        let accounts = match direction {
            SwapDirection::Buy => {
                vec![
                    AccountMeta::new_readonly(GLOBAL_ACCOUNT_ADDRESS, false),  // global
                    AccountMeta::new(FEE_RECIPIENT_ADDRESS, false),            // feeRecipient
                    AccountMeta::new_readonly(*mint, false),                   // mint
                    AccountMeta::new(*bonding_curve, false),                   // bondingCurve
                    AccountMeta::new(*curve_token_account, false),             // associatedBondingCurve
                    AccountMeta::new(*user_token_account, false),              // associatedUser
                    AccountMeta::new(*user_wallet, true),                      // user
                    AccountMeta::new_readonly(system_program::ID, false),      // systemProgram
                    AccountMeta::new_readonly(spl_token::ID, false),           // tokenProgram
                    AccountMeta::new(creator_vault, false),                    // creatorVault - 需要标记为可写
                    AccountMeta::new_readonly(EVENT_AUTHORITY_ADDRESS, false), // eventAuthority
                    AccountMeta::new_readonly(PUMP_PROGRAM_ID, false),         // program
                ]
            },
            SwapDirection::Sell => {
                vec![
                    // 0. global (只读)
                    AccountMeta::new_readonly(GLOBAL_ACCOUNT_ADDRESS, false),
                    // 1. fee_recipient (可写)
                    AccountMeta::new(FEE_RECIPIENT_ADDRESS, false),
                    // 2. mint (只读)
                    AccountMeta::new_readonly(*mint, false),
                    // 3. bonding_curve (可写)
                    AccountMeta::new(*bonding_curve, false),
                    // 4. associated_bonding_curve (可写)
                    AccountMeta::new(*curve_token_account, false),
                    // 5. associated_user (可写)
                    AccountMeta::new(*user_token_account, false),
                    // 6. user (签名者，可写)
                    AccountMeta::new(*user_wallet, true),
                    // 7. system_program (只读)
                    AccountMeta::new_readonly(system_program::ID, false),
                    // 8. creator_vault (可写)
                    AccountMeta::new(creator_vault, false),
                    // 9. token_program (只读)
                    AccountMeta::new_readonly(spl_token::ID, false),
                    // 10. event_authority (只读)
                    AccountMeta::new_readonly(EVENT_AUTHORITY_ADDRESS, false),
                    // 11. program (只读)
                    AccountMeta::new_readonly(PUMP_PROGRAM_ID, false),
                ]
            }
        };
        
        Ok(accounts)
    }

    // 修改检查代币余额的方法，使用本地缓存
    pub async fn check_token_balance(&self, mint: &Pubkey, required_amount: u64) -> Result<bool> {
        let mint_str = mint.to_string();

        // 异步锁定本地钱包缓存
        let wallet_cache_guard = self.wallet_cache.lock().await;

        // 在缓存中查找代币信息
        match wallet_cache_guard.tokens.get(&mint_str) {
            Some(token_info) => {
                let balance = token_info.balance;
                if balance >= required_amount {
                    Ok(true)
                } else {
                    self.logger.warn(format!("本地缓存代币余额不足: 需要 {}, 实际 {}", required_amount, balance));
                    Ok(false)
                }
            }
            None => {
                self.logger.warn(format!("本地缓存中未找到代币: {}, 默认余额不足", mint_str));
                Ok(false)
            }
        }
    }

    pub async fn build_swap_instructions(
        &self,
        direction: SwapDirection,
        token_amount_param: u64,
        limit_amount_param: u64,
        mint: &str,
        compute_unit_limit: Option<u32>,
        compute_unit_price_micro_lamports: Option<u64>,
        add_zero_slot_tip: bool,
        api_fixed_tip_sol: Option<f64>,
        api_tip_percentage: Option<f64>,
        transaction_originator: Option<String>,
    ) -> Result<Vec<Instruction>> {
        let mint_pubkey = Pubkey::from_str(mint)
            .map_err(|e| anyhow!("Invalid mint address: {}, error: {}", mint, e))?;
        
        let (fixed_template, program_id) = match direction {
            SwapDirection::Buy => (&self.buy_fixed_template, self.buy_fixed_template.program_id),
            SwapDirection::Sell => (&self.sell_fixed_template, self.sell_fixed_template.program_id),
        };
        
        let data = match direction {
            SwapDirection::Buy => self.build_buy_instruction_data_new(token_amount_param, limit_amount_param)?,
            SwapDirection::Sell => self.build_sell_instruction_data_new(token_amount_param, limit_amount_param)?,
        };

        let (bonding_curve, curve_token_account, user_token_account) = {
            use crate::common::utils::pda_cache::PdaCacheService;
            let pda_cache_service = PdaCacheService::new(
                self.client.clone(),
                self.wallet.clone(),
                self.wallet_cache.clone(),
                None,
            );
            
            match pda_cache_service.get_or_cache_token_pda(mint).await {
                Ok((bc, cta, uta)) => (bc, cta, uta),
                Err(_) => {
                    self.logger.warn(format!("PDA缓存失败，使用传统方式计算PDA/ATA地址: mint={}", mint));
                    let (bc, _) = Pubkey::find_program_address(&[b"bonding-curve", mint_pubkey.as_ref()], &program_id);
                    let cta = get_associated_token_address(&bc, &mint_pubkey);
                    let uta = get_associated_token_address(&self.wallet.pubkey(), &mint_pubkey);
                    (bc, cta, uta)
                }
            }
        };
        
        let user_wallet_pubkey = self.wallet.pubkey();

        // 详细记录地址信息便于调试
        self.logger.log(format!(
            "【DEBUG-ADDRESSES】\nMint地址: {}\nBonding Curve地址: {}\nCurve Token地址: {}\n用户Token地址: {}\n用户钱包地址: {}",
            mint_pubkey, bonding_curve, curve_token_account, user_token_account, user_wallet_pubkey
        ));

        // 直接从交易数据中获取创作者金库地址，不再依赖缓存
        // 获取缓存的账户列表
        let final_accounts = self.merge_common_accounts(
            &fixed_template.fixed_accounts,
            &mint_pubkey,
            &bonding_curve,
            &curve_token_account,
            &user_token_account,
            &user_wallet_pubkey,
            &direction,
        )?;

        let swap_instruction = Instruction {
            program_id,
            accounts: final_accounts,
            data,
        };

        let mut instructions = Vec::with_capacity(5);
        
        let compute_budget_instructions = build_compute_budget_instructions(
            compute_unit_limit.unwrap_or(70000),
            compute_unit_price_micro_lamports.unwrap_or(60000)
        );
        self.logger.log(format!("构建计算预算指令 - 单元限制: {}, 优先费: {} 微lamports",
            compute_unit_limit.unwrap_or(70000),
            compute_unit_price_micro_lamports.unwrap_or(60000)
        ));
        instructions.extend(compute_budget_instructions);
        
        if matches!(direction, SwapDirection::Buy) {
            let create_ata_ix = spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &mint_pubkey,
                &spl_token::id(),
            );
            instructions.push(create_ata_ix);
        }

        instructions.push(swap_instruction);
        
        if add_zero_slot_tip {
            let use_zero_slot = std::env::var("USE_ZERO_SLOT").unwrap_or_default() == "true";
            
            if use_zero_slot {
                let transaction_amount_lamports = match direction {
                    SwapDirection::Buy => {
                        // 买入交易：必须从代币数量和价格正确计算SOL金额
                        // 对于买入操作，limit_amount_param本身就是SOL金额(lamports)
                        // 做一个安全检查，确保金额不会过大
                        if limit_amount_param > LAMPORTS_PER_SOL * 1000 {
                            self.logger.warn(format!("检测到异常大的SOL输入金额: {} lamports (约 {} SOL)，限制在1000 SOL", 
                                limit_amount_param, limit_amount_param as f64 / LAMPORTS_PER_SOL as f64));
                            LAMPORTS_PER_SOL * 1000
                        } else {
                            limit_amount_param
                        }
                    },
                    SwapDirection::Sell => {
                        // 卖出交易：必须从代币数量和价格正确计算预计获得的SOL金额
                        // 1. 获取代币精度
                        let decimals = self.get_token_decimals(mint).await.unwrap_or(6);
                        
                        // 2. 将代币从最小单位转换为UI数量
                        let token_amount_ui = token_amount_param as f64 / 10f64.powi(decimals as i32);
                        self.logger.log(format!("卖出交易-代币UI数量: {} (最小单位: {})", token_amount_ui, token_amount_param));
                        
                        // 3. 计算预期获得的SOL金额
                        // 对于卖出操作，limit_amount_param是最小SOL输出量(lamports)
                        // 这个值已经在trade_handler.rs的handle_sell方法中使用正确的价格计算好了
                        // 不需要再次计算或估算价格
                        let sol_lamports = limit_amount_param;
                        
                        self.logger.log(format!("卖出交易-使用传入的最小SOL输出量: {} lamports (约 {} SOL)",
                            sol_lamports, sol_lamports as f64 / LAMPORTS_PER_SOL as f64));
                        
                        // 校验计算的金额，确保不会出现异常值
                        if sol_lamports > LAMPORTS_PER_SOL * 100 {
                            self.logger.warn(format!("传入的SOL金额异常大: {} lamports (约 {} SOL)，将限制在100 SOL以内", 
                                sol_lamports, sol_lamports as f64 / LAMPORTS_PER_SOL as f64));
                            LAMPORTS_PER_SOL * 100
                        } else if sol_lamports == 0 {
                            self.logger.warn("传入的SOL金额为0，使用最小值1 lamports");
                            1 // 使用1 lamports作为最小值
                        } else {
                            sol_lamports
                        }
                    },
                };
                
                // 记录计算后的真实SOL金额
                self.logger.log(format!("预计真实的SOL金额: {} lamports (约 {:.6} SOL)", 
                    transaction_amount_lamports, 
                    transaction_amount_lamports as f64 / LAMPORTS_PER_SOL as f64));
                
                // 使用钱包配置中的tip_percentage来计算小费
                let tip_percentage = if let Some(api_tip) = api_tip_percentage {
                    self.logger.log(format!("直接使用api_tip_percentage参数: {}%", api_tip));
                    api_tip
                } else {
                    match crate::core::priority_fees::MonitorConfigs::load() {
                        Ok(configs) => {
                            // 从交易参数获取发起者地址
                            let transaction_originator = match transaction_originator {
                                Some(originator) => originator,
                                None => {
                                    // 如果没有提供交易发起者地址，返回错误
                                    self.logger.error("未提供交易发起者地址，无法计算小费");
                                    return Err(anyhow!("错误：交易缺少必要的发起者地址信息"));
                                }
                            };
                            self.logger.log(format!("为交易发起者 {} 查找小费配置", transaction_originator));
                            match configs.get_wallet_config(&transaction_originator) {
                                Some(wallet_config) => {
                                    self.logger.log(format!("找到交易发起者 {} 的小费配置: {}%", transaction_originator, wallet_config.tip_percentage));
                                    wallet_config.tip_percentage
                                },
                                None => {
                                    // 如果找不到钱包配置，返回错误
                                    self.logger.error(format!("错误：未找到交易发起者 {} 的配置，请确保该钱包已在监控配置中设置", transaction_originator));
                                    return Err(anyhow!("错误：未找到交易发起者 {} 的配置，请先添加配置", transaction_originator));
                                }
                            }
                        },
                        Err(e) => {
                            // 如果无法加载配置文件，返回错误
                            self.logger.error(format!("错误：无法加载钱包配置: {}", e));
                            return Err(anyhow!("错误：无法加载钱包配置文件: {}", e));
                        }
                    }
                };
                
                // 限制交易金额用于小费计算，避免异常值
                let max_sol_for_fee_calc = 0.1; // 最多用0.1 SOL作为小费计算基础
                let base_for_fee_sol = (transaction_amount_lamports as f64 / LAMPORTS_PER_SOL as f64).min(max_sol_for_fee_calc);
                
                // 计算小费金额并限制在合理范围内
                let tip_amount_sol = (base_for_fee_sol * (tip_percentage / 100.0)).clamp(0.0005, 1.0);
                let tip_lamports = (tip_amount_sol * LAMPORTS_PER_SOL as f64) as u64;
                
                self.logger.log(format!("小费计算 - 基础金额: {:.6} SOL (限制在{}SOL内), 比例: {}%, 小费: {:.6} SOL ({} lamports)",
                    base_for_fee_sol, 
                    max_sol_for_fee_calc,
                    tip_percentage,
                    tip_amount_sol,
                    tip_lamports
                ));

                let zero_slot_tip_address = match Pubkey::from_str(&crate::services::zero_slot::get_random_zero_slot_tip_address()) {
                    Ok(address) => address,
                    Err(err) => {
                        error!("解析 0slot 小费地址失败: {}", err);
                        return Err(anyhow!("解析 0slot 小费地址失败: {}", err));
                    }
                };
                
                let transfer_ix = system_instruction::transfer(
                    &self.wallet.pubkey(),
                    &zero_slot_tip_address,
                    tip_lamports
                );
                
                instructions.push(transfer_ix);
            }
        }

        Ok(instructions)
    }

    /// 获取代币价格
    pub async fn get_token_price(&self, mint_str: &str) -> Result<PriceResult> {
        self.logger.log(format!("获取代币价格: {}", mint_str));
        
        let mint_pubkey = Pubkey::from_str(mint_str)?;
        let curve_address = Self::find_pump_curve_address(mint_pubkey);
        
        self.logger.debug(format!("找到对应的曲线地址: {}", curve_address));
        
        let curve_state = Self::get_pump_curve_state(&self.client, curve_address)?;
        
        self.logger.debug(format!("获取曲线状态成功: {:#?}", curve_state));
        
        // 获取代币精度
        let token_decimals = self.get_token_decimals(mint_str).await.unwrap_or(6);
        let sol_decimals = 9; // SOL固定精度
        
        // 计算原始价格 (lamports/最小代币单位)
        let price_in_lamports = curve_state.virtual_sol_reserves as f64 / curve_state.virtual_token_reserves as f64;
        
        // 调整精度：考虑SOL和代币之间的精度差异（使用有符号整数避免溢出）
        let decimal_adjustment = 10f64.powi((token_decimals as i32) - (sol_decimals as i32));
        let simplified_price = price_in_lamports * decimal_adjustment;
        
        self.logger.debug(format!(
            "计算代币价格: {} SOL (或 {} lamports/token) [精度调整: 10^{}]",
            simplified_price,
            price_in_lamports,
            (token_decimals as i32) - (sol_decimals as i32)
        ));
        
        // 更新代币缓存中的价格信息
        {
            let mut wallet_cache_guard = self.wallet_cache.lock().await;
            if let Some(token_info) = wallet_cache_guard.tokens.get_mut(mint_str) {
                // 更新代币的最后价格
                token_info.last_price = Some(simplified_price);
                self.logger.log(format!("更新代币 {} 的缓存价格为: {} SOL", mint_str, simplified_price));
            }
        }
        
        Ok(PriceResult {
            price_in_lamports,
            simplified_price,
            curve_state,
        })
    }

    /// 获取代币精度
    async fn get_token_decimals(&self, mint: &str) -> Result<u8> {
        let wallet_cache = self.wallet_cache.lock().await;
        if let Some(token_info) = wallet_cache.tokens.get(mint) {
            Ok(token_info.decimals)
        } else {
            // 默认返回6位精度(大多数Solana代币)
            Ok(6)
        }
    }

    pub async fn execute_swap_transaction(
        &self,
        instructions: Vec<Instruction>,
        blockhash: Hash,
        use_jito: bool, // 是否使用Jito MEV保护
        retry_config: Option<RetryConfig>,
    ) -> Result<String> {
        // 添加日志函数，用于记录交易错误的详细信息
        let analyze_transaction_error = |error: &anyhow::Error| {
            let error_message = error.to_string();
            
            if error_message.contains("0x1772") {
                self.logger.error("=============== 滑点错误详细诊断 ===============");
                self.logger.error("可能原因:");
                self.logger.error("1. 原始交易参数提取错误或不完整");
                self.logger.error("2. 代币价格波动过大，导致滑点超出预设限制");
                self.logger.error("3. 跟单金额过大，造成市场冲击导致滑点扩大");
                
                // 示例如何从错误消息中提取更多信息
                if let Some(idx) = error_message.find("expected output") {
                    if let Some(end_idx) = error_message[idx..].find(",") {
                        let expected_output = &error_message[idx + "expected output".len()..idx + end_idx];
                        self.logger.error(format!("期望输出: {}", expected_output.trim()));
                    }
                }
                
                if let Some(idx) = error_message.find("actual output") {
                    if let Some(end_idx) = error_message[idx..].find(")") {
                        let actual_output = &error_message[idx + "actual output".len()..idx + end_idx];
                        self.logger.error(format!("实际输出: {}", actual_output.trim()));
                        
                        // 尝试计算实际滑点
                        if let (Some(expected_idx), Some(actual_idx)) = (
                            error_message.find("expected output"),
                            error_message.find("actual output")
                        ) {
                            if let (Some(expected_end), Some(actual_end)) = (
                                error_message[expected_idx..].find(","),
                                error_message[actual_idx..].find(")")
                            ) {
                                let expected_str = &error_message[expected_idx + "expected output".len()..expected_idx + expected_end];
                                let actual_str = &error_message[actual_idx + "actual output".len()..actual_idx + actual_end];
                                
                                if let (Ok(expected), Ok(actual)) = (
                                    expected_str.trim().parse::<f64>(),
                                    actual_str.trim().parse::<f64>()
                                ) {
                                    if expected > 0.0 {
                                        let actual_slippage = (expected - actual) / expected * 100.0;
                                        self.logger.error(format!("计算得到的实际滑点: {:.2}%", actual_slippage));
                                        
                                        // 给出建议
                                        self.logger.error(format!("建议设置的最小滑点: {:.0}%", actual_slippage.ceil() + 10.0));
                                    }
                                }
                            }
                        }
                    }
                }
                
                self.logger.error("=============== 解决方案 ===============");
                self.logger.error("1. 增加SLIPPAGE环境变量值至30-50");
                self.logger.error("2. 降低FOLLOW_PERCENTAGE值，减小市场影响");
                self.logger.error("3. 确保RPC连接稳定，减少延迟");
                self.logger.error("4. 检查原始交易参数提取和代币小数位转换逻辑");
                self.logger.error("===========================================");
            }
            // 其他错误类型的处理可以在这里添加...
        };
        
        self.logger.log("Preparing to retrieve blockhash and submit transaction");
        
        // 创建要签名的交易
        let tx = Transaction::new_signed_with_payer(
            &instructions,
            Some(&self.wallet.pubkey()),
            &[&*self.wallet],
            blockhash
        );
        
        // 获取重试配置，如果没有提供则使用默认值
        let retry_config = retry_config.unwrap_or_else(|| RetryConfig::default());
        
        self.logger.log("========== CRITICAL TIMING SEQUENCE BEGINS ==========");
        
        // 发送交易并获取签名，使用重试逻辑
        let mut last_error = None;
        let mut retry_count = 0;
        
        let signature = loop {
            if retry_count > 0 {
                self.logger.debug(format!("重试发送交易 (第 {} 次，共 {} 次)", 
                    retry_count, retry_config.max_retries));
                
                // 计算延迟时间
                let delay_ms = if retry_config.use_exponential_backoff {
                    retry_config.delay_ms * (2_u64.pow(retry_count as u32 - 1))
                } else {
                    retry_config.delay_ms
                };
                
                self.logger.debug(format!("等待 {} 毫秒后重试", delay_ms));
                tokio::time::sleep(std::time::Duration::from_millis(delay_ms)).await;
            }
            
            match use_jito {
                true => {
                    self.logger.log("使用JITO发送交易以获得MEV保护");
                    // TODO: 实现JITO交易提交
                    let err = anyhow!("JITO交易提交尚未实现");
                    last_error = Some(err);
                    break Err(last_error.unwrap());
                },
                false => {
                    if retry_count == 0 {
                        self.logger.log("直接从RPC获取最新区块哈希");
                        self.logger.log("成功获取RPC最新区块哈希: ".to_string() + &blockhash.to_string());
                    }
                    
                    self.logger.log(format!("创建并签名交易{}", 
                        if retry_count > 0 { format!(" (重试 {}/{})", retry_count, retry_config.max_retries) } else { "".to_string() }));
                    
                    // 通过普通RPC发送
                    self.logger.log("立即发送交易");
                    match self.client.send_transaction(&tx) {
                        Ok(signature) => {
                            if retry_count > 0 {
                                self.logger.log(format!("重试成功! 第 {} 次重试发送成功", retry_count));
                            }
                            break Ok(signature.to_string());
                        },
                        Err(e) => {
                            let err_message = e.to_string();
                            self.logger.error(format!("交易发送失败: {}", err_message));
                            
                            // 记录错误以便最后一次重试失败时使用
                            last_error = Some(anyhow!("{}", err_message));
                            
                            // 确定是否应该重试
                            let should_retry = err_message.contains("0x1") || // 一般的Solana错误
                                               err_message.contains("Blockhash") || // 区块哈希问题
                                               err_message.contains("timeout"); // 超时
                            
                            // 增加重试计数并检查是否达到最大重试次数
                            retry_count += 1;
                            if !should_retry || retry_count > retry_config.max_retries {
                                // 不应重试或已达到最大重试次数
                                if retry_count > retry_config.max_retries {
                                    self.logger.error(format!("达到最大重试次数 ({})，停止重试", retry_config.max_retries));
                                } else {
                                    self.logger.error("根据错误类型判断，不应重试此错误");
                                }
                                
                                // 详细分析错误
                                analyze_transaction_error(&last_error.as_ref().unwrap());
                                
                                // 使用common::error_translator进行错误翻译
                                if let Some(error_code) = crate::common::error_translator::extract_error_code(&err_message) {
                                    let translation = crate::common::error_translator::analyze_transaction_error(&err_message);
                                    self.logger.error(format!("[ERROR] 交易失败原因分析: {}", translation));
                                }
                                
                                break Err(last_error.unwrap());
                            }
                            
                            // 继续循环以进行下一次重试
                            self.logger.warn(format!("将在延迟后进行第 {} 次重试", retry_count));
                        }
                    }
                }
            };
        };
        
        self.logger.log("========== CRITICAL TIMING SEQUENCE ENDS ==========");
        
        // 处理交易结果
        match signature {
            Ok(signature_str) => {
                self.logger.log(format!("交易发送成功，签名: {}", signature_str));
                Ok(signature_str)
            },
            Err(e) => Err(e)
        }
    }

    // 在需要手动构建bonding curve地址时使用
    pub fn build_bonding_curve_address_manually(mint: &Pubkey) -> Result<(Pubkey, u8)> {
        let seeds = &[b"bonding-curve", mint.as_ref()];
        let program_id = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?;
        let (pubkey, bump) = Pubkey::find_program_address(seeds, &program_id);
        Ok((pubkey, bump))
    }

    pub fn find_staking_pool_address(mint: &Pubkey) -> Result<(Pubkey, u8)> {
        let seeds = &[b"staking-pool", mint.as_ref()];
        let program_id = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?;
        let (pubkey, bump) = Pubkey::find_program_address(seeds, &program_id);
        Ok((pubkey, bump))
    }

    pub fn compute_mint_pda_address(user_address: &str, name: &str) -> Result<Pubkey> {
        let user_pubkey = Pubkey::from_str(user_address)?;
        let seeds = &[user_pubkey.as_ref(), name.as_bytes()];
        let (pda, _bump) = Pubkey::find_program_address(seeds, &PUMP_PROGRAM_ID);
        Ok(pda)
    }

    // 新增方法：从mint直接派生创作者金库地址
    pub fn find_creator_vault_address(mint: &Pubkey) -> Result<Pubkey> {
        // 不再计算PDA，改为从交易数据中提取
        // 在这里我们只能返回一个错误，实际的提取将在TransactionData处理过程中完成
        Err(anyhow!("不应直接计算金库地址，应从交易数据中提取"))
    }

    // 修复交易中的账户权限和添加缺失的rent账户
    fn fix_account_permissions(&self, instructions: &mut Vec<Instruction>) {
        for ins in instructions.iter_mut() {
            if ins.program_id == PUMP_PROGRAM_ID {
                self.logger.log(format!("修复Pump交易指令账户权限，共有{}个账户", ins.accounts.len()));
                
                // 找到creator_vault账户的位置并标记为可写
                if ins.accounts.len() >= 10 {
                    // 创作者金库账户(第10个)应被标记为可写
                    ins.accounts[9].is_writable = true;
                    self.logger.log(format!("将创作者金库账户标记为可写: {}", ins.accounts[9].pubkey));
                    
                    // 确保bonding_curve(第4个)被标记为可写
                    if ins.accounts.len() >= 4 {
                        ins.accounts[3].is_writable = true;
                        self.logger.log(format!("将bonding_curve账户标记为可写: {}", ins.accounts[3].pubkey));
                    }
                    
                    // 确保associated_bonding_curve(第5个)被标记为可写
                    if ins.accounts.len() >= 5 {
                        ins.accounts[4].is_writable = true;
                        self.logger.log(format!("将curve_token_account账户标记为可写: {}", ins.accounts[4].pubkey));
                    }
                    
                    // 确保associated_user(第6个)被标记为可写
                    if ins.accounts.len() >= 6 {
                        ins.accounts[5].is_writable = true;
                        self.logger.log(format!("将user_token_account账户标记为可写: {}", ins.accounts[5].pubkey));
                    }
                }
                
                // 如果没有rent账户，添加它
                let rent_account_exists = ins.accounts.iter().any(|acc| acc.pubkey == RENT_SYSVAR_ID);
                if !rent_account_exists {
                    // 在tokenProgram后和creatorVault前添加rent账户
                    // 通常tokenProgram是第9个账户
                    if ins.accounts.len() >= 9 {
                        self.logger.log("添加缺失的rent系统变量账户");
                        ins.accounts.insert(9, AccountMeta::new_readonly(RENT_SYSVAR_ID, false));
                    }
                }
            }
        }
    }

    // 添加常量到impl内部
    const DEFAULT_SLIPPAGE_BPS: u64 = 200; // 2%的默认滑点

    pub async fn pump_swap(
        &self,
        direction: SwapDirection,
        token_amount_param: u64,
        limit_amount_param: u64,
        mint: &str,
        compute_unit_limit: Option<u32>,
        compute_unit_price_micro_lamports: Option<u64>,
        add_zero_slot_tip: bool,
        api_fixed_tip_sol: Option<f64>,
        api_tip_percentage: Option<f64>,
        transaction_originator: Option<String>,
    ) -> Result<String> {
        self.logger.log(format!("【DEBUG-SWAP】进入pump_swap函数 - 方向: {:?}, 代币: {}, 滑点: {} BPS", 
            direction, mint, Self::DEFAULT_SLIPPAGE_BPS));
        self.logger.log(format!("【DEBUG-SWAP】收到token_amount_param: {}", token_amount_param));
        self.logger.log(format!("【DEBUG-SWAP】收到limit_amount_param: {}", limit_amount_param));
        
        // 构建交易指令
        let mut instructions = self.build_swap_instructions(
            direction.clone(),
            token_amount_param,
            limit_amount_param,
            mint,
            compute_unit_limit,
            compute_unit_price_micro_lamports,
            add_zero_slot_tip,
            api_fixed_tip_sol,
            api_tip_percentage,
            transaction_originator,
        ).await?;
        
        // 修复账户权限并添加缺失的rent账户
        self.logger.log("正在修复交易账户权限和添加缺失的rent账户...");
        self.fix_account_permissions(&mut instructions);
        
        // 提交交易
        let use_zero_slot = true; // 总是使用0slot
        
        let result = if use_zero_slot {
            self.logger.log("使用0slot服务进行交易提交，计算单元限制: 250000");
            
            // 创建默认配置，不需要设置client字段
            let config = crate::services::zero_slot::ZeroSlotConfig::default();
            
            let zero_slot_service = crate::services::zero_slot::ZeroSlotService::new(
                Some(config)
            );
            
            self.logger.log(format!("当前钱包地址: {}", self.wallet.pubkey()));
            
            // 构建完整交易
            let recent_blockhash = self.client.get_latest_blockhash()?;
            let tx = Transaction::new_signed_with_payer(
                &instructions,
                Some(&self.wallet.pubkey()),
                &[&*self.wallet],
                recent_blockhash
            );
            
            // 使用正确的公共方法submit_transaction而不是私有的send_transaction
            zero_slot_service.submit_transaction(&tx).await?
        } else {
            // 原有的代码分支，保持不变
            "".to_string() // 这行只是占位，在实际使用中会被替换
        };
        
        Ok(result)
    }
}

/// 价格计算结果
#[derive(Debug)]
pub struct PriceResult {
    pub price_in_lamports: f64,
    pub simplified_price: f64,
    pub curve_state: PumpCurveState,
} 