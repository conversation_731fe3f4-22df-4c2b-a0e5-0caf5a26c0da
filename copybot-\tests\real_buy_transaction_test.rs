use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    bs58,
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::{Instruction, AccountMeta},
    native_token::LAMPORTS_PER_SOL,
    pubkey::Pubkey,
    signature::{Keypair, Signer, SeedDerivable},
    system_instruction,
    transaction::Transaction,
    sysvar,
    system_program,
};
use spl_associated_token_account::id as ata_program_id;
use spl_token;
use std::str::FromStr;
use copy_trading_bot::dex::pump_fun::get_pump_buy_method;
use spl_associated_token_account::get_associated_token_address;

// --- 配置常量 ---

// 从 tx_AW1UetPyA7M8.json 和用户输入推断
const MINT_ADDRESS_STR: &str = "52NPLudv5oKNDEPm1LtcpeXEoDx9qe4h1QcTiLFapump";
const USER_SIGNER_STR: &str = "8hAMyQCnLRGcH5WYHNRoSPpCneFoTYjSiU9hbjrhvZjd";
const FEE_RECIPIENT_STR: &str = "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV"; // 小费接收者
const PUMP_PROGRAM_ID_STR: &str = "BXxgGt3akAghZviYHLh8KUh6vhXBht5wf86De6huTp95";
const GLOBAL_ACCOUNT_STR: &str = "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf";
const EVENT_AUTHORITY_STR: &str = "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1";

const BUY_AMOUNT_SOL: f64 = 0.02;
const DEFAULT_TIP_PERCENTAGE: f64 = 1.0; // 默认1%的小费百分比
const COMPUTE_UNIT_LIMIT: u32 = 80_000;
const COMPUTE_UNIT_PRICE: u64 = 6800;

// !!! 实际 RPC URL !!!
const RPC_URL: &str = "https://mainnet.helius-rpc.com/?api-key=40b3da8c-5adb-4822-a333-44cbfb58d3c5";

// !!! 确保这是正确的私钥字符串 !!!
const PRIVATE_KEY_STR: &str = "2bnDXL2GvcNo6eAv7WhdSnqyFEsoKaZXxfL4HVRSWvtVoWD46ct5mmXGCSJZnh7F2yhsve2Uao2JSvgErB8jVRnX";

#[tokio::test(flavor = "multi_thread")]
async fn test_execute_real_buy_transaction() {
    println!("--- 开始执行真实 Pump.fun 买入交易测试 --- (使用硬编码数据)");
    println!("警告: 此操作将花费真实的 SOL!");

    // --- 1. 初始化 ---
    let rpc_client = RpcClient::new_with_commitment(RPC_URL.to_string(), CommitmentConfig::confirmed());

    // 使用 bs58 解码 (预期得到 64 字节)
    let keypair_bytes = bs58::decode(PRIVATE_KEY_STR)
        .into_vec()
        .expect("bs58 解码失败");
    println!("解码后的字节长度: {}", keypair_bytes.len());

    // 直接使用解码后的字节调用 from_bytes (预期长度为 64)
    let signer = Keypair::from_bytes(&keypair_bytes)
        .expect(&format!(
            "Keypair::from_bytes 失败 - 输入长度为 {} 字节，但该函数严格要求 64 字节!",
            keypair_bytes.len()
        ));

    println!("从正确私钥加载的公钥: {}", signer.pubkey());

    // --- 现在重新启用公钥验证 ---
    let user_signer_pk_expected = Pubkey::from_str(USER_SIGNER_STR).expect("无效的用户签名者公钥字符串");
    if signer.pubkey() != user_signer_pk_expected {
        eprintln!("错误: 从私钥加载的公钥 ({}) 与预期的签名者公钥 ({}) 不匹配。这不应该发生!", signer.pubkey(), user_signer_pk_expected);
        panic!("密钥对不匹配");
    }
    println!(">> 公钥验证通过! <<");

    // 解析硬编码的公钥
    let mint_pk = Pubkey::from_str(MINT_ADDRESS_STR).expect("无效的 Mint 地址字符串");
    let fee_recipient_pk = Pubkey::from_str(FEE_RECIPIENT_STR).expect("无效的小费接收者公钥字符串");
    let pump_program_id = Pubkey::from_str(PUMP_PROGRAM_ID_STR).expect("无效的 Pump 程序 ID 字符串");
    let event_authority_pk = Pubkey::from_str(EVENT_AUTHORITY_STR).expect("无效的事件授权公钥字符串");
    let global_account_pk = Pubkey::from_str(GLOBAL_ACCOUNT_STR).expect("无效的 Global 账户公钥字符串");

    // 动态计算派生地址
    println!("动态计算派生地址...");
    let (curve_address_pk, _curve_bump) = Pubkey::find_program_address(
        &[b"bonding-curve", mint_pk.as_ref()],
        &pump_program_id
    );
    println!("  计算得到 Bonding Curve (PDA): {}", curve_address_pk);

    let associated_bonding_curve_pk = get_associated_token_address(
        &curve_address_pk, // Owner is the bonding curve PDA
        &mint_pk
    );
    println!("  计算得到 Associated Bonding Curve (ATA): {}", associated_bonding_curve_pk);

    let user_ata_pk = get_associated_token_address(
        &signer.pubkey(), // Owner is the user/signer
        &mint_pk
    );
    println!("  计算得到 User ATA: {}", user_ata_pk);

    // 计算金额 (Lamports)
    let buy_amount_lamports = (BUY_AMOUNT_SOL * LAMPORTS_PER_SOL as f64) as u64;

    // 修改构建小费指令的部分
    // d) 构建小费转账指令
    let tip_percentage = std::env::var("TIP_PERCENTAGE")
        .unwrap_or_else(|_| DEFAULT_TIP_PERCENTAGE.to_string())
        .parse::<f64>()
        .unwrap_or(DEFAULT_TIP_PERCENTAGE);

    // 计算小费金额为交易金额的百分比
    let tip_amount_sol = (BUY_AMOUNT_SOL * tip_percentage) / 100.0;

    // 确保小费在合理范围内
    let min_tip_sol = 0.0005; // 最小0.0005 SOL
    let max_tip_sol = 1.0;    // 最大1 SOL
    let tip_amount_sol = tip_amount_sol.max(min_tip_sol).min(max_tip_sol);

    let tip_amount_lamports = (tip_amount_sol * LAMPORTS_PER_SOL as f64) as u64;

    println!("小费百分比: {}%", tip_percentage);
    println!("交易金额 (SOL): {}", BUY_AMOUNT_SOL);
    println!("小费金额 (SOL): {}", tip_amount_sol);
    println!("小费金额 (Lamports): {}", tip_amount_lamports);

    // --- 2. 构建指令 ---
    let mut instructions = Vec::new();

    // a) 计算预算指令
    instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(COMPUTE_UNIT_LIMIT));
    instructions.push(ComputeBudgetInstruction::set_compute_unit_price(COMPUTE_UNIT_PRICE));
    println!("已添加 Compute Budget 指令 (Limit: {}, Price: {})", COMPUTE_UNIT_LIMIT, COMPUTE_UNIT_PRICE);

    // b) 获取/创建关联 Token 账户 (ATA) 指令
    let create_ata_ix = spl_associated_token_account::instruction::create_associated_token_account_idempotent(
        &signer.pubkey(),
        &signer.pubkey(),
        &mint_pk,
        &spl_token::id(),
    );
    instructions.push(create_ata_ix);
    println!("已添加 Create ATA (Idempotent) 指令");

    // c) 构建 Pump.fun 买入指令
    let buy_discriminator = get_pump_buy_method();

    let mut buy_instruction_data: Vec<u8> = Vec::new();
    buy_instruction_data.extend_from_slice(&buy_discriminator);
    let token_amount_param: u64 = 4300; // 最小可接受代币数量 (滑点控制)
    let sol_amount_param = buy_amount_lamports; // 支付的 SOL 数量 (仍为 0.02 SOL)

    buy_instruction_data.extend_from_slice(&token_amount_param.to_le_bytes());
    buy_instruction_data.extend_from_slice(&sol_amount_param.to_le_bytes());

    println!("Buy Discriminator (from project): {:?}", buy_discriminator);
    println!("  - Token Amount Param (Min Expected): {}", token_amount_param);
    println!("  - SOL Amount Param (Max Spend): {}", sol_amount_param);

    let buy_ix = Instruction {
        program_id: pump_program_id,
        accounts: vec![
            AccountMeta::new_readonly(global_account_pk, false),
            AccountMeta::new(fee_recipient_pk, false),
            AccountMeta::new_readonly(mint_pk, false),
            AccountMeta::new(curve_address_pk, false),
            AccountMeta::new(associated_bonding_curve_pk, false),
            AccountMeta::new(user_ata_pk, false),
            AccountMeta::new(signer.pubkey(), true),
            AccountMeta::new_readonly(system_program::id(), false),
            AccountMeta::new_readonly(spl_token::id(), false),
            AccountMeta::new_readonly(sysvar::rent::id(), false),
            AccountMeta::new_readonly(event_authority_pk, false),
            AccountMeta::new_readonly(pump_program_id, false),
        ],
        data: buy_instruction_data,
    };
    instructions.push(buy_ix);
    println!("已添加 Pump.fun Buy 指令");

    // d) 构建小费转账指令
    if tip_amount_lamports > 0 {
        let tip_transfer_ix = system_instruction::transfer(
            &signer.pubkey(),
            &fee_recipient_pk,
            tip_amount_lamports,
        );
        instructions.push(tip_transfer_ix);
        println!("已添加 Tip Transfer 指令 ({} Lamports)", tip_amount_lamports);
    } else {
         println!("小费为 0，跳过 Tip Transfer 指令");
    }

    // --- 3. 构建并发送交易 ---
    let recent_blockhash = rpc_client
        .get_latest_blockhash()
        .expect("获取最新 blockhash 失败");
    println!("最新 Blockhash: {}", recent_blockhash);

    // 添加调试打印语句
    println!("DEBUG: Blockhash 获取成功，准备构建完整交易...");

    let tx = Transaction::new_signed_with_payer(
        &instructions,
        Some(&signer.pubkey()),
        &[&signer],
        recent_blockhash,
    );

    println!("交易已构建并签名，包含 {} 条指令。准备模拟...", instructions.len());
    println!("模拟交易使用的账户（部分关键账户）:");
    println!("  - Signer (用户): {}", signer.pubkey());
    println!("  - Mint: {}", mint_pk);
    println!("  - Bonding Curve: {}", curve_address_pk);
    println!("  - Assoc Bonding Curve ATA: {}", associated_bonding_curve_pk);
    println!("  - User ATA: {}", user_ata_pk);

    // --- 4. 模拟交易 (在真实发送前) ---
    println!("尝试模拟交易...");
    match rpc_client.simulate_transaction(&tx) {
        Ok(sim_response) => {
            // 即使有错误，也尝试打印日志
            if let Some(logs) = &sim_response.value.logs {
                eprintln!("--- 模拟日志 开始 ---");
                for log in logs {
                    eprintln!("- {}", log);
                }
                eprintln!("--- 模拟日志 结束 ---");
            }

            if let Some(err) = sim_response.value.err {
                eprintln!("错误: 交易模拟失败!");
                eprintln!("错误详情: {:?}", err);
                panic!("交易模拟失败，请检查错误和日志。"); // 模拟失败则停止
            } else {
                println!("交易模拟成功!");
                // 成功时的日志已经在上面打印过了，这里可以不再打印或保留
                // println!("日志:");
                // if let Some(logs) = sim_response.value.logs {
                //     for log in logs {
                //         println!("- {}", log);
                //     }
                // }
            }
        }
        Err(e) => {
            eprintln!("错误: 调用模拟交易 RPC 时出错: {}", e);
            panic!("模拟交易 RPC 调用失败");
        }
    }

    // --- 5. 发送并确认交易 (只有模拟成功后才执行) ---
    println!("模拟成功，准备发送并确认真实交易...");
    match rpc_client.send_and_confirm_transaction_with_spinner(&tx) {
        Ok(signature) => {
            println!("交易成功发送并确认！");
            println!("签名: {}", signature);
            println!("在 Solscan 上查看: https://solscan.io/tx/{}", signature);
        }
        Err(e) => {
            eprintln!("错误: 发送或确认交易失败: {}", e);
            panic!("交易失败");
        }
    }

    println!("--- 真实 Pump.fun 买入交易测试执行完毕 ---");
}

// 注释掉或删除未使用的 bytes_to_hex_string 辅助函数
// fn bytes_to_hex_string(bytes: &[u8]) -> String { ... } 