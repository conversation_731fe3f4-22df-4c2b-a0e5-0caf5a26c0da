use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Duration;

// 卖出请求参数
#[derive(Debug, Serialize)]
struct SellRequest {
    token_address: String,
    percentage: u8,
    slippage_bps: Option<u64>,  // 添加滑点参数
    tip_percentage: Option<f64>,
    tip_fixed: Option<f64>,
    priority_fee: Option<u64>,
    follow_system_defaults: Option<bool>,
}

// 卖出响应结构
#[derive(Debug, Deserialize)]
struct SellResponse {
    success: bool,
    message: String,
    signatures: Option<Vec<String>>,
    error: Option<String>,
    status: String,             // 交易状态
    token_address: String,      // 代币地址
    token_symbol: Option<String>, // 代币符号
    sell_percentage: u8,        // 卖出百分比
    sell_amount: f64,           // 卖出数量（UI单位）
    sell_amount_raw: u64,       // 卖出数量（原始单位）
    tip_amount: Option<f64>,    // 小费数量（SOL）
    slippage_bps: u64,          // 滑点（基点）
    priority_fee: Option<u64>,  // 优先费
    submitted_at: String,       // 提交时间
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建HTTP客户端
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .build()?;
    
    // API服务器地址
    let api_url = "http://localhost:3000/api/sell";
    
    // USDC代币地址 (示例用，实际使用时替换为要卖出的代币地址)
    let token_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
    
    // 构建卖出请求
    let sell_request = SellRequest {
        token_address: token_address.to_string(),
        percentage: 25, // 卖出25%
        slippage_bps: Some(100), // 设置1%的滑点 (100基点 = 1%)
        tip_percentage: Some(1.0), // 使用交易金额1%作为小费
        tip_fixed: None, // 不使用固定小费
        priority_fee: Some(10000), // 设置优先费
        follow_system_defaults: Some(true), // 使用系统默认配置
    };
    
    println!("发送卖出请求:");
    println!("代币地址: {}", token_address);
    println!("卖出比例: {}%", sell_request.percentage);
    println!("滑点设置: {:?} 基点 ({}%)", 
             sell_request.slippage_bps, 
             sell_request.slippage_bps.map(|bps| bps as f64 / 100.0).unwrap_or(0.0));
    println!("小费比例: {:?}%", sell_request.tip_percentage);
    println!("优先费: {:?} micro-lamports", sell_request.priority_fee);
    
    // 发送请求
    println!("\n发送请求到: {}", api_url);
    let response = client.post(api_url)
        .json(&sell_request)
        .send()
        .await?;
    
    // 处理响应
    if response.status().is_success() {
        let sell_response: SellResponse = response.json().await?;
        
        println!("\n收到响应:");
        println!("成功: {}", sell_response.success);
        println!("消息: {}", sell_response.message);
        
        if sell_response.success {
            if let Some(signatures) = sell_response.signatures {
                println!("交易签名:");
                for (i, signature) in signatures.iter().enumerate() {
                    println!("  {}: {}", i+1, signature);
                    println!("    查看链接: https://solscan.io/tx/{}", signature);
                }
            }
        } else if let Some(error) = sell_response.error {
            println!("错误: {}", error);
        }
        
        // 显示详细交易信息
        println!("\n--- 交易详情 ---");
        println!("交易状态: {}", sell_response.status);
        println!("代币地址: {}", sell_response.token_address);
        if let Some(symbol) = sell_response.token_symbol {
            println!("代币符号: {}", symbol);
        }
        println!("卖出百分比: {}%", sell_response.sell_percentage);
        println!("卖出数量: {} (UI单位)", sell_response.sell_amount);
        println!("卖出数量: {} (原始单位)", sell_response.sell_amount_raw);
        if let Some(tip) = sell_response.tip_amount {
            println!("小费数量: {} SOL", tip);
        }
        println!("滑点设置: {} 基点 ({}%)", sell_response.slippage_bps, sell_response.slippage_bps as f64 / 100.0);
        if let Some(fee) = sell_response.priority_fee {
            println!("优先费: {} 微lamports", fee);
        }
        println!("提交时间: {}", sell_response.submitted_at);
    } else {
        println!("\n请求失败:");
        println!("状态码: {}", response.status());
        println!("错误信息: {}", response.text().await?);
    }
    
    // 演示不同滑点设置的示例
    println!("\n不同滑点值示例:");
    println!("- 0.5% 滑点 = 50 基点");
    println!("- 1% 滑点 = 100 基点");
    println!("- 5% 滑点 = 500 基点");
    println!("- 10% 滑点 = 1000 基点");
    println!("- 最大50% 滑点 = 5000 基点");
    println!("\n波动较大的代币建议使用更高的滑点值，如5-10%");
    
    Ok(())
} 