use anyhow::Result;
use copy_trading_bot::common::rpc::ZeroSlotClient;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    signature::{Keypair, read_keypair_file},
    system_instruction,
    transaction::Transaction,
    commitment_config::CommitmentConfig,
};
use std::{env, str::FromStr, sync::Arc};
use dotenv::dotenv;

#[tokio::main]
async fn main() -> Result<()> {
    // 加载环境变量
    dotenv().ok();
    
    // 从环境变量获取API密钥
    let api_key = env::var("ZERO_SLOT_API_KEY")
        .expect("ZERO_SLOT_API_KEY环境变量未设置");
    
    println!("创建0slot客户端...");
    let zero_slot = ZeroSlotClient::new(&api_key, None);
    
    // 测试服务器速度
    println!("测试服务器速度...");
    let speeds = ZeroSlotClient::test_server_speeds().await?;
    println!("服务器速度测试结果:");
    for (server, duration) in &speeds {
        println!("  {} - {}ms", server, duration.as_millis());
    }
    
    // 获取最快的服务器
    let fastest_server = ZeroSlotClient::get_fastest_server().await?;
    println!("最快的服务器: {}", fastest_server);
    
    // 创建更新后的客户端使用最快的服务器
    let zero_slot = ZeroSlotClient::new(&api_key, Some(fastest_server));
    
    // 加载钱包
    let private_key_str = env::var("PRIVATE_KEY").expect("PRIVATE_KEY环境变量未设置");
    let keypair_bytes = solana_sdk::bs58::decode(&private_key_str)
        .into_vec()
        .expect("无法解码PRIVATE_KEY");
    let wallet = Keypair::from_bytes(&keypair_bytes).expect("无法从PRIVATE_KEY创建钱包");
    
    // 连接到Solana RPC获取最新区块哈希
    let rpc_url = env::var("RPC_URL").expect("RPC_URL环境变量未设置");
    let rpc_client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));
    
    println!("获取最新区块哈希...");
    let blockhash = rpc_client.get_latest_blockhash()?;
    
    // 创建一个转账交易 - 发送0.001 SOL到0slot小费账户
    let tip_account = solana_sdk::pubkey::Pubkey::from_str("6fQaVhYZA4w3MBSXjJ81Vf6W1EDYeUPXpgVQ6UQyU1Av")?;
    let transfer_amount = 1_000_000; // 0.001 SOL (最小小费要求)
    
    let transfer_instruction = system_instruction::transfer(
        &wallet.pubkey(),
        &tip_account,
        transfer_amount,
    );
    
    let transaction = Transaction::new_signed_with_payer(
        &[transfer_instruction],
        Some(&wallet.pubkey()),
        &[&wallet],
        blockhash,
    );
    
    // 发送交易到0slot
    println!("发送交易到0slot服务...");
    let signature = zero_slot.send_transaction(&transaction).await?;
    
    println!("交易已提交，签名: {}", signature);
    println!("可以在Solana浏览器查看: https://explorer.solana.com/tx/{}", signature);
    
    Ok(())
} 