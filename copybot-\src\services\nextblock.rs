use anyhow::{anyhow, Result};
use solana_sdk::{transaction::Transaction, pubkey::Pubkey};
use reqwest::{Client, header::{HeaderMap, HeaderValue, CONTENT_TYPE, AUTHORIZATION}};
use std::env;
use rand::seq::SliceRandom;
use std::time::Duration;
use log::{debug, error, warn, info, trace};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::str::FromStr;
use bincode;
use base64;

// NextBlock API响应结构
#[derive(Debug, Deserialize)]
pub struct NextBlockResponse {
    /// 交易签名信息（成功时）
    pub signature: Option<String>,
    /// 错误信息（失败时）
    pub error: Option<String>,
}

// NextBlock API错误结构
#[derive(Debug, Deserialize)]
pub struct NextBlockErrorResponse {
    /// 错误信息
    pub error: String,
}

// NextBlock错误枚举
#[derive(Debug)]
pub enum NextBlockError {
    NotEnabled(String),
    SerializationError(String),
    AuthorizationError(String), 
    RequestSendingError(String),
    ConnectionError(String),
    TimeoutError(String),
    ResponseReadingError(String),
    ResponseParsingError(String),
    MaxRetriesExceeded(String),
    ApiError { message: String, status: u16 },
}

// NextBlock提交配置
pub struct NextBlockConfig {
    /// API端点（主端点）
    pub primary_endpoint: String,
    /// API备用端点
    pub backup_endpoint: String,
    /// API令牌
    pub token: String,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试超时（毫秒）
    pub retry_timeout_ms: u64,
    /// 详细日志模式
    pub verbose_logging: bool,
}

impl Default for NextBlockConfig {
    fn default() -> Self {
        // 检查是否启用详细日志模式
        let verbose_logging = env::var("NEXTBLOCK_VERBOSE").unwrap_or_default() == "true" 
                           || env::var("LOG_MODE").unwrap_or_default() == "infu";
        
        // 尝试从环境变量获取端点，如果未设置则使用默认值
        let primary_endpoint = env::var("NEXTBLOCK_PRIMARY_ENDPOINT")
            .unwrap_or_else(|_| "https://ny.nextblock.io/api/v2/submit".to_string());
            
        let backup_endpoint = env::var("NEXTBLOCK_BACKUP_ENDPOINT")
            .unwrap_or_else(|_| "https://fra.nextblock.io/api/v2/submit".to_string());
        
        Self {
            primary_endpoint,
            backup_endpoint,
            token: env::var("NEXTBLOCK_TOKEN").unwrap_or_default(),
            max_retries: 3,
            retry_timeout_ms: 1000,
            verbose_logging,
        }
    }
}

// NextBlock小费地址列表
const NEXTBLOCK_TIP_ADDRESSES: [&str; 8] = [
    "NextbLoCkVtMGcV47JzewQdvBpLqT9TxQFozQkN98pE",
    "NexTbLoCkWykbLuB1NkjXgFWkX9oAtcoagQegygXXA2",
    "NeXTBLoCKs9F1y5PJS9CKrFNNLU1keHW71rfh7KgA1X",
    "NexTBLockJYZ7QD7p2byrUa6df8ndV2WSd8GkbWqfbb",
    "neXtBLock1LeC67jYd1QdAa32kbVeubsfPNTJC1V5At",
    "nEXTBLockYgngeRmRrjDV31mGSekVPqZoMGhQEZtPVG",
    "NEXTbLoCkB51HpLBLojQfpyVAMorm3zzKg7w9NFdqid",
    "nextBLoCkPMgmG8ZgJtABeScP35qLa2AMCNKntAP7Xc"
];

/// 随机选择一个NextBlock小费地址
pub fn get_random_nextblock_tip_address() -> &'static str {
    let mut rng = rand::thread_rng();
    NEXTBLOCK_TIP_ADDRESSES.choose(&mut rng).unwrap()
}

/// NextBlock交易提交服务
pub struct NextBlockService {
    client: Client,
    config: NextBlockConfig,
}

impl NextBlockService {
    /// 创建新的NextBlock服务实例
    pub fn new(config: Option<NextBlockConfig>) -> Self {
        let config = config.unwrap_or_default();

        // 验证小费地址格式的特殊处理
        for &address in NEXTBLOCK_TIP_ADDRESSES.iter() {
            // 特殊检查NextBlock地址格式而不是直接使用Pubkey::from_str
            // NextBlock地址是特殊格式，不是标准Solana地址
            if config.verbose_logging {
                debug!("NextBlock小费地址: {} (特殊格式，已接受)", address);
            }
        }
        
        // 确保令牌已设置，只在详细日志模式下记录详情
        if config.token.is_empty() {
            error!("NextBlock令牌未设置，将使用环境变量NEXTBLOCK_TOKEN");
        } else {
            debug!("NextBlock令牌已设置，长度: {}", config.token.len());
        }
        
        // 创建HTTP客户端，设置超时
        let client = Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .expect("创建HTTP客户端失败");
            
        if config.verbose_logging {
            debug!("NextBlock服务实例已创建，主端点: {}, 备用端点: {}", 
                  config.primary_endpoint, config.backup_endpoint);
        } else {
            debug!("NextBlock服务实例已创建");
        }
        
        Self { client, config }
    }
    
    /// 检查NextBlock服务是否已启用
    pub fn is_enabled() -> bool {
        // 使用与主程序同样的环境变量名称
        let enabled = env::var("USE_NEXTBLOCK").unwrap_or_default() == "true";
        if enabled {
            let token = env::var("NEXTBLOCK_TOKEN").unwrap_or_default();
            let token_status = if token.is_empty() {
                "未设置"
            } else {
                "已设置"
            };
            
            let primary_endpoint = env::var("NEXTBLOCK_PRIMARY_ENDPOINT")
                .unwrap_or_else(|_| "https://ny.nextblock.io/api/v2/submit".to_string());
                
            // trace!("NextBlock服务已启用 - 端点: {}, 令牌: {}", primary_endpoint, token_status);
            
            let verbose = env::var("NEXTBLOCK_VERBOSE").unwrap_or_default() == "true" 
                       || env::var("LOG_MODE").unwrap_or_default() == "infu";
            if verbose {
                // debug!("NextBlock服务已启用详细日志模式");
            }
        } else {
            // debug!("NextBlock服务未启用");
        }
        enabled
    }
    
    /// 尝试在单个端点提交交易
    async fn try_submit_to_endpoint(&self, transaction: &Transaction, endpoint: &str, headers: &HeaderMap) -> Result<String> {
        let config = &self.config;
        
        // 序列化交易
        let serialized_tx = match bincode::serialize(transaction) {
            Ok(data) => {
                if config.verbose_logging {
                    debug!("交易序列化成功: {} 字节", data.len());
                }
                base64::encode(&data)
            },
            Err(e) => {
                error!("序列化交易失败: {}", e);
                return Err(anyhow!("Failed to serialize transaction: {}", e));
            }
        };
        
        // 构建请求参数 - 严格按照官方API格式
        let req_body = json!({
            "transaction": {
                "content": serialized_tx  // 交易放在嵌套的content字段中
            },
            "skipPreflight": true,
            "maxRetries": 20,
            "encoding": "base64"
        });
        
        if config.verbose_logging {
            debug!("请求体已创建 - 端点：{}", endpoint);
            debug!("请求体JSON: {}", serde_json::to_string(&req_body).unwrap_or_default());
        }
        
        // 开始时间
        let start = std::time::Instant::now();
        
        // 最大重试次数
        let max_retries = self.config.max_retries;
        let mut last_error = None;
        
        // 重试循环
        for retry in 0..=max_retries {
            if retry > 0 {
                // 重试延迟 (毫秒)
                let delay = self.config.retry_timeout_ms * retry as u64;
                if config.verbose_logging {
                    debug!("第 {} 次重试，等待 {} 毫秒...", retry, delay);
                }
                tokio::time::sleep(Duration::from_millis(delay)).await;
            }
            
            // 请求开始时间
            let req_start = std::time::Instant::now();
            
            if config.verbose_logging {
                debug!("向端点 {} 发送请求 (重试 {}/{})", endpoint, retry, max_retries);
            }
            
            // 发送请求
            let request_builder = self.client.post(endpoint)
                .headers(headers.clone())
                .json(&req_body);
                
            // 如果是详细日志模式，输出完整请求内容
            if config.verbose_logging {
                debug!("发送的HTTP请求: {:?}", request_builder);
                debug!("请求头: {:?}", headers);
                debug!("请求体: {}", serde_json::to_string(&req_body).unwrap_or_default());
            }
            
            match request_builder.send().await {
                Ok(response) => {
                    let elapsed = req_start.elapsed();
                    
                    let status = response.status();
                    
                    // 输出详细的响应信息
                    // debug!("响应状态码: {}", status);
                    // debug!("响应头: {:?}", response.headers());
                    
                    if status.is_success() {
                        // 请求成功，解析响应
                        match response.text().await {
                            Ok(text) => {
                                if config.verbose_logging {
                                    // debug!("收到成功响应: HTTP {}, 耗时: {:?}", status, elapsed);
                                    // debug!("响应内容: {}", text);
                                }
                                
                                // 始终记录响应内容，即使非详细日志模式
                                // trace!("NextBlock API响应: HTTP {}, 内容: {}", status, text);
                                
                                match serde_json::from_str::<NextBlockResponse>(&text) {
                                    Ok(result) => {
                                        // 成功信息始终详细显示
                                        if let Some(signature) = &result.signature {
                                            trace!("NextBlock交易提交成功: {}", signature);
                                            return Ok(signature.clone());
                                        } else if let Some(error) = &result.error {
                                            // 有错误消息但状态码是成功
                                            error!("NextBlock API返回成功状态码但有错误信息: {}", error);
                                            last_error = Some(anyhow!("NextBlock API返回成功状态码但有错误: {}", error));
                                        } else {
                                            // 没有ID也没有错误消息
                                            error!("NextBlock API返回成功状态码但没有signature或错误信息，响应内容: {}", text);
                                            
                                            // 尝试作为普通JSON解析，看看有什么字段
                                            match serde_json::from_str::<serde_json::Value>(&text) {
                                                Ok(value) => {
                                                    if let Some(obj) = value.as_object() {
                                                        let keys = obj.keys().map(|k| k.to_string()).collect::<Vec<_>>();
                                                        error!("响应JSON包含以下字段: {}", keys.join(", "));
                                                        
                                                        // 检查是否有其他可能的签名字段
                                                        for key in ["txid", "sig", "id", "hash", "transaction_id"].iter() {
                                                            if let Some(value) = obj.get(*key) {
                                                                if let Some(s) = value.as_str() {
                                                                    trace!("找到潜在的签名字段 {}: {}", key, s);
                                                                    return Ok(s.to_string());
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                Err(e) => {
                                                    error!("响应内容不是有效的JSON: {}", e);
                                                }
                                            }
                                            
                                            last_error = Some(anyhow!("NextBlock API返回成功状态码但没有签名"));
                                        }
                                    },
                                    Err(e) => {
                                        // 解析错误始终详细显示
                                        error!("解析响应JSON失败: {}, 响应内容: {}", e, text);
                                        last_error = Some(anyhow!("解析响应JSON失败: {}, 响应内容: {}", e, text));
                                    }
                                }
                            },
                            Err(e) => {
                                // 读取响应错误始终详细显示
                                error!("读取响应正文失败: {}", e);
                                last_error = Some(anyhow!("读取响应正文失败: {}", e));
                            }
                        }
                    } else {
                        // 请求返回非成功状态码
                        match response.text().await {
                            Ok(text) => {
                                if config.verbose_logging {
                                    debug!("收到错误响应: HTTP {}, 耗时: {:?}", status, elapsed);
                                    debug!("错误响应内容: {}", text);
                                }
                                
                                // 尝试解析为错误结构体
                                match serde_json::from_str::<NextBlockErrorResponse>(&text) {
                                    Ok(error) => {
                                        // API错误信息始终详细显示
                                        error!("NextBlock API返回错误: {}", error.error);
                                        last_error = Some(anyhow!("NextBlock API错误: {}", error.error));
                                    },
                                    Err(_) => {
                                        // 解析失败的API错误始终详细显示
                                        error!("NextBlock API返回错误: HTTP {}, 响应内容: {}", status, text);
                                        last_error = Some(anyhow!("NextBlock API错误: HTTP {}, 响应: {}", status, text));
                                    }
                                }
                            },
                            Err(e) => {
                                // 读取错误响应失败始终详细显示
                                error!("读取错误响应正文失败: {}", e);
                                last_error = Some(anyhow!("读取错误响应正文失败: {}", e));
                            }
                        }
                    }
                },
                Err(e) => {
                    let elapsed = req_start.elapsed();
                    
                    // 根据错误类型分类
                    if e.is_timeout() {
                        error!("请求超时: {}, 耗时: {:?}", e, elapsed);
                        last_error = Some(anyhow!("NextBlock请求超时: {}", e));
                    } else if e.is_connect() {
                        error!("连接错误: {}, 耗时: {:?}", e, elapsed);
                        last_error = Some(anyhow!("NextBlock连接错误: {}", e));
                    } else {
                        error!("请求发送失败: {}, 耗时: {:?}", e, elapsed);
                        last_error = Some(anyhow!("NextBlock请求失败: {}", e));
                    }
                }
            }
        }
        
        // 总耗时
        let total_elapsed = start.elapsed();
        
        // 如果所有重试都失败，返回最后一个错误
        error!("NextBlock提交到端点 {} 失败，已达到最大重试次数: {}, 总耗时: {:?}", 
              endpoint, self.config.max_retries, total_elapsed);
        Err(last_error.unwrap_or_else(|| anyhow!("NextBlock请求失败，无具体错误信息")))
    }
    
    /// 提交交易到NextBlock服务（尝试多个端点）
    pub async fn submit_transaction(&self, transaction: &Transaction) -> Result<String> {
        // 只有在启用NextBlock时才进行真正的提交
        if !Self::is_enabled() {
            return Err(anyhow!("NextBlock not enabled"));
        }

        // 准备通用请求头
        let mut headers = reqwest::header::HeaderMap::new();
        
        // 必须的Content-Type头
        headers.insert(
            reqwest::header::CONTENT_TYPE, 
            reqwest::header::HeaderValue::from_static("application/json")
        );
        
        // 添加授权头 (使用环境变量中的令牌)
        let token = if !self.config.token.is_empty() {
            self.config.token.clone()
        } else {
            env::var("NEXTBLOCK_TOKEN").unwrap_or_default()
        };
        
        if !token.is_empty() {
            // 直接使用令牌作为授权头的值，无需添加Bearer前缀
            // NextBlock API要求令牌格式为"trial开头的字符串，不需要Bearer前缀"
            match reqwest::header::HeaderValue::from_str(&token) {
                Ok(auth_value) => {
                    headers.insert(reqwest::header::AUTHORIZATION, auth_value);
                    if self.config.verbose_logging {
                        debug!("已添加授权头 (无Bearer前缀，直接使用令牌)");
                    }
                },
                Err(e) => {
                    error!("创建授权头失败: {}", e);
                    return Err(anyhow!("创建授权头失败: {}", e));
                }
            }
        } else {
            error!("无法提交到NextBlock: NEXTBLOCK_TOKEN环境变量未设置");
            return Err(anyhow!("无法提交到NextBlock: NEXTBLOCK_TOKEN环境变量未设置"));
        }
        
        // 首先尝试主端点
        let primary_result = self.try_submit_to_endpoint(transaction, &self.config.primary_endpoint, &headers).await;
        
        match primary_result {
            Ok(signature) => {
                // 主端点成功
                trace!("通过主端点 {} 成功提交到NextBlock", self.config.primary_endpoint);
                return Ok(signature);
            },
            Err(primary_error) => {
                // 主端点失败，尝试备用端点
                warn!("主端点提交失败: {}，尝试备用端点...", primary_error);
                
                let backup_result = self.try_submit_to_endpoint(transaction, &self.config.backup_endpoint, &headers).await;
                
                match backup_result {
                    Ok(signature) => {
                        // 备用端点成功
                        trace!("通过备用端点 {} 成功提交到NextBlock", self.config.backup_endpoint);
                        return Ok(signature);
                    },
                    Err(backup_error) => {
                        // 两个端点都失败
                        error!("NextBlock提交彻底失败 - 主端点错误: {}, 备用端点错误: {}", 
                              primary_error, backup_error);
                        return Err(anyhow!("NextBlock提交失败 - 所有端点均失败: {}", backup_error));
                    }
                }
            }
        }
    }
} 