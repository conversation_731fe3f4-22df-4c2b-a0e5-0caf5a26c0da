use std::{env, sync::Arc, time::Duration};

use anyhow::Result;
use jito_json_rpc_client::jsonrpc_client::rpc_client::RpcClient as JitoRpcClient;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_transaction,
    transaction::{Transaction, VersionedTransaction},
};
use spl_token::{
    ui_amount_to_amount,
};
use spl_token_2022::{
    extension::StateWithExtensionsOwned,
    state::Mint,
};

use std::str::FromStr;
use tokio::time::Instant;

use crate::{
    common::logger::Logger,
    services::jito::{
        self, get_tip_account, init_tip_accounts, wait_for_bundle_confirmation, 
        get_bundle_api_url, get_bundle_api_url_async, get_transactions_api_url,
    },
    core::priority_fees::{
        estimate_tip_cu_config, 
        build_compute_budget_instructions,
        get_optimized_jito_tip
    },
};

// prioritization fee = UNIT_PRICE * UNIT_LIMIT
fn get_unit_price() -> u64 {
    env::var("UNIT_PRICE")
        .ok()
        .and_then(|v| u64::from_str(&v).ok())
        .unwrap_or(1)
}

fn get_unit_limit() -> u32 {
    env::var("UNIT_LIMIT")
        .ok()
        .and_then(|v| u32::from_str(&v).ok())
        .unwrap_or(300_000)
}

pub async fn get_mint_info(
    client: Arc<solana_client::nonblocking::rpc_client::RpcClient>,
    _keypair: Arc<Keypair>,
    _address: &Pubkey,
) -> Result<StateWithExtensionsOwned<Mint>> {
    let _program_client = Arc::new(spl_token_client::client::ProgramRpcClient::new(
        client.clone(),
        spl_token_client::client::ProgramRpcClientSendTransaction,
    ));
    
    // 添加缺失的实现
    todo!("Implement mint info retrieval")
}

pub async fn send_and_confirm_transaction(
    client: Arc<RpcClient>,
    mut txn: Transaction,
    keypair: &Keypair,
    recent_blockhash: solana_sdk::hash::Hash,
    use_jito: bool,
    logger: &Logger,
    trade_amount_lamports: Option<u64>,
) -> Result<Vec<String>> {
    let amount = trade_amount_lamports.unwrap_or(0);
    
    let mut txs: Vec<String> = Vec::new();
    
    // 记录开始时间
    let start_time = Instant::now();
    
    // 检查环境变量是否启用了网络拥堵检测
    let is_congested = std::env::var("NETWORK_CONGESTED")
        .unwrap_or_else(|_| "false".to_string())
        .to_lowercase() == "true";
    
    // 交易是否高优先级
    let is_high_priority = std::env::var("HIGH_PRIORITY_TX")
        .unwrap_or_else(|_| "false".to_string())
        .to_lowercase() == "true";
    
    // 检查区块哈希是否已过期（超过1.5秒）
    let blockhash_age = Instant::now().duration_since(start_time);
    let need_new_blockhash = blockhash_age.as_millis() > 1500;
    
    // 如果有必要，更新交易哈希
    let mut actual_blockhash = recent_blockhash;
    if need_new_blockhash {
        logger.log("区块哈希可能已过期，获取最新区块哈希".to_string());
        // 直接从RPC获取最新哈希
        match client.get_latest_blockhash() {
            Ok(new_hash) => {
                logger.log(format!("更新区块哈希: {} -> {}", actual_blockhash, new_hash));
                actual_blockhash = new_hash;
                
                // 使用新哈希更新交易
                let mut new_message = txn.message.clone();
                new_message.recent_blockhash = actual_blockhash;
                txn = Transaction::new_unsigned(new_message);
                txn.sign(&[keypair], actual_blockhash);
            },
            Err(e) => {
                logger.log(format!("获取新区块哈希失败: {:?}，继续使用原哈希", e));
            }
        }
    }
    
    if use_jito {
        // 初始化Jito tip账户
        if let Err(e) = init_tip_accounts().await {
            logger.log(format!("初始化Jito tip账户失败: {:?}，但继续执行", e));
        }
        
        let tip_account = match get_tip_account().await {
            Ok(account) => account,
            Err(e) => {
                logger.log(format!("获取Jito tip账户失败: {:?}，回退到普通交易", e));
                // 如果无法获取tip账户，回退到普通交易处理
                let sig = common::rpc::send_txn(&client, &txn, true)?;
                logger.log(format!("已回退到普通交易，签名: {:#?}", sig));
                txs.push(sig.to_string());
                return Ok(txs);
            }
        };
        
        // 获取完整的Bundle API URL (使用异步方法获取最快节点)
        let mut bundle_api_url = match get_bundle_api_url_async().await {
            Ok(url) => url,
            Err(e) => {
                logger.log(format!("获取最佳Bundle API URL失败: {:?}，使用默认URL", e));
                get_bundle_api_url()
            }
        };
        logger.log(format!("使用Jito Bundle API: {}", bundle_api_url));
        
        // 创建Jito客户端 - 使用Bundle API (暂时未使用)
        #[allow(unused_variables)]
        let jito_client = Arc::new(JitoRpcClient::new(bundle_api_url.clone()));
        
        // 使用动态小费策略计算Jito小费
        // 根据Jito官方文档，sendBundle情况下只考虑Jito小费
        let mut tip = match std::env::var("JITO_TIP_VALUE") {
            Ok(val) => f64::from_str(&val).unwrap_or(0.0005),
            Err(_) => get_optimized_jito_tip(amount, is_high_priority, is_congested),
        };
        
        // 确保小费至少为1000 lamports (Jito文档中的最小值)
        let min_tip_lamports = 1000;
        
        // 应用上限 - 根据官方文档推荐
        tip = tip.min(1.0).max(min_tip_lamports as f64 / 1_000_000_000.0);
        let tip_lamports = ui_amount_to_amount(tip, spl_token::native_mint::DECIMALS);
        
        logger.log(format!(
            "Jito tip: account: {}, 金额: {} SOL ({} lamports)",
            tip_account, tip, tip_lamports
        ));
        
        // 创建小费交易 - 确保使用最新区块哈希
        let tip_transaction = system_transaction::transfer(
            keypair,
            &tip_account,
            tip_lamports,
            actual_blockhash, // 使用最新哈希
        );
        
        // 构建Jito Bundle (主交易 + tip交易)
        // 根据官方文档，Bundle最多包含5个交易
        // 将小费交易放在最后，确保主交易完成
        let bundle: Vec<VersionedTransaction> = vec![
            VersionedTransaction::from(txn.clone()),
            VersionedTransaction::from(tip_transaction),
        ];
        
        // 发送Bundle - 注意：Jito使用JSON-RPC需要base58或base64编码
        logger.log("发送交易Bundle到Jito Block Engine...".to_string());
        
        // 准备bundle请求数据格式
        use serde_json::{json, Value};
        use base58::{FromBase58, ToBase58};
        use reqwest::Client;
        
        let encoded_txns: Vec<String> = bundle.iter().map(|tx| {
            let serialized = bincode::serialize(tx).expect("序列化交易失败");
            serialized.to_base58()
        }).collect();
        
        let bundle_data = json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendBundle",
            "params": [encoded_txns]
        });
        
        // 创建HTTP客户端
        let http_client = Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .unwrap_or_else(|_| Client::new());
        let start_send = Instant::now();
        
        // 添加错误处理和重试逻辑
        let mut retry_count = 0;
        let max_retries = 3;
        let mut bundle_id = String::new();
        
        while retry_count < max_retries {
            match http_client.post(&bundle_api_url)
                .header("Content-Type", "application/json")
                .json(&bundle_data)
                .send()
                .await {
                Ok(response) => {
                    match response.json::<Value>().await {
                        Ok(json_response) => {
                            if let Some(result) = json_response.get("result") {
                                let id = result.as_str().unwrap_or("unknown").to_string();
                                // logger.log(format!("Bundle发送成功，ID: {}", id));
                                bundle_id = id;
                                break;
                            } else if let Some(error) = json_response.get("error") {
                                retry_count += 1;
                                // logger.log(format!("Bundle发送失败 (尝试 {}/{}): {:?}", retry_count, max_retries, error));
                            } else {
                                retry_count += 1;
                                // logger.log(format!("Bundle发送响应格式异常 (尝试 {}/{}): {:?}", retry_count, max_retries, json_response));
                            }
                        },
                        Err(e) => {
                            retry_count += 1;
                            // logger.log(format!("解析Bundle响应失败 (尝试 {}/{}): {:?}", retry_count, max_retries, e));
                        }
                    }
                },
                Err(e) => {
                    retry_count += 1;
                    logger.log(format!("Bundle发送请求失败 (尝试 {}/{}): {:?}", retry_count, max_retries, e));
                    
                    // 如果是超时错误，尝试使用另一个节点
                    if e.is_timeout() && retry_count < max_retries {
                        logger.log("检测到超时，尝试使用另一个节点...".to_string());
                        match get_bundle_api_url_async().await {
                            Ok(new_url) => {
                                if new_url != bundle_api_url {
                                    logger.log(format!("切换到新节点: {}", new_url));
                                    // 更新URL并继续下一次重试
                                    bundle_api_url = new_url;
                                }
                            },
                            Err(e) => {
                                logger.log(format!("获取新节点失败: {:?}", e));
                            }
                        }
                    }
                }
            }
            
            if retry_count == max_retries {
                logger.log("达到最大重试次数，回退到普通交易".to_string());
                // 如果Jito发送失败，回退到普通交易处理
                let sig = common::rpc::send_txn(&client, &txn, true)?;
                logger.log(format!("已回退到普通交易，签名: {:#?}", sig));
                txs.push(sig.to_string());
                return Ok(txs);
            }
            
            // 等待短暂时间后重试
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
        
        logger.log(format!("Bundle ID: {}", bundle_id));
        logger.log(format!("发送耗时: {:?}", start_send.elapsed()));
        
        // 直接返回bundle_id作为结果，不等待确认
        // 根据教程，我们可以简化等待确认部分，直接返回bundle_id
        txs.push(bundle_id);
        
        // 如果需要等待确认，可以放在另一个单独的函数中处理
        // 参考教程，不必立即等待确认结果
    } else {
        // 计算CU配置并构建优先级费用
        let (cu_limit, cu_price) = estimate_tip_cu_config(amount);
        logger.log(format!(
            "设置计算预算: CU Limit = {}, CU Price = {}",
            cu_limit, cu_price
        ));
        
        // 使用普通RPC发送交易
        let sig = common::rpc::send_txn(&client, &txn, true)?;
        logger.log(format!("交易签名: {:#?}", sig));
        txs.push(sig.to_string());
    }

    Ok(txs)
}

