{"name": "Rust项目规则", "description": "适用于Solana Rust项目的代码规范和建议", "rules": [{"name": "避免过大文件", "description": "文件不应超过1500行，保持代码可维护性", "severity": "warning", "selector": "file.rust", "condition": "file.lineCount > 1500", "message": "文件超过1500行，建议拆分为多个模块"}, {"name": "导入顺序", "description": "标准库导入应位于第三方库之前", "severity": "suggestion", "selector": "file.rust", "pattern": "use (tokio|serde|solana|spl|anchor|raydium).*;\nuse std::.*;"}, {"name": "缺少错误处理", "description": "避免使用unwrap()而不处理错误", "severity": "warning", "selector": "file.rust", "pattern": "\\.unwrap\\(\\)"}, {"name": "注释规范", "description": "公共API应有文档注释", "severity": "suggestion", "selector": "function.public", "condition": "!function.hasDocComment", "message": "公共函数应该有文档注释"}, {"name": "避免过长函数", "description": "函数不应过长", "severity": "warning", "selector": "function", "condition": "function.lineCount > 50", "message": "函数超过50行，考虑拆分为更小的函数"}, {"name": "Result处理", "description": "推荐使用?操作符处理Result", "severity": "suggestion", "selector": "file.rust", "pattern": "if let Err\\(.*\\) = .*\\{[\\s\\S]*?\\}"}, {"name": "Solana版本一致性", "description": "确保Solana依赖版本一致", "severity": "error", "selector": "file.toml", "pattern": "solana-[a-z\\-]+ = \"[^=]"}, {"name": "测试文件位置", "description": "所有测试文件应放在tests文件夹中", "severity": "error", "selector": "file.rust", "condition": "file.path.includes('_test.rs') && !file.path.startsWith('tests/')", "message": "测试文件应放在tests文件夹中"}, {"name": "买卖逻辑错误参考", "description": "修复买卖逻辑错误时应参考tests文件夹中的测试", "severity": "suggestion", "selector": "function", "condition": "function.name.includes('buy') || function.name.includes('sell')", "message": "买卖逻辑实现时，请参考tests文件夹中的real_buy_transaction_test.rs和real_sell_transaction_test.rs测试文件"}, {"name": "错误修复分析原则", "description": "修复错误前应先分析错误原因和上下文", "severity": "warning", "selector": "file.rust", "condition": "true", "message": "遇到错误时，必须先完整阅读相关文件内容，分析错误上下文，找到根本原因，再进行修复，避免盲目猜测或部分理解后的草率修复"}]}