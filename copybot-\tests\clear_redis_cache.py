'''
import redis

# Redis 连接配置
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
# 如果您的 Redis 有密码，请在这里填写
# REDIS_PASSWORD = 'your_password'
# 要清除的数据库编号，0 是默认数据库
REDIS_DB = 0

def clear_redis_cache():
    """
    连接到 Redis 服务器并清除当前选择的数据库 (DB 0 by default)。
    """
    try:
        # 建立连接
        # 如果有密码，使用: r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, password=REDIS_PASSWORD)
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB)
        
        # Ping 服务器以检查连接
        r.ping()
        print(f"成功连接到 Redis 服务器: {REDIS_HOST}:{REDIS_PORT}, 数据库: {REDIS_DB}")
        
        # 清除当前数据库
        r.flushdb()
        print(f"数据库 {REDIS_DB} 的所有键已被成功清除。")
        
    except redis.exceptions.ConnectionError as e:
        print(f"无法连接到 Redis 服务器: {e}")
    except Exception as e:
        print(f"执行清除操作时发生错误: {e}")

if __name__ == "__main__":
    # 询问用户确认，这是一个危险操作
    confirm = input(f"您确定要清除 Redis 数据库 {REDIS_DB} 在 {REDIS_HOST}:{REDIS_PORT} 上的所有数据吗? (yes/no): ")
    if confirm.lower() == 'yes':
        clear_redis_cache()
    else:
        print("操作已取消。")
''' 