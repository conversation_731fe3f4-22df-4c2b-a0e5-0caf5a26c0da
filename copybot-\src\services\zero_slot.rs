use anyhow::{anyhow, Result};
use solana_sdk::{transaction::Transaction, pubkey::Pubkey};
use std::env;
use std::time::Duration;
use log::{debug, error, warn, info, trace};
use serde_json::{json, Value};
use std::str::FromStr;
use bincode;
use base64;
use rand::seq::SliceRandom;
use uuid;

/// 0slot小费地址列表
const ZERO_SLOT_TIP_ADDRESSES: [&str; 15] = [
    "6fQaVhYZA4w3MBSXjJ81Vf6W1EDYeUPXpgVQ6UQyU1Av", // 0slot_dot_trade.sol
    "4HiwLEP2Bzqj3hM2ENxJuzhcPCdsafwiet3oGkMkuQY4", // 0slot_dot_trade_tip15.sol
    "7toBU3inhmrARGngC7z6SjyP85HgGMmCTEwGNRAcYnEK", // 0slot_dot_trade_tip16.sol
    "8mR3wB1nh4D6J9RUCugxUpc6ya8w38LPxZ3ZjcBhgzws", // 0slot_dot_trade_tip17.sol
    "6SiVU5WEwqfFapRuYCndomztEwDjvS5xgtEof3PLEGm9", // 0slot_dot_trade_tip18.sol
    "TpdxgNJBWZRL8UXF5mrEsyWxDWx9HQexA9P1eTWQ42p", // 0slot_dot_trade_tip19.sol
    "D8f3WkQu6dCF33cZxuAsrKHrGsqGP2yvAHf8mX6RXnwf", // 0slot_dot_trade_tip20.sol
    "GQPFicsy3P3NXxB5piJohoxACqTvWE9fKpLgdsMduoHE", // 0slot_dot_trade_tip21.sol
    "Ey2JEr8hDkgN8qKJGrLf2yFjRhW7rab99HVxwi5rcvJE", // 0slot_dot_trade_tip22.sol
    "4iUgjMT8q2hNZnLuhpqZ1QtiV8deFPy2ajvvjEpKKgsS", // 0slot_dot_trade_tip23.sol
    "3Rz8uD83QsU8wKvZbgWAPvCNDU6Fy8TSZTMcPm3RB6zt", // 0slot_dot_trade_tip24.sol
    "DiTmWENJsHQdawVUUKnUXkconcpW4Jv52TnMWhkncF6t", // 0slot_dot_trade_tip1.sol 
    "HRyRhQ86t3H4aAtgvHVpUJmw64BDrb61gRiKcdKUXs5c", // 0slot_dot_trade_tip2.sol
    "7y4whZmw388w1ggjToDLSBLv47drw5SUXcLk6jtmwixd", // 0slot_dot_trade_tip6.sol
    "J9BMEWFbCBEjtQ1fG5Lo9kouX1HfrKQxeUxetwXrifBw", // 0slot_dot_trade_tip7.sol
];

/// 随机选择一个0slot小费地址
pub fn get_random_zero_slot_tip_address() -> &'static str {
    let mut rng = rand::thread_rng();
    ZERO_SLOT_TIP_ADDRESSES.choose(&mut rng).unwrap()
}

/// 0slot配置
pub struct ZeroSlotConfig {
    /// API端点
    pub endpoint: String,
    /// API密钥
    pub api_key: String,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试超时（毫秒）
    pub retry_timeout_ms: u64,
    /// 详细日志模式
    pub verbose_logging: bool,
}

impl Default for ZeroSlotConfig {
    fn default() -> Self {
        // 检查是否启用详细日志模式
        let verbose_logging = env::var("ZERO_SLOT_VERBOSE").unwrap_or_default() == "true" 
                          || env::var("LOG_MODE").unwrap_or_default() == "infu";
        
        // 固定使用ny1.0slot.trade节点
        let endpoint = "http://ny1.0slot.trade".to_string();
        
        Self {
            endpoint,
            api_key: env::var("ZERO_SLOT_API_KEY").unwrap_or_default(),
            max_retries: 3,
            retry_timeout_ms: 1000,
            verbose_logging,
        }
    }
}

/// 0slot交易提交服务
pub struct ZeroSlotService {
    client: reqwest::Client,
    config: ZeroSlotConfig,
}

impl ZeroSlotService {
    /// 创建新的0slot服务实例
    pub fn new(config: Option<ZeroSlotConfig>) -> Self {
        let config = config.unwrap_or_default();
        
        // 验证小费地址
        for &address in ZERO_SLOT_TIP_ADDRESSES.iter() {
            if let Ok(pubkey) = Pubkey::from_str(address) {
                if config.verbose_logging {
                    debug!("0slot小费地址: {}", pubkey);
                }
            } else if config.verbose_logging {
                warn!("无效的0slot小费地址格式: {}", address);
            }
        }
        
        // 确保API密钥已设置
        if config.api_key.is_empty() {
            error!("0slot API密钥未设置，将使用环境变量ZERO_SLOT_API_KEY");
        } else if config.verbose_logging {
            debug!("0slot API密钥已设置，长度: {}", config.api_key.len());
        }
        
        // 创建HTTP客户端，设置超时
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .expect("创建HTTP客户端失败");
            
        if config.verbose_logging {
            debug!("0slot服务实例已创建，端点: {}", config.endpoint);
        } else {
            debug!("0slot服务实例已创建");
        }
        
        Self { client, config }
    }
    
    /// 检查0slot服务是否已启用
    pub fn is_enabled() -> bool {
        // 使用与主程序同样的环境变量名称
        let enabled = env::var("USE_ZERO_SLOT").unwrap_or_default() == "true";
        if enabled {
            let api_key = env::var("ZERO_SLOT_API_KEY").unwrap_or_default();
            let api_key_status = if api_key.is_empty() {
                "未设置"
            } else {
                "已设置"
            };
            
            let endpoint = env::var("ZERO_SLOT_ENDPOINT")
                .unwrap_or_else(|_| "https://ny.0slot.trade".to_string());
                
            trace!("0slot服务已启用 - 端点: {}, API密钥: {}", endpoint, api_key_status);
            
            let verbose = env::var("ZERO_SLOT_VERBOSE").unwrap_or_default() == "true" 
                       || env::var("LOG_MODE").unwrap_or_default() == "infu";
            if verbose {
                debug!("0slot服务已启用详细日志模式");
            }
        } else {
            debug!("0slot服务未启用");
        }
        enabled
    }
    
    /// 提交交易到0slot服务
    pub async fn submit_transaction(&self, tx: &Transaction) -> Result<String> {
        let result = self.send_transaction(tx).await;
        
        match result {
            Ok(signature) => {
                if self.config.verbose_logging {
                    debug!("交易提交成功: {}", signature);
                }
                Ok(signature)
            }
            Err(e) => {
                error!("交易提交失败: {}", e);
                Err(e)
            }
        }
    }
    
    /// 测试0slot服务器速度
    pub async fn test_server_speeds() -> Result<Vec<(String, Duration)>> {
        let servers = vec![
            "http://ny1.0slot.trade".to_string(),
        ];
        
        let client = reqwest::Client::new();
        let mut results = Vec::new();
        
        for server in servers {
            let start_time = std::time::Instant::now();
            
            // 发送简单请求测试响应时间
            match client.get(&server).timeout(Duration::from_secs(5)).send().await {
                Ok(_) => {
                    let elapsed = start_time.elapsed();
                    results.push((server.clone(), elapsed));
                    if elapsed.as_millis() < 500 {
                        debug!("服务器 {} 响应快速: {}ms", server, elapsed.as_millis());
                    } else {
                        debug!("服务器 {} 响应: {}ms", server, elapsed.as_millis());
                    }
                }
                Err(e) => {
                    // 如果请求失败，添加最大持续时间
                    warn!("服务器 {} 请求失败: {}", server, e);
                    results.push((server, Duration::from_secs(5)));
                }
            }
        }
        
        // 按响应时间排序
        results.sort_by(|a, b| a.1.cmp(&b.1));
        
        Ok(results)
    }
    
    /// 获取最快的服务器节点
    pub async fn get_fastest_server() -> Result<String> {
        let speeds = Self::test_server_speeds().await?;
        
        if speeds.is_empty() {
            return Err(anyhow!("无法获取任何服务器的速度信息"));
        }
        
        // 返回最快的服务器
        debug!("最快的0slot服务器: {} ({}ms)", speeds[0].0, speeds[0].1.as_millis());
        Ok(speeds[0].0.clone())
    }

    /// 发送交易到0slot服务
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        // 只有在启用0slot时才进行真正的提交
        if !Self::is_enabled() {
            return Err(anyhow!("0slot not enabled"));
        }

        // 获取API密钥
        let api_key = if !self.config.api_key.is_empty() {
            self.config.api_key.clone()
        } else {
            env::var("ZERO_SLOT_API_KEY").unwrap_or_default()
        };
        
        if api_key.is_empty() {
            error!("无法提交到0slot: ZERO_SLOT_API_KEY环境变量未设置");
            return Err(anyhow!("无法提交到0slot: ZERO_SLOT_API_KEY环境变量未设置"));
        }

        // 序列化交易为base64
        let serialized_tx = bincode::serialize(tx)?;
        let encoded_tx = base64::encode(&serialized_tx);

        // 构建请求体
        let request_body = json!({
            "jsonrpc": "2.0",
            "id": uuid::Uuid::new_v4().to_string(),
            "method": "sendTransaction",
            "params": [
                encoded_tx,
                {
                    "encoding": "base64"
                }
            ]
        });

        // 构建URL（包含API密钥）
        let url = format!("{}?api-key={}", self.config.endpoint, api_key);
        
        // 发送请求
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;

        let status = response.status();
        let text = response.text().await?;
        
        if status.is_success() {
            // 解析JSON响应
            let parsed: Value = serde_json::from_str(&text)?;
            
            if let Some(result) = parsed.get("result") {
                if let Some(signature) = result.as_str() {
                    debug!("交易提交成功: {}", signature);
                    return Ok(signature.to_string());
                }
            }
            
            error!("0slot响应格式不正确: {}", text);
            Err(anyhow!("0slot响应格式不正确"))
        } else {
            error!("0slot服务返回HTTP错误: {}, 响应: {}", status, text);
            Err(anyhow!("0slot服务HTTP错误: {}", status))
        }
    }
} 