# Pump.fun 协议错误代码对照表

此文档列出了 Pump.fun 协议中可能遇到的错误代码及其中文解释，方便在日志中快速查找和诊断问题。

## 常见错误代码

| 错误代码 (十六进制) | 错误代码 (十进制) | 中文说明 | 可能的解决方案 |
|-------------------|-----------------|--------|--------------|
| 0xbc0 | 3008 | 最小购买数量未满足 | 增加交易滑点容忍度，或增加输入金额 |
| 0xbc1 | 3009 | 流动性不足 | 减少交易金额，或等待流动性增加 |
| 0xbc2 | 3010 | 代币价格超出可接受范围 | 调整滑点设置，或等待价格稳定 |
| 0xbc3 | 3011 | 交易金额过小 | 增加交易金额 |
| 0xbc4 | 3012 | 代币余额不足 | 检查钱包余额，降低跟单比例，或充值所需代币 |
| 0xbc5 | 3013 | 代币授权不足 | 检查并增加代币授权额度 |
| 0xbc6 | 3014 | 交易结构错误 | 检查交易指令参数和账户顺序 |
| 0xbc7 | 3015 | 曲线变量计算错误 | 可能是内部协议错误，等待修复或更新 |
| 0xbc8 | 3016 | 代币铸造限制 | 该代币可能有铸造限制，无法购买 |
| 0xbc9 | 3017 | 交易超时 | 重试交易，可能是因为区块确认时间过长 |
| 0xbca | 3018 | 无效的交易方向 | 检查交易方向设置（买/卖） |
| 0xbcb | 3019 | 代币池关闭或暂停 | 该代币可能暂时不可交易 |
| 0xbcc | 3020 | 重复交易 | 避免在短时间内提交相同交易 |
| 0xbcd | 3021 | 计算单元不足 | 增加交易的计算单元限制 |
| 0xbce | 3022 | 交易费用不足 | 增加交易的优先级费用 |
| 0xbcf | 3023 | 账户所有权错误 | 检查交易涉及的账户是否正确 |
| 0xbd0 | 3024 | 内部程序错误 | 协议内部错误，等待开发者修复 |

## 使用方法

当您在日志中看到类似以下错误时：
```
Transaction simulation failed: Error processing Instruction 2: custom program error: 0xbc4 [9 log messages]
```

可以查询此表得知错误 `0xbc4` 表示**代币余额不足**，解决方案是检查钱包余额，降低跟单比例，或充值所需代币。

## 常见错误诊断流程

1. **交易失败**：首先查看错误代码
2. **余额相关错误**（0xbc3, 0xbc4, 0xbc5）：检查钱包SOL和代币余额
3. **价格相关错误**（0xbc0, 0xbc1, 0xbc2）：调整滑点或交易金额
4. **技术错误**（0xbc6及以上）：检查交易结构或咨询技术支持

## 注意事项

- 错误代码可能会随着协议更新而变化
- 某些错误可能有多种原因，需要结合具体情况分析
- 当遇到未记录的错误代码时，建议查询最新的Pump.fun文档 