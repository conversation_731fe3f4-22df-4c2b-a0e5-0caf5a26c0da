use anyhow::Result;
use std::path::Path;
use std::env;
use dotenv;
use copy_trading_bot::services::block_monitor::BlockMonitor;

#[tokio::main]
async fn main() -> Result<()> {
    // 加载环境变量(如果有.env文件)
    if Path::new(".env").exists() {
        dotenv::dotenv().ok();
    }

    // 获取WebSocket URL，优先从命令行参数获取，其次从环境变量，最后使用默认值
    let args: Vec<String> = env::args().collect();
    let ws_url = if args.len() > 1 {
        args[1].clone()
    } else {
        env::var("HELIUS_WS_URL").unwrap_or_else(|_| 
            "wss://thrilling-neat-dream.solana-mainnet.quiknode.pro/8e5fc9c0d3b44eda288655d087f525dbaf4dbfb2".to_string())
    };

    println!("启动 BlockMonitor (并行监听 processed & confirmed)");
    println!("WebSocket URL: {}", ws_url);
    
    // 创建监控器
    let monitor = BlockMonitor::new(ws_url);
    monitor.start_monitoring().await?;
    
    Ok(())
} 