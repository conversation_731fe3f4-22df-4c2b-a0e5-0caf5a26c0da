use anyhow::{Result, anyhow};
use redis::{Client as RedisClient, AsyncCommands};
use futures_util::StreamExt;
use dotenv::dotenv;
use std::env;
use std::fs::{self, File};
use std::io::Write;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::time::sleep;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化环境
    dotenv().ok();
    println!("Redis数据导出脚本启动");
    
    // 从环境变量获取Redis URL
    let redis_url = env::var("REDIS_URL").unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());
    println!("连接到Redis: {}", redis_url);
    
    // 创建输出目录
    let output_dir = "redis_data_export";
    fs::create_dir_all(output_dir)?;
    println!("创建输出目录: {}", output_dir);
    
    // 连接Redis
    let client = RedisClient::open(redis_url)?;
    let mut conn = client.get_async_connection().await?;
    println!("Redis连接成功");
    
    // 创建PubSub连接
    let pubsub_conn = client.get_async_connection().await?;
    let mut pubsub = pubsub_conn.into_pubsub();
    
    // 订阅所有键空间事件
    if pubsub.psubscribe("__keyspace@*__:*").await.is_ok() {
        println!("Redis键空间通知订阅成功，开始监听...");
        
        // 创建一个日志文件以记录所有事件
        let log_path = format!("{}/redis_events_log.txt", output_dir);
        let mut log_file = File::create(&log_path)?;
        writeln!(log_file, "时间戳 | 键名 | 操作 | 值")?;
        
        // 处理消息
        let mut stream = pubsub.on_message();
        
        println!("等待Redis事件...");
        println!("每个事件将保存在 {} 目录下", output_dir);
        println!("按 Ctrl+C 停止");
        
        while let Some(msg) = stream.next().await {
            // 获取当前时间戳
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)?
                .as_secs();
            
            // 获取通道名称和操作类型
            let channel: String = match msg.get_channel() {
                Ok(ch) => ch,
                Err(e) => {
                    eprintln!("获取通道名称失败: {:?}", e);
                    continue;
                }
            };
            
            let operation: String = match msg.get_payload() {
                Ok(op) => op,
                Err(e) => {
                    eprintln!("获取操作类型失败: {:?}", e);
                    continue;
                }
            };
            
            // 提取实际的键名
            let key = if let Some(key_part) = channel.split(':').nth(1) {
                key_part
            } else {
                eprintln!("无法解析键名: {}", channel);
                continue;
            };
            
            println!("收到事件: 键={}, 操作={}", key, operation);
            
            // 只处理 "set" 操作
            if operation == "set" {
                // 获取键值
                let value: Option<String> = redis::cmd("GET")
                    .arg(key)
                    .query_async(&mut conn)
                    .await?;
                
                if let Some(val) = value {
                    println!("导出键: {} (长度: {}字节)", key, val.len());
                    
                    // 保存到日志文件
                    writeln!(log_file, "{} | {} | {} | {}", timestamp, key, operation, val)?;
                    
                    // 同时保存为独立文件
                    let file_path = format!("{}/key_{}_{}.txt", output_dir, key, timestamp);
                    let mut file = File::create(&file_path)?;
                    write!(file, "{}", val)?;
                    
                    // 尝试解析为JSON并保存格式化版本
                    if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&val) {
                        let json_path = format!("{}/key_{}_{}.json", output_dir, key, timestamp);
                        let pretty_json = serde_json::to_string_pretty(&parsed)?;
                        let mut json_file = File::create(&json_path)?;
                        write!(json_file, "{}", pretty_json)?;
                        println!("已保存JSON格式: {}", json_path);
                    }
                    
                    println!("已保存到: {}", file_path);
                } else {
                    println!("键 {} 无值", key);
                }
            }
        }
    } else {
        return Err(anyhow!("无法订阅Redis键空间通知"));
    }
    
    Ok(())
} 