use std::{sync::Arc, time::{Duration, Instant}, str::FromStr};

use anyhow::{Result, anyhow};
use async_trait::async_trait;
use chrono::Local;
use colored::Colorize;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::{
    signature::Signature,
    pubkey::Pubkey,
    instruction::{AccountMeta, Instruction},
    compute_budget::ComputeBudgetInstruction,
    transaction::Transaction,
    commitment_config::CommitmentConfig,
    signer::{keypair::Keypair, Signer},
    hash::Hash,
    system_instruction,
};
use spl_token::ID as TOKEN_PROGRAM_ID;
use tokio::sync::mpsc;
use tracing::{error, info, debug, warn};
use crate::hotpath::bonk_builder::BonkTransactionBuilder;
use crate::blockhash_service::BlockhashService;

/// 根据加速器类型获取最低tip限制 (与pump协议保持一致)
fn get_min_tip_lamports(accelerator_provider: &str) -> u64 {
    match accelerator_provider {
        "astralane" => 10_000,       // Astralane: 0.00001 SOL (官方要求)
        "blockrazor" => 1_000_000,   // BlockRazor: 0.001 SOL (按官方文档要求)
        "oslot" => 100_000,          // Oslot: 0.0001 SOL
        "flashblock" => 100_000,     // Flashblock: 0.0001 SOL (与Oslot保持一致)
        _ => 100_000,                // 默认: 0.0001 SOL
    }
}

use crate::{
    services::{
        transaction_sender::TransactionSender,
        senders::{astralane_sender::AstralaneSender, blockrazor_sender::BlockRazorSender, flashblock_sender::FlashblockSender, oslot_sender::OslotSender},
        transaction_tracker::TrackRequest,
        sol_price_oracle::SolPriceOracle,
        sell_executor_trait::SellExecutorTrait,
    },
    shared::types::{HotPathTrade, TradeType, WalletConfig},
    shared::global_config::get_accelerator_config,
    hotpath::bonk_builder::{RAYDIUM_LAUNCHPAD_PROGRAM_ID, WSOL_MINT},
};

#[derive(Clone)]
pub struct BonkSellExecutor {
    tx_sender: Arc<TransactionSender>,
    tracker_tx: mpsc::Sender<TrackRequest>,
    sol_price_oracle: SolPriceOracle,
    wallet: Arc<Keypair>,
    rpc_client: Arc<RpcClient>,
    compute_unit_limit: u32,
    priority_fee: u64,
    bonk_transaction_builder: Arc<BonkTransactionBuilder>, // 新增：预创建的高性能构建器
    blockhash_service: Arc<BlockhashService>, // 新增：区块哈希缓存服务
    blockhash_lag_slots: usize, // 新增：区块哈希滞后槽数
}

impl BonkSellExecutor {
    pub fn new(
        tx_sender: Arc<TransactionSender>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        sol_price_oracle: SolPriceOracle,
        wallet: Arc<Keypair>,
        rpc_client: Arc<RpcClient>,
        compute_unit_limit: u32,
        priority_fee: u64,
        bonk_transaction_builder: Arc<BonkTransactionBuilder>, // 新增参数
        blockhash_service: Arc<BlockhashService>, // 新增参数
        blockhash_lag_slots: usize, // 新增参数
    ) -> Self {
        Self {
            tx_sender,
            tracker_tx,
            sol_price_oracle,
            wallet,
            rpc_client,
            compute_unit_limit,
            priority_fee,
            bonk_transaction_builder, // 保存预创建的构建器
            blockhash_service,
            blockhash_lag_slots,
        }
    }

    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }

    pub async fn execute_sell(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell_with_slippage(
            trade,
            wallet_config,
            token_amount_in,
            decision_dur,
            reason,
            current_price,
            close_ata,
            None, // 使用默认滑点
        ).await
    }

    /// 执行bonk卖出交易，支持自定义滑点（用于重试）
    pub async fn execute_sell_with_slippage(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
        custom_slippage: Option<f64>,
    ) -> Result<(), anyhow::Error> {
        let total_start = Instant::now();
        let decimals = 6u32;
        let token_amount_ui = token_amount_in as f64 / 10f64.powi(decimals as i32);
        let expected_sol = token_amount_ui * current_price;

        // 使用自定义滑点或默认滑点
        let slip_pct = custom_slippage.unwrap_or_else(|| {
            wallet_config
                .sell_slippage_percentage
                .unwrap_or(wallet_config.slippage_percentage)
                .max(0.0)
        });

        let min_sol_out = expected_sol * (1.0 - slip_pct / 100.0);
        let mut min_sol_out_lamports = (min_sol_out * 1_000_000_000.0) as u64;
        if min_sol_out_lamports == 0 { min_sol_out_lamports = 1; }

        let accel_cfg = get_accelerator_config();
        let (tip_pubkey_opt, accelerator_provider) = if accel_cfg.enabled {
            match accel_cfg.provider.as_str() {
                "astralane" => (Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(), Some("astralane")),
                "blockrazor" => (Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(), Some("blockrazor")),
                "oslot" => (Pubkey::from_str(OslotSender::get_random_tip_account()).ok(), Some("oslot")),
                "flashblock" => (Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(), Some("flashblock")),
                _ => (None, None),
            }
        } else { (None, None) };

        let build_start = Instant::now();
        let unsigned_tx = self.build_unsigned_sell_transaction(
            &trade, 
            token_amount_in, 
            min_sol_out_lamports, 
            wallet_config,
            close_ata, 
            tip_pubkey_opt,
            accelerator_provider
        ).await?;
        let build_dur = build_start.elapsed();
        
        let sign_start = Instant::now();
        let (signed_tx, _signature, _blockhash) = self.sign_and_log_details(unsigned_tx).await?;
        let sign_finish = Instant::now();
        let sign_dur = sign_finish.duration_since(sign_start);
        
        let send_start = Instant::now();
        let sign_to_send_delay = send_start.duration_since(sign_finish);
        let send_result = self.tx_sender.send_transaction(&signed_tx).await;
        let send_dur = send_start.elapsed();

        match send_result {
            Ok(sig) => {
                let ts = Local::now().format("%H:%M:%S.%3f");
                let detail_line = format!(
                    "{} 🚀 {} | 总耗时: {} (决策: {}, 构建: {}, 签名: {}, 签名→发送: {})",
                    ts, reason, fmt_us(total_start.elapsed() - send_dur).red(),
                    fmt_us(decision_dur).yellow(), fmt_us(build_dur).yellow(), fmt_us(sign_dur).yellow(), fmt_us(sign_to_send_delay).yellow(),
                );
                info!("{}", detail_line);

                info!("⏳ Bonk卖出交易已发送至RPC节点 (等待确认)，签名: {} | 发送耗时: {:?}", sig, send_dur);
                let signature_obj: Signature = sig.parse()?;

                let sol_price_usd = self.sol_price_oracle.get_price_usd();
                let original_token_amount = if trade.token_amount == 0 { 1 } else { trade.token_amount };
                let cost_of_portion_in_sol = trade.sol_cost * (token_amount_in as f64 / original_token_amount as f64);
                let entry_usd = Some(cost_of_portion_in_sol * sol_price_usd);

                let track_req = TrackRequest {
                    trade_type: TradeType::Sell, 
                    signature: signature_obj,
                    mint: trade.mint_pubkey.to_string(), 
                    sol_amount: expected_sol,
                    token_amount: token_amount_in, 
                    user_wallet: self.wallet.pubkey().to_string(),
                    entry_sol_amount_usd: entry_usd, 
                    trade_info: Some(trade.clone()), // 添加trade_info用于重试
                    wallet_config: Some(wallet_config.clone()), // 添加wallet_config用于重试
                    executor_type: "bonk".to_string(), // bonk执行器
                };

                if let Err(e) = self.tracker_tx.send(track_req).await {
                    error!("无法将Bonk卖出交易提交给跟踪服务: {}", e);
                }

                Ok(())
            }
            Err(e) => {
                error!("Bonk卖出交易发送或确认失败: {}", e);
                Err(e.into())
            }
        }
    }

    /// 构建Bonk卖出交易的无符号交易 - 高性能版本 (对标买入性能)
    async fn build_unsigned_sell_transaction(
        &self,
        trade: &HotPathTrade,
        token_amount_in: u64,
        min_sol_out_lamports: u64,
        wallet_config: &WalletConfig,
        close_ata: bool,
        tip_pubkey_opt: Option<Pubkey>,
        accelerator_provider: Option<&str>,
    ) -> Result<Transaction> {

        // 使用预创建的高性能构建器，直接构建完整交易
        let sell_result = self.build_complete_sell_transaction(
            trade,
            token_amount_in,
            min_sol_out_lamports,
            wallet_config,
            close_ata,
            tip_pubkey_opt,
            accelerator_provider,
        ).await?;

        Ok(sell_result)
    }

    /// 构建完整的卖出交易 - 高性能实现 (对标买入性能)
    async fn build_complete_sell_transaction(
        &self,
        trade: &HotPathTrade,
        token_amount_in: u64,
        min_sol_out_lamports: u64,
        wallet_config: &WalletConfig,
        close_ata: bool,
        tip_pubkey_opt: Option<Pubkey>,
        accelerator_provider: Option<&str>,
    ) -> Result<Transaction> {
        let wallet_pubkey = self.wallet.pubkey();

        // 基础参数
        let token_mint = trade.mint_pubkey;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(&wallet_pubkey, &wsol_mint);
        let user_token_account = spl_associated_token_account::get_associated_token_address(&wallet_pubkey, &token_mint);

        // 计算有效的计算预算设置
        let effective_compute_limit = wallet_config
            .sell_compute_unit_limit
            .unwrap_or(wallet_config.compute_unit_limit)
            .max(80_000);

        let effective_priority_fee = wallet_config
            .sell_priority_fee
            .unwrap_or(wallet_config.priority_fee)
            .max(1_000);

        // 构建完整的指令序列
        let mut instructions = Vec::new();

        // 1. 设置计算预算
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(effective_compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(effective_priority_fee));

        // 2. 创建WSOL账户（幂等）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &wallet_pubkey,
                &wallet_pubkey,
                &wsol_mint,
                &TOKEN_PROGRAM_ID,
            )
        );

        // 3. 构建完整的Raydium卖出指令
        let raydium_instruction = self.build_complete_raydium_sell_instruction(
            trade, &wallet_pubkey, &user_wsol_account, &user_token_account,
            token_amount_in, min_sol_out_lamports
        )?;
        instructions.push(raydium_instruction);


        // 4. 转账手续费指令 (参考pump实现)
        let fee_address = "Cd4magyg5n2Qs1dLZEoLfCRYKxHrnkf61RBr14PyVbHW"; // 与pump保持一致的手续费地址
        let fee_percentage = 0.004; // 0.4% 手续费
        if let Ok(fee_pubkey) = Pubkey::from_str(fee_address) {
            let fee_lamports = ((min_sol_out_lamports as f64) * fee_percentage) as u64;
            if fee_lamports > 0 {
                instructions.push(system_instruction::transfer(
                    &wallet_pubkey,
                    &fee_pubkey,
                    fee_lamports,
                ));
            }
        }

        // 5. 如果指定了tip账户，添加tip转账指令 (必须是最后一条指令！)
        if let Some(tip_receiver) = tip_pubkey_opt {
            // 直接使用卖出专用tip百分比，如果没设置就用通用tip
            let tip_pct = if let Some(sell_tip) = wallet_config.sell_tip_percentage {
                sell_tip  // 如果设置了卖出tip，直接用
            } else {
                wallet_config.accelerator_tip_percentage.unwrap_or(1.0)  // 否则用通用tip
            };
            let tip_lamports = ((min_sol_out_lamports as f64) * (tip_pct / 100.0)) as u64;

            // 根据加速器类型获取最低tip限制
            let min_tip = get_min_tip_lamports(accelerator_provider.unwrap_or(""));
            let final_tip_lamports = tip_lamports.max(min_tip);

            instructions.push(system_instruction::transfer(
                &wallet_pubkey,
                &tip_receiver,
                final_tip_lamports,
            ));

        }

        // 6. 如果需要关闭ATA账户
        if close_ata {
            instructions.push(spl_token::instruction::close_account(
                &TOKEN_PROGRAM_ID,
                &user_token_account,
                &wallet_pubkey,
                &wallet_pubkey,
                &[],
            )?);
        }


        // 🚀 使用BlockhashService缓存获取blockhash (高性能，微秒级)
        let recent_blockhash = self.blockhash_service
            .get_blockhash_with_lag(self.blockhash_lag_slots)
            .map(|d| d.hash)
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;

        let transaction = Transaction::new_with_payer(
            &instructions,
            Some(&wallet_pubkey),
        );

        // 设置blockhash
        let mut transaction_with_blockhash = transaction;
        transaction_with_blockhash.message.recent_blockhash = recent_blockhash;

        Ok(transaction_with_blockhash)
    }

    /// 构建完整的Raydium卖出指令 (对标买入指令的完整实现)
    fn build_complete_raydium_sell_instruction(
        &self,
        trade: &HotPathTrade,
        wallet_pubkey: &Pubkey,
        user_wsol_account: &Pubkey,
        user_token_account: &Pubkey,
        token_amount_in: u64,
        min_sol_out_lamports: u64,
    ) -> Result<Instruction> {
        let raydium_launchpad_program = Pubkey::from_str(RAYDIUM_LAUNCHPAD_PROGRAM_ID)?;

        // 构建Raydium Launchpad sell_exact_in指令数据
        let mut swap_data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // sell_exact_in指令discriminator
        swap_data.extend_from_slice(&token_amount_in.to_le_bytes()); // amount_in (代币数量)
        swap_data.extend_from_slice(&min_sol_out_lamports.to_le_bytes()); // minimum_amount_out (最小SOL输出)
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate

        // 计算必要的PDA账户
        let authority_seed = b"vault_auth_seed";
        let event_authority_seed = b"__event_authority";
        let (authority, _) = Pubkey::find_program_address(&[authority_seed], &raydium_launchpad_program);
        let (event_authority, _) = Pubkey::find_program_address(&[event_authority_seed], &raydium_launchpad_program);

        // 使用固定的配置地址
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;

        // 构建完整的账户列表 (与买入保持一致的顺序)
        let accounts = vec![
            AccountMeta::new_readonly(*wallet_pubkey, true),                    // 0. payer
            AccountMeta::new_readonly(authority, false),                        // 1. authority
            AccountMeta::new_readonly(global_config, false),                    // 2. global_config
            AccountMeta::new_readonly(platform_config, false),                  // 3. platform_config
            AccountMeta::new(trade.creator_vault_pubkey, false),                // 4. pool_state
            AccountMeta::new(*user_token_account, false),                       // 5. user_base_token (卖出时输入)
            AccountMeta::new(*user_wsol_account, false),                        // 6. user_quote_token (卖出时输出)
            AccountMeta::new(trade.bonding_curve_pubkey, false),                // 7. base_vault
            AccountMeta::new(trade.associated_bonding_curve, false),            // 8. quote_vault
            AccountMeta::new_readonly(trade.mint_pubkey, false),                // 9. base_token_mint
            AccountMeta::new_readonly(wsol_mint, false),                        // 10. quote_token_mint
            AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),                 // 11. base_token_program
            AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),                 // 12. quote_token_program
            AccountMeta::new_readonly(event_authority, false),                  // 13. event_authority
            AccountMeta::new_readonly(raydium_launchpad_program, false),        // 14. program
        ];

        Ok(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        })
    }

    /// 构建并发送卖出交易，返回签名但不创建跟踪记录（用于重试）
    pub async fn build_and_send_sell_transaction(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        current_price: f64,
        custom_slippage: Option<f64>,
        reason: &str,
    ) -> Result<solana_sdk::signature::Signature, anyhow::Error> {
        let decimals = 6u32;
        let token_amount_ui = token_amount_in as f64 / 10f64.powi(decimals as i32);
        let expected_sol = token_amount_ui * current_price;

        // 使用自定义滑点或默认滑点
        let slip_pct = custom_slippage.unwrap_or_else(|| {
            wallet_config
                .sell_slippage_percentage
                .unwrap_or(wallet_config.slippage_percentage)
                .max(0.0)
        });

        let min_sol_out = expected_sol * (1.0 - slip_pct / 100.0);
        let mut min_sol_out_lamports = (min_sol_out * 1_000_000_000.0) as u64;
        if min_sol_out_lamports == 0 { min_sol_out_lamports = 1; }

        let accel_cfg = get_accelerator_config();
        let (tip_pubkey_opt, accelerator_provider) = if accel_cfg.enabled {
            match accel_cfg.provider.as_str() {
                "astralane" => (Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(), Some("astralane")),
                "blockrazor" => (Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(), Some("blockrazor")),
                "oslot" => (Pubkey::from_str(OslotSender::get_random_tip_account()).ok(), Some("oslot")),
                "flashblock" => (Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(), Some("flashblock")),
                _ => (None, None),
            }
        } else { (None, None) };

        // 移除内部重试循环，使用统一的transaction_tracker重试机制
        // 构建交易
        let unsigned_tx = self.build_unsigned_sell_transaction(
            &trade, token_amount_in, min_sol_out_lamports, wallet_config, true, tip_pubkey_opt, accelerator_provider
        ).await?;

        // 签名交易
        let (signed_tx, _signature, _blockhash) = self.sign_and_log_details(unsigned_tx).await?;

        // 发送交易
        match self.tx_sender.send_transaction(&signed_tx).await {
            Ok(sig) => {
                info!("⏳ Bonk {} 卖出交易已发送，签名: {}", reason, sig);
                let signature_obj: solana_sdk::signature::Signature = sig.parse()?;

                Ok(signature_obj)
            }
            Err(e) => {
                error!("Bonk卖出交易发送失败: {}", e);
                Err(e.into())
            }
        }
    }

    /// 签名交易并记录详细信息
    pub async fn sign_and_log_details(&self, mut transaction: Transaction) -> Result<(Transaction, Signature, Hash)> {
        let wallet = self.wallet.clone();
        let recent_blockhash = transaction.message.recent_blockhash;
        
        // 使用 spawn_blocking 将同步的、CPU密集的签名操作移到专门的线程池
        // 从而避免阻塞 Tokio 的主异步运行时
        transaction = tokio::task::spawn_blocking(move || {
            transaction
                .try_partial_sign(&[wallet.as_ref()], recent_blockhash)
                .map_err(|e| anyhow!("Bonk交易签名失败: {}", e))?;
            Ok::<_, anyhow::Error>(transaction)
        }).await
        .map_err(|e| anyhow!("签名任务执行失败: {}", e))??;

        // 提取签名
        let signature = transaction.signatures[0];
        
        Ok((transaction, signature, recent_blockhash))
    }
}

/// 为BonkSellExecutor实现统一的卖出执行器接口
#[async_trait]
impl SellExecutorTrait for BonkSellExecutor {
    async fn execute_sell(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell(trade, wallet_config, token_amount_in, decision_dur, reason, current_price, close_ata).await
    }

    async fn execute_sell_with_slippage(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
        custom_slippage: Option<f64>,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell_with_slippage(trade, wallet_config, token_amount_in, decision_dur, reason, current_price, close_ata, custom_slippage).await
    }

    async fn build_and_send_sell_transaction(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        current_price: f64,
        custom_slippage: Option<f64>,
        reason: &str,
    ) -> Result<Signature, anyhow::Error> {
        self.build_and_send_sell_transaction(trade, wallet_config, token_amount_in, current_price, custom_slippage, reason).await
    }
}

fn fmt_us(d: std::time::Duration) -> String {
    format!("{} µs", d.as_micros())
}