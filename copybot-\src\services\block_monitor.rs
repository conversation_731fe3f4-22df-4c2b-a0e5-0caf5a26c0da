use anyhow::{Result, anyhow};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use futures_util::{SinkExt, StreamExt};
use serde_json::{json, Value};
use chrono::Local;
use tokio::time::{sleep, Duration};

/// 区块监控器，监听 confirmed 级别的区块更新
pub struct BlockMonitor {
    ws_url: String,
}

impl BlockMonitor {
    /// 创建新的区块监控器
    pub fn new(ws_url: String) -> Self {
        Self { ws_url }
    }
    
    /// 启动监控任务 (只监听 confirmed)
    pub async fn start_monitoring(&self) -> Result<()> {
        println!("启动区块监控 (confirmed)... WebSocket URL: {}", self.ws_url);

        // 添加简单的重试循环
        loop {
            println!("正在连接到 WebSocket...");
            match connect_async(&self.ws_url).await {
                Ok((ws_stream, _)) => {
                    println!("已连接 WebSocket，正在订阅 confirmed 区块...");
                    let (mut write, mut read) = ws_stream.split();

                    // 订阅 confirmed 区块更新信息
                    let subscribe_msg = json!({
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "blockSubscribe",
                        "params": [
                            "all",
                            {
                                "commitment": "confirmed",
                                "encoding": "jsonParsed",
                                "maxSupportedTransactionVersion": 0,
                                "transactionDetails": "none"
                            }
                        ]
                    });

                    if let Err(e) = write.send(Message::Text(subscribe_msg.to_string().into())).await {
                        eprintln!("[错误] 发送 blockSubscribe (confirmed) 订阅请求失败: {} - 5秒后重试...", e);
                        sleep(Duration::from_secs(5)).await;
                        continue;
                    }

                    println!("已发送 blockSubscribe (confirmed) 请求，等待数据...");

                    let mut message_count = 0;
                    while let Some(message) = read.next().await {
                        message_count += 1;

                        match message {
                            Ok(Message::Text(text)) => {
                                match serde_json::from_str::<Value>(&text) {
                                    Ok(json) => {
                                        let now = Local::now();
                                        let time_str = now.format("%Y.%m.%d.%H.%M.%S").to_string();

                                        if json.get("id").and_then(|id| id.as_u64()) == Some(1) {
                                            if let Some(result) = json.get("result") {
                                                println!("{} 订阅确认 (blockSubscribe confirmed): {}", time_str, result);
                                            } else if let Some(error) = json.get("error") {
                                                eprintln!("[错误] blockSubscribe (confirmed) 订阅失败: {} - 5秒后重试...", error);
                                                break;
                                            }
                                            continue;
                                        }

                                        if json.get("method").and_then(|m| m.as_str()) == Some("blockNotification") {
                                            if let Some(params) = json.get("params") {
                                                if let Some(result) = params.get("result") {
                                                    if let Some(value) = result.get("value") {
                                                        if let Some(block) = value.get("block") {
                                                            let blockhash = block.get("blockhash").and_then(|h| h.as_str());
                                                            let block_height = block.get("blockHeight").and_then(|h| h.as_u64());
                                                            let slot = value.get("slot").and_then(|s| s.as_u64()).unwrap_or(0);

                                                            if let Some(bh) = blockhash {
                                                                print!("{} 区块更新 (confirmed) - Slot: {}, Blockhash: {}", 
                                                                    time_str, slot, bh);
                                                                if let Some(height) = block_height {
                                                                    print!(", Block Height: {}", height);
                                                                }
                                                                println!(); 
                                                            } else {
                                                                // Blockhash 为空或非字符串，打印警告
                                                                println!("[警告] {} (confirmed) - Slot: {}, 未能提取 Blockhash", time_str, slot);
                                                            }
                                                        } else if value.get("err").is_some() {
                                                            // 理论上 confirmed 不应该只返回 err，但也处理一下以防万一
                                                            let slot = value.get("slot").and_then(|s| s.as_u64()).unwrap_or(0);
                                                            println!("{} 跳过区块 (confirmed) - Slot: {}, 原因: {:?}", 
                                                                time_str, slot, value.get("err"));
                                                        } else {
                                                             println!("[警告][消息 {}] (confirmed) blockNotification 缺少 'block' 或 'err' 字段", message_count);
                                                             println!("[调试][消息 {}] (confirmed) value: {:?}", message_count, value);
                                                        }
                                                    } else {
                                                         println!("[警告][消息 {}] (confirmed) blockNotification 缺少 'value' 字段", message_count);
                                                    }
                                                } else {
                                                     println!("[警告][消息 {}] (confirmed) blockNotification 缺少 'result' 字段", message_count);
                                                }
                                            } else {
                                                println!("[警告][消息 {}] (confirmed) blockNotification 缺少 'params' 字段", message_count);
                                            }
                                        } else if let Some(error) = json.get("error") {
                                             println!("[错误][消息 {}] (confirmed) 收到错误响应: {}", message_count, error);
                                        } else {
                                             // println!("[消息 {}] (confirmed) 收到未知结构的JSON: {:?}", message_count, json);
                                        }
                                    },
                                    Err(e) => {
                                        eprintln!("[错误][消息 {}] (confirmed) 解析JSON响应失败: {} - 尝试继续...", message_count, e);
                                        let snippet = text.chars().take(200).collect::<String>();
                                        eprintln!("[原始响应片段] {}...", snippet);
                                    }
                                }
                            },
                            Ok(Message::Ping(ping_data)) => {
                                if let Err(e) = write.send(Message::Pong(ping_data)).await {
                                    eprintln!("[错误] (confirmed) 响应ping失败: {} - 连接可能断开，将尝试重连", e);
                                    break;
                                }
                            },
                            Ok(Message::Close(close_frame)) => {
                                println!("[连接] (confirmed) WebSocket连接已关闭: {:?} - 尝试重连...", close_frame);
                                break;
                            },
                            Err(e) => {
                                eprintln!("[错误] (confirmed) WebSocket错误: {} - 尝试重连...", e);
                                break;
                            }
                             Ok(_) => {}
                        }
                    }
                    println!("WebSocket 内部消息循环结束，准备重连...");
                },
                Err(e) => {
                    eprintln!("[严重错误] 连接 WebSocket 失败: {} - 5秒后重试...", e);
                }
            }
            sleep(Duration::from_secs(5)).await;
        }
    }
} 