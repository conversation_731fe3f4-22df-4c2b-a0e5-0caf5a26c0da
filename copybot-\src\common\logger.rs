use chrono::Local;
use std::env;

// 将常量改为从环境变量获取，默认为DEBUG
// const LOG_LEVEL: &str = "LOG";

#[derive(Clone)]
pub struct Logger {
    prefix: String,
    date_format: String,
}

impl Logger {
    // Constructor function to create a new Logger instance
    pub fn new(prefix: String) -> Self {
        Logger {
            prefix,
            date_format: String::from("%Y-%m-%d %H:%M:%S.%3f"),
        }
    }

    // Method to log a message with a prefix
    pub fn log(&self, message: impl AsRef<str>) -> String {
        // 使用标准日志格式，不再使用单独的时间格式
        // 交给log宏来处理日志输出格式统一问题
        let log = format!("[{}] {}", self.prefix, message.as_ref());
        log::info!("{}", log);
        log
    }

    pub fn debug(&self, message: impl AsRef<str>) -> String {
        let log = format!("{} [{}] {}", self.prefix_with_date(), "DEBUG", message.as_ref());
        if LogLevel::new().is_debug() {
            println!("{}", log);
        }
        log
    }
    
    pub fn info(&self, message: impl AsRef<str>) -> String {
        // 改为使用标准日志库，避免重复输出
        let log = format!("[{}] {}", self.prefix, message.as_ref());
        log::info!("{}", log);
        log
    }
    
    pub fn error(&self, message: impl AsRef<str>) -> String {
        // 改为使用标准日志库，避免重复输出
        let log = format!("[{}] {}", self.prefix, message.as_ref());
        log::error!("{}", log);
        log
    }
    
    pub fn warn(&self, message: impl AsRef<str>) -> String {
        // 改为使用标准日志库，避免重复输出
        let log = format!("[{}] {}", self.prefix, message.as_ref());
        log::warn!("{}", log);
        log
    }

    fn prefix_with_date(&self) -> String {
        let date = Local::now();
        format!(
            "[{}] {}",
            date.format(self.date_format.as_str()),
            self.prefix
        )
    }
}

struct LogLevel {
    level: String,
}

impl LogLevel {
    fn new() -> Self {
        // 尝试从RUST_LOG获取日志级别
        let level = env::var("RUST_LOG")
            .ok()
            .and_then(|v| {
                // 尝试从格式如"copy_trading_bot=info"中提取级别
                v.to_lowercase().split('=').nth(1).map(|s| s.to_uppercase())
            })
            // 如果RUST_LOG解析失败，回退到原来的LOG_LEVEL
            .unwrap_or_else(|| env::var("LOG_LEVEL").unwrap_or_else(|_| "DEBUG".to_string())
                                                  .to_uppercase());
        
        LogLevel { level }
    }
    
    fn is_debug(&self) -> bool {
        self.level.to_uppercase() == "DEBUG"
    }
}
