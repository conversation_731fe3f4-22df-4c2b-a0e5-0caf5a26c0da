use anyhow::{Result, anyhow};
use log::{debug, info};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{pubkey::Pubkey, signature::{Keypair, Signer}, system_program};
use spl_associated_token_account::get_associated_token_address;
use std::{str::FromStr, sync::Arc, time::{SystemTime, UNIX_EPOCH}};
use tokio::sync::Mutex;

use crate::common::logger::Logger;
use crate::common::utils::WalletCache;
use crate::PUMP_PROGRAM_ID; // 从 lib.rs 导入

// PDA缓存工具
pub struct PdaCacheService {
    rpc_client: Arc<RpcClient>,
    wallet: Arc<Keypair>,
    wallet_cache: Arc<Mutex<WalletCache>>,
    logger: Logger,
    // 缓存持续时间（秒）
    cache_duration: u64,
}

impl PdaCacheService {
    // 创建新的PDA缓存服务
    pub fn new(
        rpc_client: Arc<RpcClient>,
        wallet: Arc<Keypair>,
        wallet_cache: Arc<Mutex<WalletCache>>,
        cache_duration_seconds: Option<u64>,
    ) -> Self {
        let logger = Logger::new("PdaCache".to_string());
        
        Self {
            rpc_client,
            wallet,
            wallet_cache,
            logger,
            // 默认缓存持续时间为1小时
            cache_duration: cache_duration_seconds.unwrap_or(3600),
        }
    }

    // 批量缓存多个代币的PDA/ATA
    pub async fn batch_cache_token_pdas(&self, mint_strings: &[String]) -> Result<()> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow!("获取系统时间失败: {}", e))?
            .as_secs();
            
        self.logger.log(format!("开始批量缓存 {} 个代币的PDA/ATA地址", mint_strings.len()));
        
        let mut cached_count = 0;
        for mint_str in mint_strings {
            match self.cache_token_pda(mint_str, current_time).await {
                Ok(_) => {
                    cached_count += 1;
                }
                Err(e) => {
                    self.logger.error(format!("缓存代币 {} 的PDA/ATA失败: {}", mint_str, e));
                }
            }
        }
        
        self.logger.log(format!("批量缓存完成，成功缓存 {}/{} 个代币的PDA/ATA地址", cached_count, mint_strings.len()));
        Ok(())
    }
    
    // 从钱包中的代币列表缓存所有代币的PDA/ATA
    pub async fn cache_all_wallet_tokens(&self) -> Result<()> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow!("获取系统时间失败: {}", e))?
            .as_secs();
            
        // 获取钱包中的所有代币
        let wallet_lock = self.wallet_cache.lock().await;
        let tokens = wallet_lock.tokens.keys().cloned().collect::<Vec<String>>();
        drop(wallet_lock); // 释放锁
        
        self.logger.log(format!("从钱包缓存中获取到 {} 个代币，开始批量缓存PDA/ATA", tokens.len()));
        
        self.batch_cache_token_pdas(&tokens).await
    }

    // 为单个代币缓存PDA/ATA
    pub async fn cache_token_pda(&self, mint_str: &str, current_time: u64) -> Result<()> {
        // 1. 解析Mint pubkey
        let mint_pubkey = Pubkey::from_str(mint_str)
            .map_err(|e| anyhow!("无效的Mint地址: {}, 错误: {}", mint_str, e))?;
            
        // 2. 计算Bonding Curve PDA
        let (bonding_curve_pubkey, curve_bump) = Pubkey::find_program_address(
            &[b"bonding-curve", mint_pubkey.as_ref()],
            &PUMP_PROGRAM_ID
        );
        
        // 3. 计算Curve的Token账户（ATA）
        let curve_token_account_pubkey = get_associated_token_address(
            &bonding_curve_pubkey,
            &mint_pubkey
        );
        
        // 4. 计算用户的Token账户（ATA）
        let user_token_account_pubkey = get_associated_token_address(
            &self.wallet.pubkey(),
            &mint_pubkey
        );
        
        // 5. 更新缓存
        let mut wallet_lock = self.wallet_cache.lock().await;
        wallet_lock.cache_pda(
            mint_str.to_string(),
            mint_pubkey.to_string(),
            bonding_curve_pubkey.to_string(),
            curve_bump,
            curve_token_account_pubkey.to_string(),
            user_token_account_pubkey.to_string(),
            current_time,
            self.cache_duration,
        );
        
        self.logger.debug(format!(
            "已缓存代币 {} 的PDA/ATA: 曲线={}, 曲线ATA={}, 用户ATA={}",
            mint_str, 
            bonding_curve_pubkey, 
            curve_token_account_pubkey, 
            user_token_account_pubkey
        ));
        
        Ok(())
    }

    // 获取缓存的PDA/ATA，如果不存在或已过期则重新计算并缓存
    pub async fn get_or_cache_token_pda(&self, mint_str: &str) -> Result<(Pubkey, Pubkey, Pubkey)> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow!("获取系统时间失败: {}", e))?
            .as_secs();
            
        // 先尝试从缓存获取
        let cache_hit = {
            let wallet_lock = self.wallet_cache.lock().await;
            wallet_lock.get_pda_cache(mint_str, current_time).cloned()
        };
        
        // 如果缓存命中，直接返回
        if let Some(cache) = cache_hit {
            let bonding_curve = Pubkey::from_str(&cache.bonding_curve_pubkey)
                .map_err(|e| anyhow!("无效的缓存Bonding Curve地址: {}", e))?;
                
            let curve_token_account = Pubkey::from_str(&cache.curve_token_account_pubkey)
                .map_err(|e| anyhow!("无效的缓存Curve Token Account地址: {}", e))?;
                
            let user_token_account = Pubkey::from_str(&cache.user_token_account_pubkey)
                .map_err(|e| anyhow!("无效的缓存User Token Account地址: {}", e))?;
                
            return Ok((bonding_curve, curve_token_account, user_token_account));
        }
        
        // 缓存未命中，重新计算并缓存
        self.cache_token_pda(mint_str, current_time).await?;
        
        // 从新缓存中获取
        let wallet_lock = self.wallet_cache.lock().await;
        if let Some(cache) = wallet_lock.get_pda_cache(mint_str, current_time) {
            let bonding_curve = Pubkey::from_str(&cache.bonding_curve_pubkey)
                .map_err(|e| anyhow!("无效的缓存Bonding Curve地址: {}", e))?;
                
            let curve_token_account = Pubkey::from_str(&cache.curve_token_account_pubkey)
                .map_err(|e| anyhow!("无效的缓存Curve Token Account地址: {}", e))?;
                
            let user_token_account = Pubkey::from_str(&cache.user_token_account_pubkey)
                .map_err(|e| anyhow!("无效的缓存User Token Account地址: {}", e))?;
                
            Ok((bonding_curve, curve_token_account, user_token_account))
        } else {
            Err(anyhow!("无法缓存代币 {} 的PDA/ATA地址", mint_str))
        }
    }
    
    // 清理过期的PDA缓存
    pub async fn clean_expired_caches(&self) -> Result<()> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow!("获取系统时间失败: {}", e))?
            .as_secs();
            
        let mut wallet_lock = self.wallet_cache.lock().await;
        
        // 记录清理前的缓存数量
        let before_count = wallet_lock.pda_cache.len();
        
        // 清理过期的PDA缓存
        wallet_lock.clean_expired_pda_cache(current_time);
        
        // 记录清理后的缓存数量
        let after_count = wallet_lock.pda_cache.len();
        
        self.logger.log(format!("已清理过期的PDA缓存，清理前: {}，清理后: {}", before_count, after_count));
        
        Ok(())
    }
} 