use anyhow::Result;
use serde::{Deserialize, Serialize};
use solana_sdk::{commitment_config::CommitmentConfig, hash::Hash, signature::Keypair, pubkey::Pubkey};
use std::{env, sync::Arc, time::Instant};
use tokio::sync::Mutex;
use std::collections::{HashMap, VecDeque};
use tokio::sync::RwLock;
use solana_client::rpc_client::RpcClient;
use solana_client::nonblocking::rpc_client::RpcClient as NonblockingRpcClient;
use crate::services::api::WalletConfig;
use std::time::SystemTime;
use serde_json;
use std::fs;
use std::path::Path;
use crate::dex::pump_fun::Pump;

// 添加PDA缓存模块
pub mod pda_cache;

// 添加配置监控模块
pub mod config_monitor;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub mint: String,
    pub balance: u64,
    pub decimals: u8,
    pub token_account_pubkey: String,
    #[serde(default)]
    pub last_price: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenAccountCache {
    pub mint: String,
    pub bonding_curve: String,
    pub curve_token_account: String,
    pub user_token_account: String,
    pub last_updated: u64,
    pub expires_at: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PDACache {
    pub mint: String,
    pub mint_pubkey: String,
    pub bonding_curve_pubkey: String,
    pub curve_bump: u8,
    pub curve_token_account_pubkey: String,
    pub user_token_account_pubkey: String,
    pub last_updated: u64,
    pub expires_at: u64,
}

#[derive(Debug, Clone, Default)]
pub struct WalletCache {
    pub sol_balance: u64,
    pub tokens: HashMap<String, TokenInfo>,
    pub last_updated: u64,
    pub token_accounts: HashMap<String, TokenAccountCache>,
    pub pda_cache: HashMap<String, PDACache>,
}

impl WalletCache {
    pub fn new() -> Self {
        Default::default()
    }

    pub fn cache_token_accounts(
        &mut self,
        mint: String,
        bonding_curve: String,
        curve_token_account: String,
        user_token_account: String,
        current_time: u64,
        cache_duration_seconds: u64,
    ) {
        let expires_at = current_time + cache_duration_seconds;
        let cache_entry = TokenAccountCache {
            mint: mint.clone(),
            bonding_curve,
            curve_token_account,
            user_token_account,
            last_updated: current_time,
            expires_at,
        };
        self.token_accounts.insert(mint, cache_entry);
    }

    pub fn get_token_accounts(&self, mint: &str, current_time: u64) -> Option<&TokenAccountCache> {
        self.token_accounts.get(mint).filter(|cache| cache.expires_at > current_time)
    }

    pub fn clean_expired_token_accounts(&mut self, current_time: u64) {
        self.token_accounts.retain(|_, cache| cache.expires_at > current_time);
    }

    pub fn cache_pda(
        &mut self,
        mint: String,
        mint_pubkey: String,
        bonding_curve_pubkey: String,
        curve_bump: u8,
        curve_token_account_pubkey: String,
        user_token_account_pubkey: String,
        current_time: u64,
        cache_duration_seconds: u64,
    ) {
        let expires_at = current_time + cache_duration_seconds;
        let cache_entry = PDACache {
            mint: mint.clone(),
            mint_pubkey,
            bonding_curve_pubkey,
            curve_bump,
            curve_token_account_pubkey,
            user_token_account_pubkey,
            last_updated: current_time,
            expires_at,
        };
        self.pda_cache.insert(mint, cache_entry);
    }

    pub fn get_pda_cache(&self, mint: &str, current_time: u64) -> Option<&PDACache> {
        self.pda_cache.get(mint).filter(|cache| cache.expires_at > current_time)
    }

    pub fn clean_expired_pda_cache(&mut self, current_time: u64) {
        self.pda_cache.retain(|_, cache| cache.expires_at > current_time);
    }
}

#[derive(Debug, Clone)]
pub struct HashWithTimestamp {
    pub hash: Hash,
    pub timestamp: Instant,
}

impl HashWithTimestamp {
    pub fn new(hash: Hash) -> Self {
        Self {
            hash,
            timestamp: Instant::now(),
        }
    }
}

pub type LatestBlockhash = VecDeque<HashWithTimestamp>;

/// 应用状态
pub struct AppState {
    /// Solana RPC 客户端
    pub rpc_client: Arc<RpcClient>,
    /// Solana 非阻塞 RPC 客户端
    pub rpc_nonblocking_client: Arc<NonblockingRpcClient>,
    /// 钱包密钥对
    pub wallet: Arc<Keypair>,
    /// 钱包缓存
    pub wallet_cache: Arc<Mutex<WalletCache>>,
    /// 最新区块哈希
    pub latest_blockhash: Arc<RwLock<VecDeque<HashWithTimestamp>>>,
    /// Pump服务实例 (可选)
    pub pump_service: Option<Arc<Mutex<Pump>>>,
    /// 钱包配置
    pub wallet_configs: Arc<RwLock<HashMap<String, WalletConfig>>>,
    /// 配置文件路径
    pub config_file_path: String,
    /// 钱包监控服务
    pub wallet_monitor: Arc<crate::services::wallet_monitor::WalletMonitor>,
}

impl AppState {
    pub fn new(
        rpc_client: Arc<RpcClient>,
        rpc_nonblocking_client: Arc<NonblockingRpcClient>,
        wallet: Arc<Keypair>,
        pump_service: Option<Arc<Mutex<Pump>>>,
    ) -> Self {
        let config_file_path = std::env::var("WALLET_CONFIG_PATH")
            .unwrap_or_else(|_| "wallet_configs.json".to_string());

        let mut initial_blockhash = VecDeque::with_capacity(3);
        initial_blockhash.push_back(HashWithTimestamp::new(Hash::default()));

        Self {
            rpc_client,
            rpc_nonblocking_client,
            wallet,
            wallet_cache: Arc::new(Mutex::new(WalletCache::new())),
            latest_blockhash: Arc::new(RwLock::new(initial_blockhash)),
            pump_service,
            wallet_configs: Arc::new(RwLock::new(HashMap::new())),
            config_file_path,
            wallet_monitor: Arc::new(crate::services::wallet_monitor::WalletMonitor::new()),
        }
    }

    pub async fn load_wallet_configs(&self) -> Result<()> {
        if Path::new(&self.config_file_path).exists() {
            let content = fs::read_to_string(&self.config_file_path)?;
            let configs: HashMap<String, WalletConfig> = serde_json::from_str(&content)?;
            let mut wallet_configs = self.wallet_configs.write().await;
            *wallet_configs = configs;
        }
        Ok(())
    }

    pub async fn save_wallet_configs(&self) -> Result<()> {
        let configs = self.wallet_configs.read().await;
        let json = serde_json::to_string_pretty(&*configs)?;
        fs::write(&self.config_file_path, json)?;
        Ok(())
    }
}

#[derive(Debug)]
pub struct ParseTx {
    pub type_tx: String,
    pub direction: Option<String>,
    pub amount_in: f64,
    pub amount_out: f64,
    pub mint: String,
    pub price: Option<f64>,
}

pub fn import_env_var(key: &str) -> String {
    env::var(key).unwrap_or_else(|_| panic!("Environment variable {} is not set", key))
}

pub fn create_rpc_client() -> Result<Arc<solana_client::rpc_client::RpcClient>> {
    let rpc_https = import_env_var("RPC_URL");
    let rpc_client = solana_client::rpc_client::RpcClient::new_with_commitment(
        rpc_https,
        CommitmentConfig::processed(),
    );
    Ok(Arc::new(rpc_client))
}

pub async fn create_nonblocking_rpc_client(
) -> Result<Arc<solana_client::nonblocking::rpc_client::RpcClient>> {
    let rpc_https = import_env_var("RPC_URL");
    let rpc_client = solana_client::nonblocking::rpc_client::RpcClient::new_with_commitment(
        rpc_https,
        CommitmentConfig::processed(),
    );
    Ok(Arc::new(rpc_client))
}

pub fn import_wallet() -> Result<Arc<Keypair>> {
    let priv_key = import_env_var("PRIVATE_KEY");
    let wallet: Keypair = Keypair::from_base58_string(priv_key.as_str());

    Ok(Arc::new(wallet))
} 