use anyhow::{Result, anyhow};
use redis::{Client as RedisClient, AsyncCommands};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::sleep;
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::geyser::{
    CommitmentLevel, SubscribeRequest, SubscribeRequestFilterBlocks,
    subscribe_update::UpdateOneof,
};
use tonic::transport::ClientTlsConfig;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use solana_sdk::hash::Hash;
use std::str::FromStr;
use chrono::{DateTime, Utc};

// --- 配置常量 ---
const GRPC_URL: &str = "solana-yellowstone-grpc.publicnode.com:443"; // gRPC服务地址
const REDIS_URL: &str = "redis://127.0.0.1:6379/"; // Redis服务地址
const REDIS_HASH_KEY: &str = "solana:latest_hash"; // Redis中存储哈希的键名
const CACHE_EXPIRY: u64 = 300; // 缓存过期时间(秒)

/// 区块哈希信息结构
#[derive(Debug, Clone)]
struct BlockHashInfo {
    hash: String,
    slot: u64,
    parent_slot: u64,
    timestamp: u64,
}

/// 哈希监控服务
struct HashMonitor {
    grpc_url: String,
    redis_client: RedisClient,
    latest_hash: Arc<Mutex<Option<BlockHashInfo>>>,
}

impl HashMonitor {
    /// 创建新的哈希监控服务实例
    pub async fn new() -> Result<Self> {
        // 创建Redis客户端
        let redis_client = RedisClient::open(REDIS_URL)
            .map_err(|e| anyhow!("Redis连接失败: {}", e))?;
            
        // 验证Redis连接
        let mut conn = redis_client.get_async_connection().await
            .map_err(|e| anyhow!("无法连接到Redis: {}", e))?;
            
        println!("成功连接到Redis服务: {}", REDIS_URL);
        
        Ok(Self {
            grpc_url: GRPC_URL.to_string(),
            redis_client,
            latest_hash: Arc::new(Mutex::new(None)),
        })
    }
    
    /// 从Redis获取缓存的哈希
    async fn get_cached_hash(&self) -> Result<Option<BlockHashInfo>> {
        let mut conn = self.redis_client.get_async_connection().await
            .map_err(|e| anyhow!("Redis获取连接失败: {}", e))?;
            
        // 获取哈希、槽位、父槽位和时间戳数据
        let result: redis::RedisResult<(Option<String>, Option<u64>, Option<u64>, Option<u64>)> = redis::pipe()
            .hget(REDIS_HASH_KEY, "hash")
            .hget(REDIS_HASH_KEY, "slot")
            .hget(REDIS_HASH_KEY, "parent_slot")
            .hget(REDIS_HASH_KEY, "timestamp")
            .query_async(&mut conn).await;
            
        match result {
            Ok((Some(hash), Some(slot), Some(parent_slot), Some(timestamp))) => {
                let now = SystemTime::now().duration_since(UNIX_EPOCH)
                    .map_err(|_| anyhow!("获取系统时间失败"))?
                    .as_secs();
                    
                // 检查缓存是否过期
                if now - timestamp <= CACHE_EXPIRY {
                    return Ok(Some(BlockHashInfo {
                        hash,
                        slot,
                        parent_slot,
                        timestamp,
                    }));
                }
            }
            _ => {}
        }
        
        Ok(None)
    }
    
    /// 将哈希数据缓存到Redis
    async fn cache_hash(&self, hash_info: &BlockHashInfo) -> Result<()> {
        let mut conn = self.redis_client.get_async_connection().await
            .map_err(|e| anyhow!("Redis获取连接失败: {}", e))?;
            
        // 使用Redis的哈希结构存储数据
        redis::pipe()
            .hset(REDIS_HASH_KEY, "hash", &hash_info.hash)
            .hset(REDIS_HASH_KEY, "slot", hash_info.slot)
            .hset(REDIS_HASH_KEY, "parent_slot", hash_info.parent_slot)
            .hset(REDIS_HASH_KEY, "timestamp", hash_info.timestamp)
            .expire(REDIS_HASH_KEY, CACHE_EXPIRY as usize)
            .query_async::<_, ()>(&mut conn).await
            .map_err(|e| anyhow!("Redis缓存哈希数据失败: {}", e))?;
            
        println!("已将哈希数据缓存到Redis: {} (槽位: {})", hash_info.hash, hash_info.slot);
        Ok(())
    }
    
    /// 创建gRPC客户端
    async fn create_grpc_client(&self) -> Result<GeyserGrpcClient<impl tonic::service::Interceptor>> {
        println!("正在连接到gRPC端点: {}", self.grpc_url);
        
        // 获取域名部分用于TLS配置
        let domain = self.grpc_url.split(':').next().unwrap_or(&self.grpc_url);
        
        // 格式化端点地址
        let formatted_endpoint = if !self.grpc_url.starts_with("http") {
            if self.grpc_url.contains(":443") {
                format!("https://{}", self.grpc_url)
            } else {
                format!("http://{}", self.grpc_url)
            }
        } else {
            self.grpc_url.clone()
        };
        
        println!("使用格式化后的端点: {}", formatted_endpoint);
        
        // 使用TLS配置连接
        let tls_config = ClientTlsConfig::new().domain_name(domain);
        
        match GeyserGrpcClient::connect(
            formatted_endpoint.clone(),
            None::<String>,
            Some(tls_config),
        ) {
            Ok(client) => {
                println!("成功连接到Yellowstone gRPC服务");
                Ok(client)
            },
            Err(e) => {
                // 尝试不使用TLS
                println!("带TLS连接失败，尝试不使用TLS配置");
                match GeyserGrpcClient::connect(
                    formatted_endpoint.clone(),
                    None::<String>,
                    None,
                ) {
                    Ok(client) => {
                        println!("成功连接到Yellowstone gRPC服务(无TLS)");
                        Ok(client)
                    },
                    Err(e2) => {
                        Err(anyhow!("无法连接到gRPC服务: 带TLS错误: {}, 无TLS错误: {}", e, e2))
                    }
                }
            }
        }
    }
    
    /// 启动哈希监控服务
    pub async fn start_monitoring(&self) -> Result<()> {
        println!("启动区块哈希监控服务...");
        
        // 创建gRPC客户端
        let mut client = self.create_grpc_client().await?;
        
        // 订阅区块更新 (应用 filter_by_commitment 过滤器)
        let mut blocks_filter = SubscribeRequestFilterBlocks {
            filter_by_commitment: true, // 过滤特定提交级别的区块
            ..Default::default()
        };
        
        // 创建订阅请求
        let request = SubscribeRequest {
            blocks: Some(blocks_filter),
            commitment: Some(CommitmentLevel::Finalized as i32), // 仅接收已确认的区块
            accounts: None,
            slots: None,
            transactions: None,
            blocks_meta: None,
            entry: None,
            ping: None,
        };
        
        // 发起订阅
        println!("正在订阅区块更新...");
        let mut update_stream = client.subscribe(request).await
            .map_err(|e| anyhow!("无法订阅区块更新: {}", e))?;
            
        // 处理流中的更新
        println!("开始接收区块更新...");
        
        let mut heartbeat_interval = tokio::time::interval(Duration::from_secs(60));
        
        while let Some(update_result) = update_stream.next().await {
            match update_result {
                Ok(update) => {
                    // 仅处理区块更新
                    if let Some(UpdateOneof::Block(block)) = update.update_oneof {
                        let slot = block.slot;
                        let parent_slot = block.parent_slot;
                        let hash_str = bs58::encode(&block.blockhash).into_string();
                        
                        // 获取当前时间戳
                        let now = SystemTime::now().duration_since(UNIX_EPOCH)
                            .map_err(|_| anyhow!("获取系统时间失败"))?
                            .as_secs();
                            
                        // 将时间戳转换为可读格式
                        let dt = DateTime::<Utc>::from_timestamp(now as i64, 0)
                            .ok_or(anyhow!("时间戳转换错误"))?;
                        let time_str = dt.format("%Y-%m-%d %H:%M:%S").to_string();
                            
                        // 创建哈希信息
                        let hash_info = BlockHashInfo {
                            hash: hash_str.clone(),
                            slot,
                            parent_slot,
                            timestamp: now,
                        };
                        
                        // 更新内存中的最新哈希
                        {
                            let mut latest = self.latest_hash.lock().await;
                            *latest = Some(hash_info.clone());
                        }
                        
                        // 更新Redis缓存
                        if let Err(e) = self.cache_hash(&hash_info).await {
                            println!("警告: 缓存哈希到Redis失败: {}", e);
                        }
                        
                        // 终端显示最新哈希信息
                        println!("===== 最新区块哈希信息 =====");
                        println!("哈希: {}", hash_str);
                        println!("槽位: {}", slot);
                        println!("父槽位: {}", parent_slot);
                        println!("时间: {}", time_str);
                        println!("============================");
                    }
                },
                Err(e) => {
                    println!("接收更新时出错: {}", e);
                    break;
                }
            }
            
            // 检查心跳
            if heartbeat_interval.is_elapsed() {
                heartbeat_interval.tick().await;
                println!("监控服务心跳 - 正在运行中...");
            }
        }
        
        println!("区块监控流已结束");
        Ok(())
    }
    
    /// 获取最新哈希(先检查缓存,必要时通过监控获取)
    pub async fn get_latest_hash(&self) -> Result<Option<BlockHashInfo>> {
        // 先尝试从缓存获取
        if let Some(cached) = self.get_cached_hash().await? {
            println!("使用缓存的哈希数据");
            return Ok(Some(cached));
        }
        
        // 从内存中获取
        let latest = self.latest_hash.lock().await.clone();
        
        Ok(latest)
    }
}

// 测试函数
#[tokio::test]
async fn test_grpc_latest_hash_with_redis() -> Result<()> {
    println!("===== 开始测试gRPC最新哈希监控和Redis缓存功能 =====");
    
    // 创建监控实例
    let hash_monitor = HashMonitor::new().await?;
    
    println!("按Ctrl+C终止测试...");
    
    // 由于这是一个长时间运行的监控程序，我们直接执行监控
    // 在实际应用中，你可能希望将其设置为后台任务
    hash_monitor.start_monitoring().await?;
    
    Ok(())
}

// 主函数，方便直接运行
#[tokio::main]
async fn main() -> Result<()> {
    // 运行测试函数
    test_grpc_latest_hash_with_redis().await
} 