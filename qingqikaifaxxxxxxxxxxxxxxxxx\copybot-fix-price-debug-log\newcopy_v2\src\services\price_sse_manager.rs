// 这是一个新文件: newcopy_v2/src/services/price_sse_manager.rs
use tokio::sync::mpsc;
use tracing::{info, warn};
use std::sync::{Arc, Mutex};
use axum::response::sse::Event;
use std::convert::Infallible;

/// 用于管理所有Server-Sent Events (SSE)价格订阅和广播的服务
#[derive(Debug, Default)]
pub struct PriceSseManager {
    /// 全局订阅者列表，包含所有已连接客户端的发送通道
    subscribers: Vec<mpsc::Sender<Result<Event, Infallible>>>,
}

impl PriceSseManager {
    /// 创建一个新的管理器实例
    pub fn new() -> Arc<Mutex<Self>> {
        Arc::new(Mutex::new(Self::default()))
    }

    /// 注册一个新的订阅者
    pub fn register(&mut self, sender: mpsc::Sender<Result<Event, Infallible>>) {

        self.subscribers.push(sender);
    }

    /// 向所有订阅了的客户端广播新价格
    pub fn broadcast(&mut self, mint: &str, price: f64) {
        if self.subscribers.is_empty() {
            return;
        }

        let message = format!(r#"{{"mint":"{}","price":{}}}"#, mint, price);
        let event = Event::default().data(message);
        
        // 使用 retain 来遍历并移除那些已经断开连接（发送失败）的通道
        self.subscribers.retain(|sender| {
            match sender.try_send(Ok(event.clone())) {
                Ok(_) => true, // 保留
                Err(e) => {
                    if let mpsc::error::TrySendError::Closed(_) = e {
                        warn!("检测到SSE客户端断开连接，正在清理订阅。");
                        false // 从列表中移除
                    } else {
                        // 如果通道已满，暂时保留
                        true 
                    }
                }
            }
        });
    }
} 