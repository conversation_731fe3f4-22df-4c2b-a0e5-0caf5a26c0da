{"错误处理Result": {"prefix": "result", "body": ["fn ${1:function_name}(${2:args}) -> Result<${3:ReturnType}, ${4:anyhow::Error}> {", "    ${5:// 实现}", "    Ok(${6:result})", "}"], "description": "创建返回Result的函数"}, "Solana程序调用": {"prefix": "solcpi", "body": ["let ix = solana_sdk::instruction::Instruction {", "    program_id: ${1:program_id},", "    accounts: vec![", "        ${2:// 账户元数据}", "    ],", "    data: ${3:instruction_data},", "};", "", "let transaction = solana_sdk::transaction::Transaction::new_with_payer(", "    &[ix],", "    Some(&${4:payer.pubkey()}),", ");", "", "let blockhash = ${5:rpc_client}.get_latest_blockhash().await?;", "let signed_transaction = solana_sdk::transaction::Transaction::sign(", "    &[&${6:payer}],", "    transaction,", "    blockhash,", ");", "", "${5:rpc_client}.send_and_confirm_transaction(&signed_transaction).await?"], "description": "创建Solana程序调用模板"}, "Token账户创建": {"prefix": "splacc", "body": ["let ata = spl_associated_token_account::get_associated_token_address(", "    &${1:wallet_address},", "    &${2:token_mint},", ");", "", "// 检查ATA是否存在", "if ${3:rpc_client}.get_account(&ata).await.is_err() {", "    // 创建ATA", "    let create_ata_ix = spl_associated_token_account::instruction::create_associated_token_account(", "        &${4:payer.pubkey()},", "        &${1:wallet_address},", "        &${2:token_mint},", "        &spl_token::id(),", "    );", "    ", "    let transaction = solana_sdk::transaction::Transaction::new_with_payer(", "        &[create_ata_ix],", "        Some(&${4:payer.pubkey()}),", "    );", "    ", "    let blockhash = ${3:rpc_client}.get_latest_blockhash().await?;", "    let signed_transaction = solana_sdk::transaction::Transaction::sign(", "        &[&${4:payer}],", "        transaction,", "        blockhash,", "    );", "    ", "    ${3:rpc_client}.send_and_confirm_transaction(&signed_transaction).await?;", "}"], "description": "创建SPL Token关联账户"}, "异步函数": {"prefix": "asyncfn", "body": ["async fn ${1:function_name}(${2:args}) -> Result<${3:ReturnType}, ${4:anyhow::Error}> {", "    ${5:// 实现}", "    Ok(${6:result})", "}"], "description": "创建异步函数"}, "Raydium交易模板": {"prefix": "raydium", "body": ["// 创建Raydium交易指令", "let ix = amm_cli::instructions::${1:swap_base_in}(", "    ${2:args},", ")?;", "", "// 构建交易", "let transaction = solana_sdk::transaction::Transaction::new_with_payer(", "    &[ix],", "    Some(&${3:payer.pubkey()}),", ");", "", "// 签名并发送交易", "let blockhash = ${4:rpc_client}.get_latest_blockhash().await?;", "let signed_transaction = solana_sdk::transaction::Transaction::sign(", "    &[&${3:payer}],", "    transaction,", "    blockhash,", ");", "", "// 发送并确认交易", "let signature = ${4:rpc_client}.send_and_confirm_transaction(&signed_transaction).await?;", "println!(\"交易已确认: {}\", signature);"], "description": "创建Raydium交易模板"}, "模块定义": {"prefix": "moddef", "body": ["pub mod ${1:module_name} {", "    use super::*;", "    ", "    ${2:// 实现}", "}"], "description": "创建模块定义"}, "结构体定义": {"prefix": "structdef", "body": ["#[derive(Debug${1:, <PERSON><PERSON>, PartialEq})]", "pub struct ${2:<PERSON>ruct<PERSON>ame} {", "    ${3:// 字段}", "}", "", "impl ${2:<PERSON><PERSON>ct<PERSON>ame} {", "    pub fn new(${4:args}) -> Self {", "        Self {", "            ${5:// 初始化}", "        }", "    }", "    ", "    ${6:// 方法}", "}"], "description": "创建结构体定义和实现"}, "新建测试文件": {"prefix": "newtest", "body": ["#[cfg(test)]", "mod tests {", "    use super::*;", "    use solana_sdk::signer::keypair::Keypair;", "    use solana_client::rpc_client::RpcClient;", "    use anyhow::Result;", "    use std::str::FromStr;", "", "    // 在实际测试中设置正确的RPC URL", "    const RPC_URL: &str = \"https://api.mainnet-beta.solana.com\";", "", "    #[tokio::test]", "    async fn test_${1:feature_name}() -> Result<()> {", "        // 初始化客户端", "        let rpc_client = RpcClient::new(RPC_URL.to_string());", "        ", "        // 初始化测试数据", "        ${2:// 准备测试数据}", "        ", "        // 执行测试操作", "        ${3:// 测试代码}", "        ", "        // 验证结果", "        ${4:// 断言和验证}", "        ", "        Ok(())", "    }", "}"], "description": "创建新的测试文件模板"}, "买交易测试": {"prefix": "buytest", "body": ["#[tokio::test]", "async fn test_buy_transaction() -> Result<()> {", "    // 初始化客户端", "    let rpc_client = RpcClient::new(RPC_URL.to_string());", "    ", "    // 加载钱包和参数", "    let payer = Keypair::from_bytes(&${1:payer_bytes})?;", "    let pool_address = ${2:pool_address};", "    let amount = ${3:amount};", "    ", "    // 参考 real_buy_transaction_test.rs 进行实现", "    // 执行买操作", "    let result = ${4:execute_buy}(&rpc_client, &payer, &pool_address, amount).await?;", "    ", "    // 验证交易结果", "    assert!(result.${5:is_success});", "    println!(\"交易成功: {}\", result.${6:signature});", "    ", "    Ok(())", "}"], "description": "创建买交易测试，参考tests/real_buy_transaction_test.rs"}, "卖交易测试": {"prefix": "selltest", "body": ["#[tokio::test]", "async fn test_sell_transaction() -> Result<()> {", "    // 初始化客户端", "    let rpc_client = RpcClient::new(RPC_URL.to_string());", "    ", "    // 加载钱包和参数", "    let payer = Keypair::from_bytes(&${1:payer_bytes})?;", "    let token_address = ${2:token_address};", "    let amount = ${3:amount};", "    ", "    // 参考 real_sell_transaction_test.rs 进行实现", "    // 执行卖操作", "    let result = ${4:execute_sell}(&rpc_client, &payer, &token_address, amount).await?;", "    ", "    // 验证交易结果", "    assert!(result.${5:is_success});", "    println!(\"交易成功: {}\", result.${6:signature});", "    ", "    Ok(())", "}"], "description": "创建卖交易测试，参考tests/real_sell_transaction_test.rs"}}