use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::instruction::Instruction;
use std::env;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use anyhow::{Result, anyhow};
use std::fs::File;
use std::io::BufReader;
use std::collections::HashMap;

/// 钱包配置
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WalletConfig {
    pub follow_percentage: f64,
    pub slippage_percentage: f64,
    pub tip_percentage: f64,
    pub min_price_multiplier: f64,
    pub max_price_multiplier: f64,
    #[serde(default)]
    pub priority_fee: Option<u64>,
    #[serde(default)]
    pub compute_unit_limit: Option<u32>,
}

/// 监控配置
#[derive(Debug, Serialize, Deserialize)]
pub struct MonitorConfigs {
    pub sol_address: String,
    pub unwanted_key: String,
    pub targets: Vec<String>,
    pub wallets: HashMap<String, WalletConfig>,
}

impl MonitorConfigs {
    /// 从文件加载配置
    pub fn load() -> Result<Self> {
        let config_path = "monitor_addrs.json";  // 从根目录读取
        let file = File::open(config_path)
            .map_err(|e| anyhow!("无法读取监控配置文件: {}", e))?;
        
        let reader = BufReader::new(file);
        let configs: MonitorConfigs = serde_json::from_reader(reader)
            .map_err(|e| anyhow!("无法解析监控配置文件: {}", e))?;
        
        Ok(configs)
    }

    /// 获取指定钱包的配置
    pub fn get_wallet_config(&self, wallet_address: &str) -> Option<&WalletConfig> {
        self.wallets.get(wallet_address)
    }
}

/// 计算交易小费
/// 
/// # 参数
/// * `trade_amount_sol` - 交易金额(SOL)
/// * `wallet_address` - 钱包地址
/// 
/// # 返回值
/// 返回计算后的小费金额(SOL)
pub fn calculate_tip(
    trade_amount_sol: f64,
    wallet_address: &str,
) -> f64 {
    // 加载监控配置
    let configs = match MonitorConfigs::load() {
        Ok(cfg) => cfg,
        Err(_) => return 0.0005 // 如果加载失败，返回最小小费
    };
    
    // 获取钱包配置
    let wallet_config = match configs.get_wallet_config(wallet_address) {
        Some(cfg) => cfg,
        None => return 0.0005 // 如果找不到配置，返回最小小费
    };
    
    // 使用钱包配置中的小费百分比计算小费
    // 例如：如果交易金额是1 SOL，小费百分比是1%，则小费是0.01 SOL
    let tip_amount = trade_amount_sol * wallet_config.tip_percentage / 100.0;
    
    // 确保小费在合理范围内（最小0.0005 SOL，最大1 SOL）
    tip_amount.max(0.0005).min(1.0)
}

/// 构建计算预算指令
pub fn build_compute_budget_instructions(cu_limit: u32, cu_price: u64) -> Vec<Instruction> {
    vec![
        ComputeBudgetInstruction::set_compute_unit_limit(cu_limit),
        ComputeBudgetInstruction::set_compute_unit_price(cu_price),
    ]
}

/// 获取钱包的价格范围
pub fn get_wallet_price_range(wallet_address: &str) -> Result<(f64, f64)> {
    let configs = MonitorConfigs::load()?;
    let wallet_config = configs.get_wallet_config(wallet_address)
        .ok_or_else(|| anyhow!("未找到钱包配置: {}", wallet_address))?;
    
    Ok((wallet_config.min_price_multiplier, wallet_config.max_price_multiplier))
}

/// 紧急情况下获取价格范围 - 不读取配置文件，直接返回一个非常宽泛的价格范围
/// 用于快速卖出情况，确保不会因价格检查而失败交易
pub fn get_wallet_price_range_emergency() -> (f64, f64) {
    // 返回一个非常宽泛的价格范围，确保任何价格都在范围内
    (0.000000001, 1000.0)
}

/// 获取钱包的跟单比例
pub fn get_wallet_follow_percentage(wallet_address: &str) -> Result<f64> {
    let configs = MonitorConfigs::load()?;
    let wallet_config = configs.get_wallet_config(wallet_address)
        .ok_or_else(|| anyhow!("未找到钱包配置: {}", wallet_address))?;
    
    Ok(wallet_config.follow_percentage)
}

/// 小费计算配置
pub struct TipConfig {
    pub min_tip_sol: f64,        // 最小小费（SOL）
    pub max_tip_sol: f64,        // 最大小费（SOL）
    pub default_tip_sol: f64,    // 默认小费（SOL）
    pub tip_percentage: f64,     // 默认小费百分比
}

impl Default for TipConfig {
    fn default() -> Self {
        Self {
            min_tip_sol: 0.0005,  // 最小0.0005 SOL
            max_tip_sol: 0.01,    // 最大0.01 SOL
            default_tip_sol: 0.001, // 默认0.001 SOL
            tip_percentage: 0.1,   // 默认0.1%
        }
    }
}

/// 结合 CU 设置，找到最接近的小费档位
pub fn estimate_tip_cu_config(trade_amount_lamports: u64) -> (u32, u64) {
    let tip_target = calculate_tip(trade_amount_lamports as f64 / LAMPORTS_PER_SOL as f64, "default");
    let tip_lamports = (tip_target * LAMPORTS_PER_SOL as f64) as u64;

    let candidate_levels = vec![
        (250_000, 1),
        (250_000, 2),
        (300_000, 2),
        (400_000, 2),
        (250_000, 3),
        (400_000, 3),
        (600_000, 3),
        (600_000, 4),
        (800_000, 4),
        (1_000_000, 5),
    ];

    // 遍历候选档位，查找最合适的小费配置
    for (cu_limit, cu_price) in candidate_levels {
        let cost = cu_limit as u64 * cu_price as u64;
        if cost >= tip_lamports {
            return (cu_limit, cu_price); // 返回合适的 CU 配置
        }
    }

    // 如果没有找到合适的，使用最保守配置
    (250_000, 1)
}

/// 根据交易重要性以及其他因素，获取综合优化的Jito小费
pub fn get_optimized_jito_tip(
    trade_amount_lamports: u64, 
    is_high_priority: bool,
    is_congested: bool
) -> f64 {
    let base_tip = calculate_tip(trade_amount_lamports as f64 / LAMPORTS_PER_SOL as f64, "default");
    
    // 高优先级交易
    let priority_multiplier = if is_high_priority {
        1.5 // 增加50%
    } else {
        1.0
    };
    
    // 网络拥堵状况
    let congestion_multiplier = if is_congested {
        2.0 // 拥堵时翻倍
    } else {
        1.0
    };
    
    // 应用乘数
    let final_tip = base_tip * priority_multiplier * congestion_multiplier;
    
    // 确保在限制范围内
    final_tip.min(1.0) // 最高不超过1 SOL
} 