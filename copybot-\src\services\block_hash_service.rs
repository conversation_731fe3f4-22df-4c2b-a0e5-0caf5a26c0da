use anyhow::{Result, anyhow};
use tokio::sync::RwLock;
use std::collections::{VecDeque, HashMap};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use chrono::{DateTime, Utc};
use futures_util::{StreamExt, SinkExt};
use tonic::transport::ClientTlsConfig;
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::geyser::{
    CommitmentLevel, SubscribeRequest, SubscribeRequestFilterBlocks,
    subscribe_update::UpdateOneof,
    SubscribeRequestFilterBlocksMeta,
};
use std::sync::Arc;

use crate::common::logger::Logger;

/// 区块哈希信息结构
#[derive(Debug, Clone)]
pub struct BlockHashInfo {
    pub hash: String,
    pub slot: u64,
    pub parent_slot: u64,
    pub timestamp: u64,
}

/// 区块哈希服务
pub struct BlockHashService {
    endpoint: String,
    latest_hashes: Arc<RwLock<VecDeque<BlockHashInfo>>>,
    max_hashes: usize,
    logger: Logger,
}

impl BlockHashService {
    /// 创建新的区块哈希服务实例
    pub fn new(endpoint: &str, max_hashes: Option<usize>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            latest_hashes: Arc::new(RwLock::new(VecDeque::with_capacity(max_hashes.unwrap_or(3)))),
            max_hashes: max_hashes.unwrap_or(3),
            logger: Logger::new("BlockHashService".to_string()),
        }
    }
    
    /// 获取最新的哈希信息
    pub async fn get_latest_hash(&self) -> Option<BlockHashInfo> {
        let hashes = self.latest_hashes.read().await;
        hashes.front().cloned()
    }
    
    /// 获取所有缓存的哈希信息
    pub async fn get_all_hashes(&self) -> Vec<BlockHashInfo> {
        let hashes = self.latest_hashes.read().await;
        hashes.iter().cloned().collect()
    }
    
    /// 添加新的哈希信息到缓存
    async fn add_hash(&self, hash_info: BlockHashInfo) {
        let mut hashes = self.latest_hashes.write().await;
        
        // 避免重复添加相同的哈希
        if hashes.iter().any(|h| h.hash == hash_info.hash) {
            return;
        }
        
        // 在队列前方添加新哈希
        hashes.push_front(hash_info);
        
        // 如果超过最大数量，移除最旧的哈希
        while hashes.len() > self.max_hashes {
            hashes.pop_back();
        }
    }
    
    /// 创建gRPC客户端
    async fn create_client(&self) -> Result<GeyserGrpcClient<impl tonic::service::Interceptor>> {
        self.logger.log(format!("正在连接到gRPC端点: {}", self.endpoint));
        
        // 获取域名部分用于TLS配置
        let domain = self.endpoint.split(':').next().unwrap_or(&self.endpoint);
        
        // 格式化端点地址
        let formatted_endpoint = if !self.endpoint.starts_with("http") {
            if self.endpoint.contains(":443") {
                format!("https://{}", self.endpoint)
            } else {
                format!("http://{}", self.endpoint)
            }
        } else {
            self.endpoint.clone()
        };
        
        // 使用TLS配置连接
        let tls_config = ClientTlsConfig::new().domain_name(domain);
        
        match GeyserGrpcClient::connect(
            formatted_endpoint.clone(),
            None::<String>,
            Some(tls_config),
        ) {
            Ok(client) => {
                self.logger.log("成功连接到Yellowstone gRPC服务");
                Ok(client)
            },
            Err(e) => {
                // 尝试不使用TLS
                // self.logger.debug(format!("带TLS连接失败 ({}), 尝试不使用TLS配置", e));
                match GeyserGrpcClient::connect(
                    formatted_endpoint.clone(),
                    None::<String>,
                    None,
                ) {
                    Ok(client) => {
                        self.logger.log("成功连接到Yellowstone gRPC服务(无TLS)");
                        Ok(client)
                    },
                    Err(e2) => {
                        Err(anyhow!("无法连接到gRPC服务: 带TLS错误: {}, 无TLS错误: {}", e, e2))
                    }
                }
            }
        }
    }
    
    /// 启动哈希监控服务
    pub async fn start_monitoring(&self) -> Result<()> {
        self.logger.log("启动区块哈希监控服务 (订阅 BlocksMeta)... GGM");
        
        // 创建gRPC客户端
        let mut client = self.create_client().await?;
        
        // 创建订阅请求
        let mut blocks_meta_filter = HashMap::new();
        blocks_meta_filter.insert("blocks_meta".to_string(), SubscribeRequestFilterBlocksMeta {});
        
        let request = SubscribeRequest {
            blocks_meta: blocks_meta_filter,
            commitment: Some(CommitmentLevel::Finalized as i32),
            accounts: HashMap::new(),
            slots: HashMap::new(),
            transactions: HashMap::new(),
            entry: HashMap::new(),
            blocks: HashMap::new(),
            accounts_data_slice: vec![],
            subscribe_banking_transaction_results: false,
            ping: None,
        };

        // 发送订阅请求
        let (mut write, mut read) = client.subscribe().await?;
        write.send(request).await.map_err(|e| anyhow!("发送订阅请求失败: {}", e))?;
        
        // 处理响应
        while let Some(msg) = read.next().await {
            match msg {
                Ok(response) => {
                    if let Some(update_oneof) = response.update_oneof {
                        match update_oneof {
                            UpdateOneof::BlockMeta(block_meta) => {
                                // 更新区块哈希缓存
                                let mut cache = self.latest_hashes.write().await;
                                cache.push_front(BlockHashInfo {
                                    hash: block_meta.blockhash,
                                    slot: block_meta.slot,
                                    parent_slot: block_meta.parent_slot,
                                    timestamp: block_meta.block_time.map(|t| t.timestamp as u64).unwrap_or(0),
                                });
                                
                                // 保持缓存大小在限制内
                                while cache.len() > self.max_hashes {
                                    cache.pop_back();
                                }
                            }
                            _ => {}
                        }
                    }
                }
                Err(e) => {
                    self.logger.error(format!("接收区块元数据更新时出错: {}", e));
                    break;
                }
            }
        }

        Ok(())
    }
    
    /// 启动后台监控任务
    pub async fn start_background_monitoring(&self) -> Result<()> {
        self.logger.log("启动区块哈希后台监控任务...");
        
        // 克隆自身引用用于任务
        let service_clone = self.clone();
        
        // 启动后台任务
        tokio::spawn(async move {
            let max_retries = 10;
            let mut retry_count = 0;
            let mut backoff_seconds = 5;
            let mut received_hash_count = 0; // 添加哈希计数器
            let mut last_stats_time = SystemTime::now(); // 上次统计时间
            
            loop {
                match service_clone.start_monitoring().await {
                    Ok(_) => {
                        // 监控正常结束，重新启动
                        service_clone.logger.log("区块哈希监控服务正常结束，准备重新启动...");
                        retry_count = 0; // 重置重试计数
                        backoff_seconds = 5; // 重置退避时间
                    },
                    Err(e) => {
                        retry_count += 1;
                        if retry_count > max_retries {
                            service_clone.logger.error(format!("区块哈希监控失败次数超过最大重试次数 ({}): {}", max_retries, e));
                            break;
                        }
                        
                        service_clone.logger.warn(format!(
                            "区块哈希监控出错 (重试 {}/{}): {}，将在 {} 秒后重试",
                            retry_count, max_retries, e, backoff_seconds
                        ));
                        
                        // 使用指数退避策略
                        tokio::time::sleep(Duration::from_secs(backoff_seconds)).await;
                        backoff_seconds = std::cmp::min(backoff_seconds * 2, 60); // 最大退避60秒
                    }
                }
            }
            
            service_clone.logger.error("区块哈希监控服务已停止");
        });
        
        Ok(())
    }
}

// 实现Clone特征，用于后台任务
impl Clone for BlockHashService {
    fn clone(&self) -> Self {
        Self {
            endpoint: self.endpoint.clone(),
            latest_hashes: Arc::clone(&self.latest_hashes),
            max_hashes: self.max_hashes,
            logger: self.logger.clone(),
        }
    }
} 