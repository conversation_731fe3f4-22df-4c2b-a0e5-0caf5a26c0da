// 这是一个新文件: newcopy_v2/src/services/transaction_tracker.rs
use std::sync::Arc;
use std::time::Duration;
use dashmap::DashMap;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::signature::Signature;
use solana_sdk::pubkey::Pubkey;
use tokio::sync::{mpsc, Mutex};
use tracing::{info, warn, error};
use crate::services::sol_price_oracle::SolPriceOracle;
use crate::services::trade_event_manager::TradeEventManager;
use crate::shared::types::{HotPathTrade, TradeEvent, TradeStatus, TradeType, WalletConfig};
use uuid::Uuid;
use chrono::Utc;
use crate::services::price_broadcast::PriceBroadcastManager;
use crate::services::sell_executor::SellExecutor;
use crate::services::bonk_sell_executor::BonkSellExecutor;
use crate::services::sell_executor_trait::SellExecutorTrait;
use crate::services::trade_lifecycle_actor::TradeLifecycleActor;
use crate::services::auto_suspend_manager::AutoSuspendManager;

/// 用于提交给跟踪服务的新交易请求
pub struct TrackRequest {
    pub trade_type: TradeType,
    pub signature: Signature,
    pub mint: String,
    pub sol_amount: f64,
    pub token_amount: u64,
    pub user_wallet: String,
    pub entry_sol_amount_usd: Option<f64>, // 仅在卖出时提供，用于计算利润
    // 以下字段仅在跟单买入时提供，用于成功确认后注册卖出策略
    pub trade_info: Option<HotPathTrade>,
    pub wallet_config: Option<WalletConfig>,
    pub executor_type: String, // 新增：记录执行器类型 "pump" | "bonk"
}

/// 内部用于跟踪的交易状态
pub struct TrackedTrade {
    pub event: TradeEvent,
    pub trade_info: Option<HotPathTrade>,
    pub wallet_config: Option<WalletConfig>,
    pub retry_count: u32, // 重试次数
    pub executor_type: String, // 执行器类型
}

/// 负责跟踪已提交交易的最终状态
#[derive(Clone)]
pub struct TransactionTracker {
    trade_event_manager: Arc<Mutex<TradeEventManager>>,
    sol_price_oracle: SolPriceOracle,
    pending_trades: Arc<DashMap<String, TrackedTrade>>, // Key: trade_id
    #[allow(dead_code)]
    rpc_client: Arc<RpcClient>,
    confirmation_timeout: Duration,
    sell_executor: SellExecutor,
    bonk_sell_executor: Option<BonkSellExecutor>,
    price_broadcast_manager: PriceBroadcastManager,
    revoked_mints: Arc<DashMap<Pubkey, u64>>,
    auto_suspend_manager: Arc<AutoSuspendManager>,
}

impl TransactionTracker {
    pub fn new(
        trade_event_manager: Arc<Mutex<TradeEventManager>>,
        sol_price_oracle: SolPriceOracle,
        rpc_url: &str,
        confirmation_timeout_ms: u64,
        sell_executor: SellExecutor,
        bonk_sell_executor: Option<BonkSellExecutor>,
        price_broadcast_manager: PriceBroadcastManager,
        revoked_mints: Arc<DashMap<Pubkey, u64>>,
        auto_suspend_manager: Arc<AutoSuspendManager>,
    ) -> Arc<Self> {
        Arc::new(Self {
            trade_event_manager,
            sol_price_oracle,
            pending_trades: Arc::new(DashMap::new()),
            rpc_client: Arc::new(RpcClient::new(rpc_url.to_string())),
            confirmation_timeout: Duration::from_millis(confirmation_timeout_ms),
            sell_executor,
            bonk_sell_executor,
            price_broadcast_manager,
            revoked_mints,
            auto_suspend_manager,
        })
    }

    /// 获取我们自己钱包的公钥，用于判断交易是否来自我们的钱包
    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.sell_executor.get_wallet_pubkey()
    }

    /// 获取撤销Mint列表的引用，用于跟卖策略信号传递
    pub fn get_revoked_mints(&self) -> Arc<DashMap<Pubkey, u64>> {
        self.revoked_mints.clone()
    }

    /// 启动后台任务，并返回一个用于提交跟踪请求的通道
    pub fn start(&self) -> mpsc::Sender<TrackRequest> {
        let (tx, mut rx) = mpsc::channel::<TrackRequest>(1024);
        let self_clone = self.clone();

        tokio::spawn(async move {
            info!("交易跟踪服务已启动。");
            while let Some(req) = rx.recv().await {
                self_clone.track_transaction(req);
            }
            warn!("交易跟踪服务接收通道已关闭。");
        });

        tx
    }
    
    /// 使用从Redis收到的真实交易数据来确认交易
    pub async fn confirm_transaction(&self, confirmed_trade: HotPathTrade) {
        let signature_str = &confirmed_trade.signature;

        let trade_id = if let Some(id) = self.find_trade_id_by_signature(signature_str) {
            id
        } else {
            // 如果签名未知，并且是卖出交易，检查是否是我们自己钱包的交易
            if confirmed_trade.trade_type == TradeType::Sell {
                // 获取我们的钱包公钥（Pump执行器）
                let our_pump_wallet_pubkey = self.sell_executor.get_wallet_pubkey().to_string();
                
                // 检查是否是我们的钱包（支持两个执行器）
                let is_our_wallet = if confirmed_trade.signer == our_pump_wallet_pubkey {
                    true
                } else if let Some(bonk_executor) = &self.bonk_sell_executor {
                    // 如果有Bonk执行器，也检查Bonk钱包（通常应该是同一个钱包）
                    confirmed_trade.signer == bonk_executor.get_wallet_pubkey().to_string()
                } else {
                    false
                };
                
                // 只有当卖出交易来自我们自己的钱包时，才增加撤销计数器（策略终止）
                if is_our_wallet {
                    let mint_pubkey = confirmed_trade.mint_pubkey;
                    info!("检测到我们钱包的外部卖出交易，撤销计数器将增加: {}", mint_pubkey);
                    self.revoked_mints.entry(mint_pubkey).and_modify(|count| *count += 1).or_insert(1);
                } else {
                    // 如果是其他钱包的卖出，不增加撤销计数器
                    info!("检测到外部钱包 {} 的卖出交易，不影响我们的策略", confirmed_trade.signer);
                }
            }
            warn!("收到一个未跟踪或已超时的交易确认签名: {}", signature_str);
            return;
        };

        if let Some((_key, mut tracked_trade)) = self.pending_trades.remove(&trade_id) {
            if tracked_trade.event.status == TradeStatus::Pending || tracked_trade.event.status == TradeStatus::Retrying {
                // 简化日志 - 只显示签名
                info!("交易确认: {}", signature_str);
                tracked_trade.event.status = TradeStatus::Confirmed;

                // 如果是买入，使用真实的成交数量启动 Actor
                if tracked_trade.event.trade_type == TradeType::Buy {
                    if let Some(wallet_cfg) = tracked_trade.wallet_config.clone() {
                        
                        let actual_received_amount = confirmed_trade.token_amount;
                        info!("[Trade: {}] 链上确认买入成功，真实收到: {} raw tokens", trade_id, actual_received_amount);

                        // 读取当前撤销计数值，如果不存在则为0
                        let known_revocations = self.revoked_mints.get(&confirmed_trade.mint_pubkey).map_or(0, |v| *v.value());

                        // 根据协议类型选择正确的卖出执行器
                        let protocol = confirmed_trade.protocol.as_deref().unwrap_or("pump");
                        info!("[Trade: {}] 协议类型: {}", trade_id, protocol);

                        match protocol {
                            "bonk" => {
                                if let Some(bonk_sell_executor) = &self.bonk_sell_executor {
                                    info!("[Trade: {}] 使用Bonk卖出执行器启动交易生命周期管理", trade_id);
                                    let price_receiver = self.price_broadcast_manager.subscribe();
                                    
                                    // 使用Bonk执行器
                                    let executor: Arc<dyn SellExecutorTrait + Send + Sync> = Arc::new(bonk_sell_executor.clone());
                                    
                                    let actor = TradeLifecycleActor::new(
                                        confirmed_trade.clone(),
                                        wallet_cfg,
                                        actual_received_amount,
                                        executor,
                                        price_receiver,
                                        self.revoked_mints.clone(),
                                        known_revocations,
                                        Some(self.auto_suspend_manager.clone()),
                                    );

                                    tokio::spawn(actor.start());
                                } else {
                                    warn!("[Trade: {}] 检测到Bonk协议但未配置BonkSellExecutor，回退到Pump执行器", trade_id);
                                    
                                    // 回退到pump执行器
                                    let executor: Arc<dyn SellExecutorTrait + Send + Sync> = Arc::new(self.sell_executor.clone());
                                    let price_receiver = self.price_broadcast_manager.subscribe();

                                    let actor = TradeLifecycleActor::new(
                                        confirmed_trade.clone(),
                                        wallet_cfg,
                                        actual_received_amount,
                                        executor,
                                        price_receiver,
                                        self.revoked_mints.clone(),
                                        known_revocations,
                                        Some(self.auto_suspend_manager.clone()),
                                    );

                                    tokio::spawn(actor.start());
                                }
                            },
                            _ => {
                                info!("[Trade: {}] 使用Pump卖出执行器启动交易生命周期管理", trade_id);
                                // 使用pump执行器
                                let executor: Arc<dyn SellExecutorTrait + Send + Sync> = Arc::new(self.sell_executor.clone());
                                let price_receiver = self.price_broadcast_manager.subscribe();

                                let actor = TradeLifecycleActor::new(
                                    confirmed_trade.clone(),
                                    wallet_cfg,
                                    actual_received_amount,
                                    executor,
                                    price_receiver,
                                    self.revoked_mints.clone(),
                                    known_revocations,
                                    Some(self.auto_suspend_manager.clone()),
                                );

                                tokio::spawn(actor.start());
                            }
                        }

                    } else {
                        warn!("已确认的买入交易缺少 wallet_config，无法启动策略 Actor。签名: {}", signature_str);
                    }
                }

                // 广播确认事件
                let mut manager = self.trade_event_manager.lock().await;
                // 使用 confirmed_trade 的数据更新事件，确保广播的是最新信息
                tracked_trade.event.sol_amount = confirmed_trade.sol_cost;
                tracked_trade.event.token_amount = confirmed_trade.token_amount;
                // 重新计算 USD 金额，确保与实际成交量一致
                tracked_trade.event.usd_amount = confirmed_trade.sol_cost * tracked_trade.event.sol_price_usd;
                manager.broadcast(&tracked_trade.event);

            } else {
                // 如果状态不是Pending，说明可能已经被超时任务处理了，把它放回去
                self.pending_trades.insert(trade_id, tracked_trade);
            }
        }
    }

    /// 原子性地确认交易，广播事件，并返回完整的跟踪上下文。
    /// 这个方法现在主要用于非Actor启动的场景，或者可以被废弃/简化
    pub async fn confirm_and_get_context(&self, signature: &Signature) -> Option<TrackedTrade> {
        let signature_str = signature.to_string();
        let trade_id = self.find_trade_id_by_signature(&signature_str)?;

        // DashMap一次只允许一个可变借用，所以我们先移除，再处理
        if let Some((_key, mut tracked_trade)) = self.pending_trades.remove(&trade_id) {
            if tracked_trade.event.status == TradeStatus::Pending {
                info!("交易确认: {}", signature_str);
                tracked_trade.event.status = TradeStatus::Confirmed;
                
                // 广播确认事件
                let event_clone = tracked_trade.event.clone();
                let mut manager = self.trade_event_manager.lock().await;
                manager.broadcast(&event_clone);

                // --- 关键改动：同样在这里创建并启动新的 Actor ---
                 if tracked_trade.event.trade_type == TradeType::Buy {
                    if let (Some(trade_info), Some(wallet_cfg)) = (tracked_trade.trade_info.clone(), tracked_trade.wallet_config.clone()) {
                        let token_amt = tracked_trade.event.token_amount;

                        // 读取当前撤销计数值，如果不存在则为0
                        let known_revocations = self.revoked_mints.get(&trade_info.mint_pubkey).map_or(0, |v| *v.value());
                        
                        let executor: Arc<dyn SellExecutorTrait + Send + Sync> = Arc::new(self.sell_executor.clone());
                        let price_receiver = self.price_broadcast_manager.subscribe();

                        let actor = TradeLifecycleActor::new(
                            trade_info.clone(),
                            wallet_cfg.clone(),
                            token_amt,
                            executor,
                            price_receiver,
                            self.revoked_mints.clone(),
                            known_revocations,
                            Some(self.auto_suspend_manager.clone()),
                        );
                        tokio::spawn(actor.start());
                    }
                }

                Some(tracked_trade)
            } else {
                // 如果状态不是Pending，说明可能已经被超时任务处理了，把它放回去
                self.pending_trades.insert(trade_id, tracked_trade);
                None
            }
        } else {
            warn!("尝试确认一个未跟踪或已移除的交易: {}", signature_str);
            None
        }
    }

    /// 根据签名在待处理交易中查找对应的trade_id
    fn find_trade_id_by_signature(&self, signature: &str) -> Option<String> {
        self.pending_trades
            .iter()
            .find(|entry| entry.event.signature.to_string() == signature)
            .map(|entry| entry.key().clone())
    }

    /// 开始跟踪一笔新的交易
    fn track_transaction(&self, req: TrackRequest) {
        let trade_id = Uuid::new_v4().to_string();
        let sol_price_usd = self.sol_price_oracle.get_price_usd();
        let usd_amount = req.sol_amount * sol_price_usd;
        
        // 计算利润
        let profit_usd = if req.trade_type == TradeType::Sell {
            req.entry_sol_amount_usd.map(|entry_usd| usd_amount - entry_usd)
        } else {
            None
        };

        let event = TradeEvent {
            trade_id: trade_id.clone(),
            status: TradeStatus::Pending,
            trade_type: req.trade_type.clone(),
            signature: req.signature.to_string(),
            mint: req.mint.clone(),
            block_time: Utc::now().timestamp(),
            sol_price_usd,
            sol_amount: req.sol_amount,
            usd_amount,
            token_amount: req.token_amount,
            user_wallet: req.user_wallet.clone(),
            followed_wallet: req.trade_info.as_ref().map(|t| t.signer.clone()),
            profit_usd,
            failure_reason: None,
        };

        let tracked = TrackedTrade {
            event: event.clone(),
            trade_info: req.trade_info,
            wallet_config: req.wallet_config,
            retry_count: 0, // 初始重试次数为0
            executor_type: req.executor_type, // 存储执行器类型
        };
        
        // 立即广播 Pending 事件
        let self_clone = self.clone();
        tokio::spawn(async move {
            let mut manager = self_clone.trade_event_manager.lock().await;
            manager.broadcast(&event);
        });
        
        self.pending_trades.insert(trade_id.clone(), tracked);
        
        // 启动超时检查器
        self.start_timeout_checker(trade_id);
    }
    
    /// 为单个交易启动一个超时检查任务
    fn start_timeout_checker(&self, trade_id: String) {
        let self_clone = self.clone();
        tokio::spawn(async move {
            tokio::time::sleep(self_clone.confirmation_timeout).await;

            if let Some(mut tracked_trade) = self_clone.pending_trades.get_mut(&trade_id) {
                if tracked_trade.event.status == TradeStatus::Pending || tracked_trade.event.status == TradeStatus::Retrying {
                    warn!("交易超时未通过Redis确认: {}", tracked_trade.event.signature);

                    // 如果是卖出交易且重试次数未达上限，立即重试
                    if tracked_trade.event.trade_type == TradeType::Sell
                        && tracked_trade.trade_info.is_some()
                        && tracked_trade.wallet_config.is_some() {

                        let wallet_config = tracked_trade.wallet_config.clone().unwrap();
                        let max_attempts = wallet_config.sell_retry_max_attempts;

                        if tracked_trade.retry_count < max_attempts {
                            tracked_trade.retry_count += 1;
                            tracked_trade.event.status = TradeStatus::Retrying;

                            // 重试日志简化 - 只记录重试次数，具体签名在发送成功后打印

                            // 立即重试卖出
                            let trade_info = tracked_trade.trade_info.clone().unwrap();
                            let token_amount = tracked_trade.event.token_amount;

                            // 🚀 重试时使用最新实时价格
                            let current_price = self_clone.price_broadcast_manager.get_latest_price(&trade_info.mint_pubkey).unwrap();

                            // 调整滑点：每次重试增加用户配置的百分比，无最大限制
                            let original_slippage = wallet_config.sell_slippage_percentage
                                .unwrap_or(wallet_config.slippage_percentage);
                            let slippage_increment = wallet_config.sell_retry_slippage_increment;
                            let final_slippage = original_slippage + (tracked_trade.retry_count as f64 * slippage_increment);

                            // 立即重新发送卖出交易，但不创建新的跟踪记录
                            let executor_type = tracked_trade.executor_type.clone();
                            let retry_count = tracked_trade.retry_count;
                            let trade_id_for_retry = trade_id.clone();
                            let self_for_retry = self_clone.clone();

                            tokio::spawn(async move {
                                // 根据执行器类型选择对应的执行器
                                let result = match executor_type.as_str() {
                                    "bonk" => {
                                        if let Some(bonk_executor) = &self_for_retry.bonk_sell_executor {
                                            bonk_executor.build_and_send_sell_transaction(
                                                trade_info,
                                                &wallet_config,
                                                token_amount,
                                                current_price,
                                                Some(final_slippage),
                                                &format!("重试卖出#{}", retry_count),
                                            ).await
                                        } else {
                                            Err(anyhow::anyhow!("Bonk执行器未配置"))
                                        }
                                    }
                                    "pump" => {
                                        self_for_retry.sell_executor.build_and_send_sell_transaction(
                                            trade_info,
                                            &wallet_config,
                                            token_amount,
                                            current_price,
                                            Some(final_slippage),
                                            &format!("重试卖出#{}", retry_count),
                                        ).await
                                    }
                                    _ => {
                                        Err(anyhow::anyhow!("未知执行器类型: {}", executor_type))
                                    }
                                };

                                match result {
                                    Ok(new_signature) => {
                                        // 🚀 关键修复：更新DashMap中的签名，以便Redis确认时能找到
                                        if let Some(mut tracked_trade) = self_for_retry.pending_trades.get_mut(&trade_id_for_retry) {
                                            tracked_trade.event.signature = new_signature.to_string();
                                        }
                                    }
                                    Err(e) => {
                                        error!("重试卖出#{}: 失败", retry_count);
                                        // 不操作DashMap，让超时机制自然处理失败状态
                                    }
                                }
                            });

                            // 重新启动超时检查器
                            self_clone.start_timeout_checker(trade_id.clone());
                        } else {
                            // 重试次数已达上限，标记为失败
                            info!("重试失败: {}", tracked_trade.event.signature);
                            tracked_trade.event.status = TradeStatus::Failed;
                        }
                    } else {
                        // 不是卖出交易，标记为失败
                        tracked_trade.event.status = TradeStatus::Failed;
                    }

                    let event_clone = tracked_trade.event.clone();
                    let mut manager = self_clone.trade_event_manager.lock().await;
                    manager.broadcast(&event_clone);
                }
            }
        });
    }

    #[allow(dead_code)]
    async fn fetch_failure_reason(&self, signature_str: &str) -> String {
        let signature: Signature = match signature_str.parse() {
            Ok(sig) => sig,
            Err(_) => return "无效的签名格式".to_string(),
        };

        match self.rpc_client.get_transaction(&signature, solana_transaction_status::UiTransactionEncoding::Json).await {
            Ok(tx) => {
                if let Some(meta) = tx.transaction.meta {
                    if let Some(err) = meta.err {
                        format!("RPC确认失败: {:?}", err)
                    } else {
                        "交易成功但Redis未收到事件".to_string()
                    }
                } else {
                    "无法获取交易元数据".to_string()
                }
            }
            Err(e) => {
                format!("RPC请求失败: {}", e)
            }
        }
    }

    /// 使用外部提供的通道启动跟踪服务
    pub fn start_with_receiver(&self, mut rx: mpsc::Receiver<TrackRequest>) {
        let self_clone = self.clone();
        tokio::spawn(async move {
            info!("交易跟踪服务已启动。");
            while let Some(req) = rx.recv().await {
                self_clone.track_transaction(req);
            }
            warn!("交易跟踪服务接收通道已关闭。");
        });
    }
} 