use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    bs58,
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::{Instruction, AccountMeta},
    native_token::LAMPORTS_PER_SOL,
    pubkey::Pubkey,
    signature::{Keypair, Signer}, // 移除了未使用的 SeedDerivable
    system_instruction,
    transaction::Transaction,
    sysvar,
    system_program,
};
// 移除了未使用的 spl_associated_token_account::id as ata_program_id;
use spl_token;
use std::str::FromStr;
// 引入 Pump.fun 的卖出方法
use copy_trading_bot::dex::pump_fun::{get_pump_sell_method};
use spl_associated_token_account::get_associated_token_address;

// --- 配置常量 (与买入脚本保持一致) ---
const MINT_ADDRESS_STR: &str = "52NPLudv5oKNDEPm1LtcpeXEoDx9qe4h1QcTiLFapump";
const USER_SIGNER_STR: &str = "8hAMyQCnLRGcH5WYHNRoSPpCneFoTYjSiU9hbjrhvZjd";
const FEE_RECIPIENT_STR: &str = "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV"; // 小费接收者
const PUMP_PROGRAM_ID_STR: &str = "BXxgGt3akAghZviYHLh8KUh6vhXBht5wf86De6huTp95";
const GLOBAL_ACCOUNT_STR: &str = "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf";
const EVENT_AUTHORITY_STR: &str = "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1";

// --- 卖出特定配置 ---
const TARGET_PRICE_USD_PER_TOKEN: f64 = 0.**********;
const ASSUMED_SOL_PRICE_USD: f64 = 150.0; // !!! 重要假设 !!!
const SELL_SLIPPAGE_PERCENT: f64 = 1.0; // 允许 1% 的滑点

const TIP_AMOUNT_SOL: f64 = 0.*********; // 与买入相同
const COMPUTE_UNIT_LIMIT: u32 = 80_000; // 与买入相同
const COMPUTE_UNIT_PRICE: u64 = 6800; // 与买入相同

const RPC_URL: &str = "https://mainnet.helius-rpc.com/?api-key=40b3da8c-5adb-4822-a333-44cbfb58d3c5";
const PRIVATE_KEY_STR: &str = "2bnDXL2GvcNo6eAv7WhdSnqyFEsoKaZXxfL4HVRSWvtVoWD46ct5mmXGCSJZnh7F2yhsve2Uao2JSvgErB8jVRnX";

// 将固定小费改为动态计算小费
const DEFAULT_TIP_PERCENTAGE: f64 = 1.0; // 默认1%的小费百分比

#[tokio::test(flavor = "multi_thread")]
async fn test_execute_real_sell_transaction() {
    println!("--- 开始执行真实 Pump.fun 卖出交易测试 ---");
    println!("警告: 此操作将尝试卖出你账户中的 {} 代币!", MINT_ADDRESS_STR);
    println!("重要假设: 当前 SOL 价格为 {} USD", ASSUMED_SOL_PRICE_USD);

    // --- 1. 初始化 ---
    let rpc_client = RpcClient::new_with_commitment(RPC_URL.to_string(), CommitmentConfig::confirmed());

    let keypair_bytes = bs58::decode(PRIVATE_KEY_STR)
        .into_vec()
        .expect("bs58 解码失败");
    let signer = Keypair::from_bytes(&keypair_bytes)
        .expect("Keypair::from_bytes 失败");

    println!("从私钥加载的公钥: {}", signer.pubkey());
    let user_signer_pk_expected = Pubkey::from_str(USER_SIGNER_STR).unwrap();
    assert_eq!(signer.pubkey(), user_signer_pk_expected, "密钥对不匹配");
    println!(">> 公钥验证通过! <<");

    // 解析公钥
    let mint_pk = Pubkey::from_str(MINT_ADDRESS_STR).unwrap();
    let fee_recipient_pk = Pubkey::from_str(FEE_RECIPIENT_STR).unwrap();
    let pump_program_id = Pubkey::from_str(PUMP_PROGRAM_ID_STR).unwrap();
    let event_authority_pk = Pubkey::from_str(EVENT_AUTHORITY_STR).unwrap();
    let global_account_pk = Pubkey::from_str(GLOBAL_ACCOUNT_STR).unwrap();

    // 计算派生地址 (与买入相同)
    let (curve_address_pk, _) = Pubkey::find_program_address(
        &[b"bonding-curve", mint_pk.as_ref()],
        &pump_program_id
    );
    let associated_bonding_curve_pk = get_associated_token_address(
        &curve_address_pk,
        &mint_pk
    );
    let user_ata_pk = get_associated_token_address(
        &signer.pubkey(),
        &mint_pk
    );
    println!("用户 ATA 地址: {}", user_ata_pk);

    // --- 2. 获取要卖出的代币余额 ---
    println!("正在获取用户 ATA ({}) 的代币余额...", user_ata_pk);
    let token_balance_response = rpc_client.get_token_account_balance(&user_ata_pk);

    let token_balance = match token_balance_response {
        Ok(balance) => {
            if balance.ui_amount.unwrap_or(0.0) == 0.0 || balance.amount == "0" {
                println!("错误: 你的 ATA ({}) 中没有找到代币 {}。", user_ata_pk, mint_pk);
                panic!("代币余额为 0，无法执行卖出。");
            }
            println!("找到代币余额: {} (原始: {})", balance.ui_amount_string, balance.amount);
            balance.amount.parse::<u64>().expect("无法解析代币余额为 u64")
        }
        Err(e) => {
            if e.to_string().contains("AccountNotFound") || e.to_string().contains("could not find account") {
                println!("错误: 找不到你的 ATA ({})。你可能从未拥有过代币 {}。", user_ata_pk, mint_pk);
            } else {
                println!("错误: 获取代币余额失败: {}", e);
            }
            panic!("获取代币余额失败。");
        }
    };

    let token_amount_to_sell = token_balance; // 卖出全部
    println!("准备卖出的代币数量 (Lamports): {}", token_amount_to_sell);

    // --- 3. 计算最低 SOL 输出 (滑点控制) ---
    let target_sol_price_per_token = TARGET_PRICE_USD_PER_TOKEN / ASSUMED_SOL_PRICE_USD;
    let expected_sol_output = target_sol_price_per_token * (token_amount_to_sell as f64); // 注意: 这里是用 u64 的 token 量
    let min_sol_output = expected_sol_output * (1.0 - (SELL_SLIPPAGE_PERCENT / 100.0));
    let min_sol_output_lamports = (min_sol_output * LAMPORTS_PER_SOL as f64) as u64;

    println!("基于目标价 ${:.8}/Token 和 SOL ${}/SOL:", TARGET_PRICE_USD_PER_TOKEN, ASSUMED_SOL_PRICE_USD);
    println!("  - 预期收到 SOL: {:.8}", expected_sol_output);
    println!("  - 允许滑点: {}%", SELL_SLIPPAGE_PERCENT);
    println!("  - 最低接受 SOL: {:.8}", min_sol_output);
    println!("  - 最低接受 Lamports (用于指令): {}", min_sol_output_lamports);

    if min_sol_output_lamports == 0 {
        println!("警告: 计算出的最低接受 Lamports 为 0。这可能导致交易失败或接收极少的 SOL。");
        // 可以选择 panic 或继续，但这里我们继续并警告
    }


    // --- 4. 构建指令 ---
    let mut instructions = Vec::new();

    // a) 计算预算指令
    instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(COMPUTE_UNIT_LIMIT));
    instructions.push(ComputeBudgetInstruction::set_compute_unit_price(COMPUTE_UNIT_PRICE));
    println!("已添加 Compute Budget 指令");

    // b) 构建 Pump.fun 卖出指令
    let sell_discriminator = get_pump_sell_method();

    let mut sell_instruction_data: Vec<u8> = Vec::new();
    sell_instruction_data.extend_from_slice(&sell_discriminator);
    sell_instruction_data.extend_from_slice(&token_amount_to_sell.to_le_bytes());
    sell_instruction_data.extend_from_slice(&0u64.to_le_bytes());

    println!("Sell Discriminator (from project): {:?}", sell_discriminator);
    println!("  - Token Amount to Sell: {}", token_amount_to_sell);
    println!("  - Min SOL Output Param: 0 (忽略滑点)");

    // 账户列表 (参考买入并调整角色)
    // 1. Global (readonly)
    // 2. Fee Recipient (writable) - 接收手续费
    // 3. Mint (readonly)
    // 4. Bonding Curve (writable) - SOL 从这里出
    // 5. Assoc Bonding Curve ATA (writable) - Token 到这里入
    // 6. User ATA (writable) - Token 从这里出 (需要签名者授权)
    // 7. Signer (writable, signer) - 授权 User ATA 转出 + 支付 SOL 手续费
    // 8. System Program (readonly) - 用于可能的 SOL 转账? (保留)
    // 9. Associated Token Account Program (readonly)
    // 10. Token Program (readonly)
    // 11. Event Authority (readonly)
    // 12. Pump Program ID (readonly) - (这个账户在买入时也传了，保留)
    let sell_ix = Instruction {
        program_id: pump_program_id,
        accounts: vec![
            AccountMeta::new_readonly(global_account_pk, false),       // 1. Global
            AccountMeta::new(fee_recipient_pk, false),              // 2. Fee Recipient (Writable)
            AccountMeta::new_readonly(mint_pk, false),                // 3. Mint
            AccountMeta::new(curve_address_pk, false),                // 4. Bonding Curve (Writable)
            AccountMeta::new(associated_bonding_curve_pk, false),     // 5. Assoc Bonding Curve ATA (Writable)
            AccountMeta::new(user_ata_pk, false),                     // 6. User ATA (Writable)
            AccountMeta::new(signer.pubkey(), true),                  // 7. Signer (Writable + Signer)
            AccountMeta::new_readonly(system_program::id(), false),    // 8. System Program
            AccountMeta::new_readonly(spl_associated_token_account::id(), false), // 9. Associated Token Account Program
            AccountMeta::new_readonly(spl_token::id(), false),        // 10. Token Program
            AccountMeta::new_readonly(event_authority_pk, false),    // 11. Event Authority
            AccountMeta::new_readonly(pump_program_id, false),       // 12. Pump Program ID
        ],
        data: sell_instruction_data,
    };
    instructions.push(sell_ix);
    println!("已添加 Pump.fun Sell 指令");

    // c) 构建小费转账指令 (与买入相同)
    let tip_percentage = std::env::var("TIP_PERCENTAGE")
        .unwrap_or_else(|_| DEFAULT_TIP_PERCENTAGE.to_string())
        .parse::<f64>()
        .unwrap_or(DEFAULT_TIP_PERCENTAGE);

    // 计算小费金额为交易金额的百分比 (这里我们使用卖出金额的估计值)
    let estimated_sol_amount = token_amount_to_sell as f64 * target_sol_price_per_token / 1_000_000.0;
    let tip_amount_sol = (estimated_sol_amount * tip_percentage) / 100.0;

    // 确保小费在合理范围内
    let min_tip_sol = 0.0005; // 最小0.0005 SOL
    let max_tip_sol = 1.0;    // 最大1 SOL
    let tip_amount_sol = tip_amount_sol.max(min_tip_sol).min(max_tip_sol);

    let tip_amount_lamports = (tip_amount_sol * LAMPORTS_PER_SOL as f64) as u64;

    println!("小费百分比: {}%", tip_percentage);
    println!("估计交易金额 (SOL): {}", estimated_sol_amount);
    println!("小费金额 (SOL): {}", tip_amount_sol);
    println!("小费金额 (Lamports): {}", tip_amount_lamports);

    if tip_amount_lamports > 0 {
        let tip_transfer_ix = system_instruction::transfer(
            &signer.pubkey(),
            &fee_recipient_pk, // 小费也给同一个地址
            tip_amount_lamports,
        );
        instructions.push(tip_transfer_ix);
        println!("已添加 Tip Transfer 指令 ({} Lamports)", tip_amount_lamports);
    } else {
         println!("小费为 0，跳过 Tip Transfer 指令");
    }

    // --- 5. 构建并发送交易 ---
    let recent_blockhash = rpc_client
        .get_latest_blockhash()
        .expect("获取最新 blockhash 失败");
    println!("最新 Blockhash: {}", recent_blockhash);

    let tx = Transaction::new_signed_with_payer(
        &instructions,
        Some(&signer.pubkey()),
        &[&signer],
        recent_blockhash,
    );

    println!("交易已构建并签名，包含 {} 条指令。准备模拟...", instructions.len());
    println!("模拟交易使用的账户（部分关键账户）:");
    println!("  - Signer (用户): {}", signer.pubkey());
    println!("  - Mint: {}", mint_pk);
    println!("  - User ATA (卖出源): {}", user_ata_pk);
    println!("  - Bonding Curve: {}", curve_address_pk);
    println!("  - Assoc Bonding Curve ATA (卖入目标): {}", associated_bonding_curve_pk);


    // --- 6. 模拟交易 ---
    println!("尝试模拟交易...");
    match rpc_client.simulate_transaction(&tx) {
        Ok(sim_response) => {
            if let Some(logs) = &sim_response.value.logs {
                eprintln!("--- 模拟日志 开始 ---");
                for log in logs {
                    eprintln!("- {}", log);
                }
                eprintln!("--- 模拟日志 结束 ---");
            }

            if let Some(err) = sim_response.value.err {
                eprintln!("错误: 交易模拟失败!");
                eprintln!("错误详情: {:?}", err);
                // 常见的卖出失败原因：
                // - Slippage: 价格波动导致无法满足 min_sol_output_lamports
                // - InsufficientFunds: Bonding curve 不够 SOL 来买你的币 (不太可能在 Pump 阶段)
                // - AccountNotFound / Uninitialized: 某个账户状态不对
                // - BondingCurveCompleted: 池子已结束
                panic!("交易模拟失败，请检查错误和日志。");
            } else {
                println!("交易模拟成功!");
            }
        }
        Err(e) => {
            eprintln!("错误: 调用模拟交易 RPC 时出错: {}", e);
            panic!("模拟交易 RPC 调用失败");
        }
    }

    // --- 7. 发送并确认交易 ---
    println!("模拟成功，准备发送并确认真实交易...");
    match rpc_client.send_and_confirm_transaction_with_spinner(&tx) {
        Ok(signature) => {
            println!("交易成功发送并确认！");
            println!("签名: {}", signature);
            println!("在 Solscan 上查看: https://solscan.io/tx/{}", signature);
        }
        Err(e) => {
            eprintln!("错误: 发送或确认交易失败: {}", e);
            panic!("交易失败");
        }
    }

    println!("--- 真实 Pump.fun 卖出交易测试执行完毕 ---");
} 