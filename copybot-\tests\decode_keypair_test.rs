use solana_sdk::signature::Keypair;
use solana_sdk::signature::Signer;
use bs58;

// The specific private key string provided by the user
const PRIVATE_KEY_STR: &str = "2bnDXL2GvcNo6eAv7WhdSnqyFEsoKaZXxfL4HVRSWvtVoWD46ct5mmXGCSJZnh7F2yhsve2Uao2JSvgErB8jVRnX";

#[test]
fn test_decode_keypair_strictly() {
    println!("--- 开始解码特定私钥字符串 (严格模拟 xiaoP 方法) ---");
    println!("私钥字符串: {}", PRIVATE_KEY_STR);

    // 1. 使用 bs58 解码
    let keypair_bytes = bs58::decode(PRIVATE_KEY_STR)
        .into_vec()
        .expect("bs58 解码失败");

    println!("bs58 解码后的字节长度: {}", keypair_bytes.len());
    println!(
        "解码后的字节 (Hex): [{}]",
        keypair_bytes.iter().map(|b| format!("{:02x}", b)).collect::<Vec<String>>().join(", ")
    );

    // 2. 严格按照 xiaoP 的方式，直接使用 Keypair::from_bytes
    //    (不处理 65 字节情况，预期如果长度不是 64 会在此处 panic)
    println!("尝试直接使用解码后的 {} 字节调用 Keypair::from_bytes...", keypair_bytes.len());
    let signer = Keypair::from_bytes(&keypair_bytes)
        .expect(&format!(
            "Keypair::from_bytes 失败 - 输入长度为 {} 字节，但该函数严格要求 64 字节!",
            keypair_bytes.len()
        ));

    // 如果上面没有 panic (理论上不可能用这个输入)，则打印公钥
    println!("--- 解码和加载成功 (理论上不应发生) ---");
    println!("派生出的公钥: {}", signer.pubkey());
} 