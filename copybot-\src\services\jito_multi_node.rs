use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::runtime::Handle;
use tokio::spawn;
use tokio::time::sleep;
use reqwest::Client;
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct JitoNode {
    pub name: String,
    pub url: String,
    pub latency: Arc<Mutex<Option<u64>>>, // 毫秒
    pub is_healthy: Arc<Mutex<bool>>,
    pub last_check: Arc<Mutex<Instant>>,
}

// 构建一个静态的节点列表
pub static JITO_NODES: Lazy<Vec<JitoNode>> = Lazy::new(|| {
    vec![
        JitoNode {
            name: "frankfurt".to_string(),
            url: "https://frankfurt.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
        JitoNode {
            name: "london".to_string(),
            url: "https://london.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
        JitoNode {
            name: "virginia".to_string(),
            url: "https://virginia.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
        JitoNode {
            name: "oregon".to_string(),
            url: "https://oregon.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
        JitoNode {
            name: "singapore".to_string(),
            url: "https://singapore.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
        JitoNode {
            name: "tokyo".to_string(),
            url: "https://tokyo.jito.wtf/api".to_string(),
            latency: Arc::new(Mutex::new(None)),
            is_healthy: Arc::new(Mutex::new(true)),
            last_check: Arc::new(Mutex::new(Instant::now())),
        },
    ]
});

pub struct JitoNodeManager {
    nodes: Arc<Mutex<HashMap<String, JitoNode>>>,
    http_client: Client,
}

impl JitoNodeManager {
    pub fn new() -> Self {
        let mut nodes_map = HashMap::new();
        for node in JITO_NODES.iter() {
            nodes_map.insert(node.name.clone(), node.clone());
        }

        let client = Client::builder()
            .timeout(Duration::from_secs(5))
            .build()
            .unwrap_or_else(|_| Client::new());

        let manager = Self {
            nodes: Arc::new(Mutex::new(nodes_map)),
            http_client: client,
        };

        manager
    }

    pub fn get_available_regions(&self) -> Vec<String> {
        let nodes = self.nodes.lock().unwrap();
        nodes.keys().cloned().collect()
    }

    pub fn get_node_by_name(&self, name: &str) -> Option<JitoNode> {
        let nodes = self.nodes.lock().unwrap();
        nodes.get(name).cloned()
    }

    pub fn get_fastest_healthy_node(&self) -> Option<JitoNode> {
        let nodes = self.nodes.lock().unwrap();
        
        nodes.values()
            .filter(|node| {
                let is_healthy = *node.is_healthy.lock().unwrap();
                is_healthy
            })
            .min_by_key(|node| {
                let latency = *node.latency.lock().unwrap();
                latency.unwrap_or(u64::MAX)
            })
            .cloned()
    }

    pub fn get_preferred_node(&self, preferred_region: Option<&str>) -> JitoNode {
        if let Some(region) = preferred_region {
            if let Some(node) = self.get_node_by_name(region) {
                if *node.is_healthy.lock().unwrap() {
                    return node;
                } else {
                    warn!("首选区域 {} 节点不健康，将使用最快的健康节点", region);
                }
            } else {
                warn!("未找到首选区域 {} 的节点，将使用最快的健康节点", region);
            }
        }

        // 如果没有首选区域或首选节点不健康，返回最快的健康节点
        if let Some(fastest_node) = self.get_fastest_healthy_node() {
            return fastest_node;
        }

        // 如果所有节点都不健康，使用默认节点（第一个）
        warn!("所有节点都不健康，使用默认节点");
        let nodes = self.nodes.lock().unwrap();
        nodes.values().next().unwrap().clone()
    }

    pub async fn check_node_health(&self, node: &JitoNode) -> bool {
        let url = format!("{}/getHealth", node.url);

        match self.http_client.get(&url).send().await {
            Ok(response) => {
                let status = response.status();
                let is_healthy = status.is_success();
                
                let now = Instant::now();
                let request_time = now.duration_since(*node.last_check.lock().unwrap());
                let latency = request_time.as_millis() as u64;
                
                *node.latency.lock().unwrap() = Some(latency);
                *node.is_healthy.lock().unwrap() = is_healthy;
                *node.last_check.lock().unwrap() = now;
                
                if is_healthy {
                    debug!("节点 {} 健康检查通过，延迟: {}ms", node.name, latency);
                } else {
                    warn!("节点 {} 健康检查失败，状态码: {}", node.name, status);
                }
                
                is_healthy
            },
            Err(e) => {
                error!("节点 {} 健康检查请求失败: {}", node.name, e);
                *node.is_healthy.lock().unwrap() = false;
                false
            }
        }
    }

    pub async fn check_all_nodes_health(&self) {
        let nodes = {
            let nodes_map = self.nodes.lock().unwrap();
            nodes_map.values().cloned().collect::<Vec<_>>()
        };

        for node in nodes {
            let _ = self.check_node_health(&node).await;
        }
    }

    // 注释掉健康检查任务的实现
    /*
    pub fn start_health_check_task(self: Arc<Self>, runtime_handle: Handle) {
        let manager = self.clone();
        
        runtime_handle.spawn(async move {
            loop {
                debug!("开始所有节点的健康检查");
                manager.check_all_nodes_health().await;
                
                // 每30秒检查一次
                sleep(Duration::from_secs(30)).await;
            }
        });
        
        info!("已启动Jito节点健康检查任务");
    }
    */
} 