use anyhow::Result;
use copy_trading_bot::services::block_hash_service::BlockHashService;
use std::time::Duration;
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    println!("启动区块哈希服务示例...");
    
    // 创建区块哈希服务实例
    let grpc_endpoint = "solana-yellowstone-grpc.publicnode.com:443";
    let hash_service = BlockHashService::new(grpc_endpoint, Some(3));
    
    println!("开始后台监控区块哈希...");
    // 启动后台监控
    hash_service.start_background_monitoring().await?;
    
    // 等待一段时间，让服务收集一些哈希
    println!("等待10秒钟收集区块哈希...");
    sleep(Duration::from_secs(10)).await;
    
    // 定期查询并显示最新哈希
    for i in 1..=10 {
        // 获取最新哈希
        if let Some(latest) = hash_service.get_latest_hash().await {
            println!("【{}】最新区块哈希: {}", i, latest.hash);
            println!("槽位: {}", latest.slot);
            println!("父槽位: {}", latest.parent_slot);
            println!("时间戳: {}", latest.timestamp);
        } else {
            println!("尚未收到区块哈希数据");
        }
        
        // 获取所有缓存的哈希
        let all_hashes = hash_service.get_all_hashes().await;
        println!("当前缓存的哈希数量: {}", all_hashes.len());
        for (index, hash_info) in all_hashes.iter().enumerate() {
            println!("  [{index}] 哈希: {}", hash_info.hash);
        }
        
        println!("等待5秒...");
        println!("-----------------------------------");
        sleep(Duration::from_secs(5)).await;
    }
    
    println!("示例完成");
    Ok(())
} 