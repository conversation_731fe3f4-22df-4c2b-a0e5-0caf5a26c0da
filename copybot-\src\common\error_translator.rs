use std::collections::HashMap;
use lazy_static::lazy_static;

/// Pump.fun 错误代码翻译器
/// 用于将十六进制错误代码转换为中文说明
lazy_static! {
    static ref PUMP_FUN_ERROR_CODES: HashMap<&'static str, &'static str> = {
        let mut map = HashMap::new();
        map.insert("0xbc0", "交易滑点限制：买入时代表最小获得代币数量未满足，卖出时代表最小获得SOL数量未满足");
        map.insert("0xbc1", "流动性不足：减少交易金额，或等待流动性增加");
        map.insert("0xbc2", "代币价格超出可接受范围：调整滑点设置，或等待价格稳定");
        map.insert("0xbc3", "交易金额过小：增加交易金额");
        map.insert("0xbc4", "代币余额不足：检查钱包余额，降低跟单比例，或充值所需代币");
        map.insert("0xbc5", "代币授权不足：检查并增加代币授权额度");
        map.insert("0xbc6", "交易结构错误：检查交易指令参数和账户顺序");
        map.insert("0xbc7", "曲线变量计算错误：可能是内部协议错误");
        map.insert("0xbc8", "代币铸造限制：该代币可能有铸造限制，无法购买");
        map.insert("0xbc9", "交易超时：重试交易，可能是因为区块确认时间过长");
        map.insert("0xbca", "无效的交易方向：检查交易方向设置（买/卖）");
        map.insert("0xbcb", "代币池关闭或暂停：该代币可能暂时不可交易");
        map.insert("0xbcc", "重复交易：避免在短时间内提交相同交易");
        map.insert("0xbcd", "计算单元不足：增加交易的计算单元限制");
        map.insert("0xbce", "交易费用不足：增加交易的优先级费用");
        map.insert("0xbcf", "账户所有权错误：检查交易涉及的账户是否正确");
        map.insert("0xbd0", "内部程序错误：协议内部错误，等待开发者修复");
        
        // 添加新的错误码
        map.insert("0x1772", "滑点超出预设限制：代币价格变动过大，实际可兑换量低于设定的最小接受量");
        
        map
    };
}

/// 从错误消息中提取错误代码
pub fn extract_error_code(error_message: &str) -> Option<&str> {
    // 寻找常见的错误代码格式
    if let Some(pos) = error_message.find("custom program error: 0x") {
        let start = pos + "custom program error: ".len();
        let end = error_message[start..].find(|c: char| !c.is_alphanumeric())
            .map(|e| start + e)
            .unwrap_or(error_message.len());
        
        return Some(&error_message[start..end]);
    }
    None
}

/// 将错误代码转换为中文说明
pub fn translate_error_code(error_code: &str) -> String {
    PUMP_FUN_ERROR_CODES
        .get(error_code)
        .map(|desc| format!("错误码 {} 说明: {}", error_code, desc))
        .unwrap_or_else(|| format!("未知错误码: {}", error_code))
}

/// 从错误消息中提取并翻译错误代码
pub fn translate_error_message(error_message: &str) -> String {
    if let Some(error_code) = extract_error_code(error_message) {
        translate_error_code(error_code)
    } else {
        format!("未能识别的错误: {}", error_message)
    }
}

/// 分析交易失败原因并给出中文说明及建议
pub fn analyze_transaction_error(error_message: &str) -> String {
    if let Some(error_code) = extract_error_code(error_message) {
        // 针对常见错误提供额外建议
        match error_code {
            "0xbc0" => format!(
                "错误码 {} - 交易滑点限制\n买入时：最小获得代币数量未满足\n卖出时：最小获得SOL数量未满足\n建议: 1. 增加滑点容忍度至25-50%\n2. 对于卖出操作，考虑禁用滑点保护\n3. 确认代币价格是否波动剧烈", 
                error_code
            ),
            "0xbc4" => format!(
                "错误码 {} - 代币余额不足\n建议: 1. 检查钱包中是否有足够代币\n2. 在.env文件中降低FOLLOW_PERCENTAGE值\n3. 考虑添加自动余额检查功能", 
                error_code
            ),
            "0xbcd" | "0xbce" => format!(
                "错误码 {} - 交易资源不足\n建议: 1. 增加COMPUTE_UNIT_PRICE环境变量值\n2. 增加计算单元限制\n3. 确保钱包中有足够的SOL支付交易费用", 
                error_code
            ),
            "0x1772" => format!(
                "错误码 {} - 滑点超出预设限制\n建议: 1. 确保正确提取原始交易参数\n2. 增加滑点容忍度至30%以上\n3. 减少跟单金额以降低市场影响\n4. 检查金额单位转换是否正确，确保基准单位(base units)正确匹配", 
                error_code
            ),
            _ => translate_error_code(error_code),
        }
    } else if error_message.contains("Blockhash not found") {
        "区块哈希未找到错误\n建议: 1. 启用BlockMonitor服务\n2. 增加交易优先级费用\n3. 检查RPC连接是否稳定".to_string()
    } else if error_message.contains("insufficient funds") {
        "SOL余额不足\n建议: 1. 确保钱包中有足够的SOL\n2. 减少交易金额\n3. 考虑SOL余额预留".to_string()
    } else {
        format!("未识别的错误: {}\n建议查阅日志获取更多信息", error_message)
    }
} 