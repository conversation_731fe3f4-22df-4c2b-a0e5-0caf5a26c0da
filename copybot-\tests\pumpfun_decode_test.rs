// tests/pumpfun_decode_test.rs

#[cfg(test)]
mod tests {
    use base64; // 用于 Base64 解码
    use bs58;   // 用于 Base58 解码
    use serde_json::Value; // 用于解析 JSON
    use std::convert::TryInto;
    use std::fs;

    // Helper function to find the relevant log message
    fn find_program_data_log(log_messages: &Vec<Value>) -> Option<String> {
        let prefix = "Program data: vdt/";
        for log_val in log_messages {
            if let Some(log_str) = log_val.as_str() {
                if log_str.starts_with(prefix) {
                    return Some(log_str.to_string());
                }
            }
        }
        None
    }

    #[test]
    fn test_decode_pumpfun_sell_data_from_file() { // 重命名测试函数以反映其来源
        // 1. 读取 JSON 文件
        let file_path = "tx_5AcUePpNG5dp.json"; // 目标 JSON 文件
        let json_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => panic!("无法读取文件 '{}': {}", file_path, e),
        };

        // 2. 解析 JSON
        let parsed_json: Value = match serde_json::from_str(&json_content) {
            Ok(value) => value,
            Err(e) => panic!("无法解析 JSON 文件 '{}': {}", file_path, e),
        };

        // 3. 提取 logMessages
        let log_messages = parsed_json["meta"]["logMessages"]
            .as_array()
            .expect("无法从 JSON 中提取 meta.logMessages 数组");

        // 4. 查找包含 "Program data: vdt/" 的日志条目
        let log_data = match find_program_data_log(log_messages) {
            Some(log) => log,
            None => panic!("在 logMessages 中未找到 'Program data: vdt/' 前缀的日志"),
        };
        println!("从文件找到的日志数据: {}", log_data);

        // 5. 截取编码后的数据字符串 (与之前逻辑相同)
        let prefix = "Program data: vdt/";
        let encoded_data = if log_data.starts_with(prefix) {
            &log_data[prefix.len()..]
        } else {
            // 这个 panic 理论上不会触发，因为 find_program_data_log 已经保证了前缀
            panic!("逻辑错误：找到的日志数据没有预期的前缀 '{}'", prefix);
        };
        println!("提取到的编码数据: {}", encoded_data);

        // 6. 解码得到字节数组 (Vec<u8>)
        // 优先尝试 Base64 解码，这是 Solana program log 的常见格式
        let decoded_bytes = match base64::decode(encoded_data) {
            Ok(bytes) => {
                println!("Base64 解码成功，长度: {}", bytes.len());
                bytes
            },
            Err(e_b64) => {
                // 如果 Base64 失败，记录错误并 panic
                panic!("Base64 解码失败: {:?}", e_b64);
            }
        };

        println!("解码后的字节 (Hex): {}", hex::encode(&decoded_bytes));

        // 7. 直接从已知偏移量提取关键值 (根据之前的动态扫描结果)
        println!("\n从已知偏移量提取关键值:");
        let extract_u64 = |offset: usize| -> Option<u64> {
            const FIELD_LENGTH: usize = 8;
            if decoded_bytes.len() >= offset + FIELD_LENGTH {
                let bytes_slice = &decoded_bytes[offset..offset + FIELD_LENGTH];
                match bytes_slice.try_into() {
                    Ok(bytes_array) => Some(u64::from_le_bytes(bytes_array)),
                    Err(_) => None,
                }
            } else {
                None
            }
        };

        let actual_sol_received_offset = 37;
        let token_amount_offset = 45;
        let virtual_sol_offset = 94;
        let virtual_token_offset = 102;

        let actual_sol_received = extract_u64(actual_sol_received_offset)
            .expect(&format!("无法从偏移量 {} 提取 actual_sol_received", actual_sol_received_offset));
        let token_amount = extract_u64(token_amount_offset)
             .expect(&format!("无法从偏移量 {} 提取 token_amount", token_amount_offset));
        let virtual_sol = extract_u64(virtual_sol_offset)
             .expect(&format!("无法从偏移量 {} 提取 virtual_sol", virtual_sol_offset));
        let virtual_token = extract_u64(virtual_token_offset)
             .expect(&format!("无法从偏移量 {} 提取 virtual_token", virtual_token_offset));

        println!("  - Actual SOL Received (Offset {}): {}", actual_sol_received_offset, actual_sol_received);
        println!("  - Token Amount (Offset {}):        {}", token_amount_offset, token_amount);
        println!("  - Virtual SOL Reserves (Offset {}):  {}", virtual_sol_offset, virtual_sol);
        println!("  - Virtual Token Reserves (Offset {}):{}", virtual_token_offset, virtual_token);

        // 8. 断言从日志提取的 actual_sol_received 是否等于已知值
        let known_actual_sol = 97_840_182u64;
        println!("\n断言从日志提取的 SOL 是否等于已知实际值:");
        assert_eq!(actual_sol_received, known_actual_sol, "从日志偏移量 {} 提取的 SOL 与已知的实际接收量不符", actual_sol_received_offset);
        println!("  ✅ 断言成功: 日志提取值 {} == 已知实际值 {}", actual_sol_received, known_actual_sol);


        // 9. 使用恒定乘积公式进行理论计算 (用于对比和理解差异)
        println!("\n使用恒定乘积公式进行理论计算 (对比):");
        if virtual_token != 0 {
            let vsol_u128 = virtual_sol as u128;
            let vtoken_u128 = virtual_token as u128;
            let dtoken_u128 = token_amount as u128;

            let k: u128 = vsol_u128.checked_mul(vtoken_u128).expect("计算 K 时溢出");
            let new_vtoken = vtoken_u128.checked_add(dtoken_u128).expect("计算 new_vtoken 时溢出");
            let new_vsol = k / new_vtoken;
            let theoretical_sol_out_lamports = vsol_u128.checked_sub(new_vsol).expect("计算 theoretical_sol_out 时得到负值？");
            let theoretical_sol_out = theoretical_sol_out_lamports as f64 / 1_000_000_000.0;

            println!("  - 理论计算结果 (lamports): {}", theoretical_sol_out_lamports);
            println!("  - 理论计算结果 (SOL):           {:.9}", theoretical_sol_out);

            let difference_lamports = actual_sol_received.abs_diff(theoretical_sol_out_lamports as u64);
            println!("  - 实际接收与理论计算差异 (lamports): {} (可能代表费用/滑点等)", difference_lamports);

        } else {
            println!("  ❌ 无法计算理论值：虚拟代币储备为零");
        }
    }

    #[test]
    fn test_decode_pumpfun_buy_mint_address_from_file() {
        // 1. 读取购买交易的 JSON 文件
        let file_path = "tx_52oNX3sMwTDy.json"; // 目标 JSON 文件 (Buy transaction)
        let json_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => panic!("无法读取文件 '{}': {}", file_path, e),
        };

        // 2. 解析 JSON
        let parsed_json: Value = match serde_json::from_str(&json_content) {
            Ok(value) => value,
            Err(e) => panic!("无法解析 JSON 文件 '{}': {}", file_path, e),
        };

        // 3. 提取 logMessages
        let log_messages = parsed_json["meta"]["logMessages"]
            .as_array()
            .expect("无法从 JSON 中提取 meta.logMessages 数组");

        // 4. 查找包含 "Program data: vdt/" 的日志条目
        let log_data = match find_program_data_log(log_messages) {
            Some(log) => log,
            None => panic!("在 logMessages 中未找到 'Program data: vdt/' 前缀的日志"),
        };
        println!("从文件找到的日志数据: {}", log_data);

        // 5. 截取 Base64 编码的数据字符串
        let prefix = "Program data: vdt/";
        let encoded_data = if log_data.starts_with(prefix) {
            &log_data[prefix.len()..]
        } else {
            panic!("逻辑错误：找到的日志数据没有预期的前缀 '{}'", prefix);
        };
        println!("提取到的编码数据: {}", encoded_data);

        // 6. Base64 解码得到字节数组 (Vec<u8>)
        let decoded_bytes = match base64::decode(encoded_data) {
            Ok(bytes) => {
                println!("Base64 解码成功，长度: {}", bytes.len());
                bytes
            },
            Err(e_b64) => {
                panic!("Base64 解码失败: {:?}", e_b64);
            }
        };
        println!("解码后的字节 (Hex): {}", hex::encode(&decoded_bytes));

        // --- 修改开始：动态扫描查找 Mint 地址字节 ---
        println!("\n动态扫描解码后的字节以查找 Mint 地址:");

        // 7. 定义目标 Mint 地址并解码为字节
        let expected_mint_address_str = "9jQ7SufnkE7MSjXHgg4ku719QjxYVJeZH6AGcJBKpump";
        let expected_mint_bytes = bs58::decode(expected_mint_address_str)
            .into_vec()
            .expect("无法将预期的 Mint 地址字符串解码为字节");
        println!("  - 目标 Mint 地址 (Bs58): {}", expected_mint_address_str);
        println!("  - 目标 Mint 地址字节 (Hex): {}", hex::encode(&expected_mint_bytes));

        // 8. 动态扫描 decoded_bytes
        let mut found_offset: Option<usize> = None;
        let mint_len = expected_mint_bytes.len(); // 应该是 32

        if decoded_bytes.len() >= mint_len {
            for i in 0..=(decoded_bytes.len() - mint_len) {
                let window = &decoded_bytes[i..i + mint_len];
                if window == expected_mint_bytes.as_slice() {
                    found_offset = Some(i);
                    break; // 找到即退出
                }
            }
        }

        // 9. 断言并打印结果
        match found_offset {
            Some(offset) => {
                println!("  ✅ 找到匹配! Mint 地址字节序列在偏移量 {} 处找到。", offset);
                // 可以选择性地提取并转换回 bs58 进行二次确认
                let extracted_bytes = &decoded_bytes[offset..offset + mint_len];
                let extracted_bs58 = bs58::encode(extracted_bytes).into_string();
                assert_eq!(extracted_bs58, expected_mint_address_str, "重新编码的地址与预期不符");
                println!("  - 从偏移量 {} 提取并重新编码为 Bs58: {}", offset, extracted_bs58);
            }
            None => {
                panic!("❌ 未能在解码后的字节中找到目标 Mint 地址的字节序列: {}", expected_mint_address_str);
            }
        }
        // --- 修改结束 ---
    }

    // ------------ 新增可靠的测试函数 ------------

    #[test]
    fn test_extract_buy_mint_from_instruction() {
        // 测试目标: 从 Buy 交易的指令和账户列表可靠地提取 Mint 地址
        let file_path = "tx_AW1UetPyA7M8.json"; // <-- 修改文件名
        let expected_mint_address = "GWjphFncBRwEbDwhZr26SB7CTz6b8xxuV7tVQ2wcppcX"; // <-- 修改期望地址
        let pump_fun_program_id_index = 7; // Pump.fun program ID 在 accountKeys 中的索引
        let buy_instruction_index = 3;     // Buy 指令在 instructions 数组中的索引
        let mint_account_index_in_instruction = 2; // Mint 账户在 Buy 指令 accounts 列表中的索引

        println!("\n测试: 从 Buy 交易指令提取 Mint 地址 ({})", file_path);

        // 1. 读取 JSON 文件
        let json_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => panic!("无法读取文件 '{}': {}", file_path, e),
        };

        // 2. 解析 JSON
        let parsed_json: Value = match serde_json::from_str(&json_content) {
            Ok(value) => value,
            Err(e) => panic!("无法解析 JSON 文件 '{}': {}", file_path, e),
        };

        // 3. 提取 transaction message 相关字段
        let message = &parsed_json["transaction"]["message"];
        let account_keys = message["accountKeys"]
            .as_array()
            .expect("无法提取 accountKeys 数组");
        let instructions = message["instructions"]
            .as_array()
            .expect("无法提取 instructions 数组");

        // 4. 定位 Buy 指令并提取 Mint 账户在 accountKeys 中的索引
        let mut mint_account_index_in_keys: Option<usize> = None;
        if instructions.len() > buy_instruction_index {
            let instruction = &instructions[buy_instruction_index];
            if instruction["programIdIndex"].as_u64() == Some(pump_fun_program_id_index as u64) {
                 println!("  - 找到索引 {} 的指令，Program ID 索引为 {} (符合 Pump.fun)", buy_instruction_index, pump_fun_program_id_index);
                let instruction_accounts = instruction["accounts"]
                    .as_array()
                    .expect("无法提取指令中的 accounts 数组");
                if instruction_accounts.len() > mint_account_index_in_instruction {
                    mint_account_index_in_keys = instruction_accounts[mint_account_index_in_instruction]
                        .as_u64()
                        .map(|i| i as usize);
                    println!("    - 指令 accounts 列表: {:?}", instruction_accounts);
                    println!("    - Mint 账户在指令 accounts 中的索引: {}", mint_account_index_in_instruction);
                    println!("    - 推断出 Mint 账户在全局 accountKeys 中的索引: {:?}", mint_account_index_in_keys);
                } else {
                     panic!("指令 accounts 列表长度不足 {}", mint_account_index_in_instruction + 1);
                 }
            } else {
                 panic!("索引 {} 的指令 Program ID 索引 ({:?}) 与预期的 {} 不符",
                     buy_instruction_index, instruction["programIdIndex"], pump_fun_program_id_index);
             }
        } else {
             panic!("instructions 数组长度不足 {}", buy_instruction_index + 1);
         }

        let mint_account_index = mint_account_index_in_keys
            .expect("未能从指令中确定 Mint 账户在 accountKeys 中的索引");

        // 5. 从 accountKeys 中提取 Mint 地址字符串
        let extracted_mint_address = account_keys[mint_account_index]
            .as_str()
            .expect("无法从 accountKeys 中提取 Mint 地址字符串");
        println!("  - 从 accountKeys[{}] 提取到的 Mint 地址: {}", mint_account_index, extracted_mint_address);

        // 6. 断言
        assert_eq!(extracted_mint_address, expected_mint_address, "提取的 Mint 地址与预期不符");
        println!("  ✅ 断言成功: 提取值 {} == 预期值 {}", extracted_mint_address, expected_mint_address);
    }

    #[test]
    fn test_extract_sell_mint_from_balances() {
        // 测试目标: 从 Sell 交易的 preTokenBalances 可靠地提取 Mint 地址
        let file_path = "tx_fzi2ScoYawCt.json"; // <-- 修改文件名
        let expected_mint_address = "JeS9haKpoPqPcjsN8H5PPVjmzaY5zuRHnZEkEkJGW7NvSGuXfZMVXekW1NVu"; // <-- 修改期望地址

        println!("\n测试: 从 Sell 交易 preTokenBalances 提取 Mint 地址 ({})", file_path);

        // 1. 读取 JSON 文件
        let json_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => panic!("无法读取文件 '{}': {}", file_path, e),
        };

        // 2. 解析 JSON
        let parsed_json: Value = match serde_json::from_str(&json_content) {
            Ok(value) => value,
            Err(e) => panic!("无法解析 JSON 文件 '{}': {}", file_path, e),
        };

        // 3. 提取 meta.preTokenBalances
        let pre_token_balances = parsed_json["meta"]["preTokenBalances"]
            .as_array()
            .expect("无法提取 meta.preTokenBalances 数组");

        // 4. 从第一个余额条目中提取 mint 地址
        let mut extracted_mint_address: Option<&str> = None;
        if !pre_token_balances.is_empty() {
            if let Some(mint_val) = pre_token_balances[0].get("mint") {
                 extracted_mint_address = mint_val.as_str();
                 println!("  - 从 preTokenBalances[0][\"mint\"] 提取到的地址: {:?}", extracted_mint_address);
             } else {
                 panic!("preTokenBalances[0] 中缺少 \"mint\" 字段");
             }
        } else {
            panic!("preTokenBalances 数组为空");
        }

        let extracted_mint_address = extracted_mint_address
            .expect("未能从 preTokenBalances[0] 中提取有效的 mint 地址字符串");

        // 5. 断言
        assert_eq!(extracted_mint_address, expected_mint_address, "提取的 Mint 地址与预期不符");
        println!("  ✅ 断言成功: 提取值 {} == 预期值 {}", extracted_mint_address, expected_mint_address);
    }

    #[test]
    fn test_extract_sell_instruction_accounts() {
        // 测试目标: 从 Sell 交易的指令中提取构建交易所需的所有账户地址
        let file_path = "tx_iheSV7RsT3kD.json"; // <-- 修改文件名

        // 预期的账户地址 (根据 tx_iheSV7RsT3kD.json 核对)
        let expected_accounts = std::collections::HashMap::from([
            ("global", "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf"),
            ("fee_recipient", "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV"),
            ("mint_internal", "9jJE2ELYEJs3AhGeYAFCTkR8g4a9dQY72bHewkJwpump"), // <--- 更新
            ("bonding_curve", "BvXVtyQ3PegeqhhD5TctXbLrx6fpcRvD57RBaigBqRdR"), // <--- 更新
            ("bonding_curve_vault", "4kKnELHdZ3oXW6pXCqU4dxP4H2VUixtJY1kbFncJB9bF"), // <--- 更新
            ("user_ata", "CxErySPgz4VjgViP3NYKEF611fMJeMTgtTFpdzUQgiPF"), // <--- 更新
            ("user_wallet", "********************************************"),
            ("system_program", "********************************"),
            ("associated_token_program", "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"),
            ("token_program", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"),
            ("event_authority", "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1"),
            ("pump_fun_program", "BXxgGt3akAghZviYHLh8KUh6vhXBht5wf86De6huTp95"),
        ]);

        let pump_fun_program_id_index = 7;
        let sell_instruction_index = 0;

        println!("\n测试: 从 Sell 交易指令提取所有账户地址 ({})", file_path);

        // 1. 读取 JSON 文件
        let json_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => panic!("无法读取文件 '{}': {}", file_path, e),
        };

        // 2. 解析 JSON
        let parsed_json: Value = match serde_json::from_str(&json_content) {
            Ok(value) => value,
            Err(e) => panic!("无法解析 JSON 文件 '{}': {}", file_path, e),
        };

        // 3. 提取 transaction message 相关字段
        let message = &parsed_json["transaction"]["message"];
        let account_keys = message["accountKeys"]
            .as_array()
            .expect("无法提取 accountKeys 数组");
        let instructions = message["instructions"]
            .as_array()
            .expect("无法提取 instructions 数组");

        // 4. 定位 Sell 指令
        let sell_instruction = if instructions.len() > sell_instruction_index &&
                                  instructions[sell_instruction_index]["programIdIndex"].as_u64() == Some(pump_fun_program_id_index as u64)
        {
            println!("  - 找到索引 {} 的 Sell 指令", sell_instruction_index);
            &instructions[sell_instruction_index]
        } else {
            panic!("未能在索引 {} 找到预期的 Pump.fun Sell 指令", sell_instruction_index);
        };

        // 5. 提取指令的账户索引列表
        let instruction_accounts_indices = sell_instruction["accounts"]
            .as_array()
            .expect("无法提取 Sell 指令中的 accounts 数组")
            .iter()
            .map(|v| v.as_u64().expect("账户索引不是有效的 u64") as usize)
            .collect::<Vec<usize>>();

        // <-- 确认这里的账户索引列表与 tx_iheSV7RsT3kD.json 一致
        println!("  - Sell 指令账户索引列表: {:?}", instruction_accounts_indices);
        assert_eq!(instruction_accounts_indices, vec![9, 2, 10, 3, 1, 4, 0, 6, 11, 8, 12, 7], "指令账户索引列表与预期不符"); // <--- 更新索引列表断言
        assert_eq!(instruction_accounts_indices.len(), 12, "Sell 指令账户数量应为 12");

        // 6. 根据索引列表从 accountKeys 提取地址并验证
        let account_roles = [
            "global", "fee_recipient", "mint_internal", "bonding_curve", "bonding_curve_vault",
            "user_ata", "user_wallet", "system_program", "associated_token_program", "token_program",
            "event_authority", "pump_fun_program"
        ];

        for (i, role) in account_roles.iter().enumerate() {
            let account_index = instruction_accounts_indices[i];
            let extracted_address = account_keys[account_index]
                .as_str()
                .expect("无法从 accountKeys 提取地址字符串");
            let expected_address = expected_accounts.get(role)
                .expect(&format!("预期地址映射中缺少角色: {}", role));

            println!("    - 账户角色: {:<25} | 指令索引: {:<2} | 全局索引: {:<2} | 提取地址: {}",
                     role, i, account_index, extracted_address);
            assert_eq!(extracted_address, *expected_address, "地址不匹配: 角色 '{}'", role);
        }
        println!("  ✅ 所有 Sell 指令账户地址提取和验证成功!");
    }
} 