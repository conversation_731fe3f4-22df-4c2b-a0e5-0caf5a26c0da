use anyhow::{Context, Result};
use rust_decimal::Decimal;
use std::str::FromStr;
use std::sync::Arc;
use futures_util::StreamExt;

// 基本交易信息结构体
#[derive(Debug, Clone)]
pub struct BasicTxInfo {
    pub transaction_type: String, // "Sell" or "Buy"
    pub mint_address: String,
    pub token_amount_raw: u64,
    pub sol_involved_str: String, // e.g., "0.048356935 SOL" or "0.1 SOL" or "0 SOL"
    pub sol_involved_decimal: Decimal, // Parsed decimal value of SOL
    pub transaction_time_str: String, // The full "TIME" string like "2025-05-07T17:18:22.788+08:00"
    pub signature: String, // The transaction signature itself
    pub signer_address: String, // 签名者地址
    pub price: Option<Decimal>, // "当前价格" from "价格信息:" section
}

// 配置结构体
#[derive(Clone)]
pub struct MonitorConfig {
    pub monitored_addresses: Arc<Vec<String>>,
}

// 从交易字符串中解析出基本交易信息
pub fn parse_transaction_for_direct_follow_up(data_str: &str) -> Result<Option<BasicTxInfo>> {
    let mut transaction_type = None;
    let mut mint_address = None;
    let mut token_amount_raw = None;
    let mut sol_involved_str = None;
    let mut sol_involved_decimal = None;
    let mut transaction_time_str = None;
    let mut signature = None;
    let mut signer_address_val = None; // Renamed to avoid conflict
    let mut price = None;

    // 如果数据为空或太短，直接返回None
    if data_str.len() < 10 {
        println!("警告：交易数据过短或为空，无法解析");
        return Ok(None);
    }

    let lines: Vec<&str> = data_str.lines().collect();

    for line in lines.iter() {
        let trimmed_line = line.trim();
        if trimmed_line.starts_with("TYPE:") {
            transaction_type = trimmed_line.split("TYPE:").nth(1).map(|s| s.trim().to_string());
        } else if trimmed_line.starts_with("MINT:") { // This is the primary MINT for the transaction
            if mint_address.is_none() { // Take the first "MINT:" as the transaction's MINT
                 mint_address = trimmed_line.split("MINT:").nth(1).map(|s| s.trim().to_string());
            }
        } else if trimmed_line.starts_with("TOKEN AMOUNT:") {
            token_amount_raw = trimmed_line.split("TOKEN AMOUNT:").nth(1)
                .and_then(|s| s.trim().parse::<u64>().ok());
        } else if trimmed_line.starts_with("MIN SOL OUTPUT:") { // For Sell
            sol_involved_str = trimmed_line.split("MIN SOL OUTPUT:").nth(1)
                .map(|s| s.trim().to_string());
            if let Some(ref s_str) = sol_involved_str {
                 sol_involved_decimal = s_str.split_whitespace().next()
                    .and_then(|val_str| Decimal::from_str(val_str).ok());
            }
        } else if trimmed_line.starts_with("SOL COST:") { // For Buy
             sol_involved_str = trimmed_line.split("SOL COST:").nth(1)
                .map(|s| s.trim().to_string());
            if let Some(ref s_str) = sol_involved_str {
                 sol_involved_decimal = s_str.split_whitespace().next()
                    .and_then(|val_str| Decimal::from_str(val_str).ok());
            }
        } else if trimmed_line.starts_with("TIME:") { // Transaction time
            if transaction_time_str.is_none() { // Usually the first TIME: is the transaction time
                transaction_time_str = trimmed_line.split("TIME:").nth(1).map(|s| s.trim().to_string());
            }
        } else if trimmed_line.starts_with("SIGNATURE:") {
            signature = trimmed_line.split("SIGNATURE:").nth(1).map(|s| s.trim().to_string());
        } else if trimmed_line.starts_with("签名者地址:") {
            signer_address_val = trimmed_line.split("签名者地址:").nth(1).map(|s| s.trim().to_string());
        } else if trimmed_line.starts_with("当前价格:") { // Price from "价格信息:"
            price = trimmed_line.split("当前价格:").nth(1)
                .and_then(|s| s.trim().split_whitespace().next()) // Get "0.000..." part
                .and_then(|p_str| Decimal::from_str(p_str).ok());
        }
    }
    
    // 额外验证签名者地址的格式 - Solana地址应该是base58编码的
    if let Some(ref signer) = signer_address_val {
        let is_valid_address = signer.chars().all(|c| 
            (c >= 'A' && c <= 'Z') || 
            (c >= 'a' && c <= 'z') || 
            (c >= '1' && c <= '9') ||
            c == '0'
        );
        
        if !is_valid_address {
            println!("警告：签名者地址 {} 格式不符合Solana地址规范", signer);
            signer_address_val = None; // 将无效地址设为None，使解析结果不完整
        }
    }
    
    // Check if all *essential* fields for follow-up are present, especially price.
    if let (
        Some(tt),
        Some(ma),
        Some(tar),
        Some(sis),
        Some(sid), // Check if sol_involved_decimal was successfully parsed
        Some(tts),
        Some(sig),
        Some(sa),
        Some(p) // Price is now mandatory for "complete" data
    ) = (
        transaction_type.clone(),
        mint_address.clone(),
        token_amount_raw,
        sol_involved_str.clone(),
        sol_involved_decimal,
        transaction_time_str.clone(),
        signature.clone(),
        signer_address_val.clone(),
        price, // Price must be Some(_)
    ) {
        Ok(Some(BasicTxInfo {
            transaction_type: tt,
            mint_address: ma,
            token_amount_raw: tar,
            sol_involved_str: sis,
            sol_involved_decimal: sid,
            transaction_time_str: tts,
            signature: sig,
            signer_address: sa,
            price: Some(p), // Price is confirmed to be Some here
        }))
    } else {
        // If any of the above (especially price) is None, consider it "incomplete" for direct follow-up
        println!(
            "解析信息：数据不完整。Type: {:?}, Mint: {:?}, Amount: {:?}, SolStr: {:?}, SolDec: {:?}, Time: {:?}, Sig: {:?}, Signer: {:?}, Price: {:?}",
            transaction_type, mint_address, token_amount_raw, sol_involved_str, sol_involved_decimal, transaction_time_str, signature, signer_address_val, price
        );
        Ok(None) // Not enough data to follow up, or price specifically is missing
    }
}

// 执行跟单交易
pub async fn execute_actual_follow_up_trade(
    tx_info: BasicTxInfo,
    signer_address_from_outer_scope: &str,
) -> Result<()> {
    let price = tx_info.price.expect("Price should be Some if parse_transaction_for_direct_follow_up returned Some");

    println!(
        "执行跟单: 监控钱包: {}, 交易内签名者: {}, MINT: {}, 价格: {}, 类型: {}, 代币数量: {}",
        signer_address_from_outer_scope,
        tx_info.signer_address,
        tx_info.mint_address,
        price,
        tx_info.transaction_type,
        tx_info.token_amount_raw
    );
    
    // 这里放置您实际的跟单交易逻辑
    // 例如根据 tx_info.transaction_type 决定买入或卖出相应的代币
    if tx_info.transaction_type == "Buy" {
        println!("模拟买入操作: 买入 {} 个 {} 代币，价格为 {}", tx_info.token_amount_raw, tx_info.mint_address, price);
        // 实际买入操作：wallet.buy(tx_info.mint_address, tx_info.token_amount_raw, price)
    } else if tx_info.transaction_type == "Sell" {
        println!("模拟卖出操作: 卖出 {} 个 {} 代币，价格为 {}", tx_info.token_amount_raw, tx_info.mint_address, price);
        // 实际卖出操作：wallet.sell(tx_info.mint_address, tx_info.token_amount_raw, price)
    }

    Ok(())
}

// 主要的跟单处理函数
pub async fn handle_direct_follow_up<C>(
    transaction_signature: &str,
    signer_address_trigger: &str,
    redis_conn: &mut C,
    config: &MonitorConfig,
) -> Result<()>
where
    C: redis::AsyncCommands + Send,
{
    // 0. 验证交易签名格式
    let is_valid_signature_format = transaction_signature.chars().all(|c| 
        (c >= 'A' && c <= 'Z') || 
        (c >= 'a' && c <= 'z') || 
        (c >= '1' && c <= '9') ||
        c == '0'
    );
    
    if !is_valid_signature_format {
        println!("警告：交易签名 {} 格式不符合Solana交易签名规范，忽略处理", transaction_signature);
        return Ok(());
    }

    // 1. 是否为被监控的钱包?
    if !config.monitored_addresses.iter().any(|addr| addr == signer_address_trigger) {
        return Ok(());
    }
    println!("信息：监控的签名者 {} 触发。检查交易: {}", signer_address_trigger, transaction_signature);

    // 2. 此钱包是否已（因其他原因被）暂停跟单?
    let pause_key = format!("pause_wallet:{}", signer_address_trigger);
    let is_paused: bool = redis_conn.exists(&pause_key).await
        .context(format!("检查 Redis 暂停键 {} 失败", pause_key))?;
    if is_paused {
        println!("信息：钱包 {} 当前已暂停跟单。", signer_address_trigger);
        return Ok(());
    }

    // 3. 从 Redis 获取交易数据字符串
    let transaction_data_str: String = match redis_conn.get(transaction_signature).await {
        Ok(Some(data)) => data,
        Ok(None) => {
            println!("信息：交易 {} 的数据在 Redis 中未找到。放弃跟单。", transaction_signature);
            return Ok(());
        }
        Err(e) => {
            return Err(anyhow::anyhow!("从 Redis 获取交易 {} 失败: {}", transaction_signature, e));
        }
    };

    // 4. 解析交易数据，判断是否"完整"到可以跟单
    match parse_transaction_for_direct_follow_up(&transaction_data_str) {
        Ok(Some(tx_info_for_follow_up)) => {
            // 可选：交叉检查 tx_info_for_follow_up.signer_address 是否与 signer_address_trigger 匹配
            if tx_info_for_follow_up.signer_address != signer_address_trigger {
                println!(
                    "警告：交易 {} 数据内的签名者 ({}) 与触发监控的签名者 ({}) 不匹配。谨慎处理。",
                    transaction_signature, tx_info_for_follow_up.signer_address, signer_address_trigger
                );
                // 如果您不希望在此情况下进行跟单，可以在此处返回
                // return Ok(());
            }

            println!(
                "信息：交易 {} 数据完整，准备跟单。MINT: {}, 价格: {:?}",
                transaction_signature, tx_info_for_follow_up.mint_address, tx_info_for_follow_up.price.unwrap()
            );
            
            match execute_actual_follow_up_trade(tx_info_for_follow_up.clone(), signer_address_trigger).await {
                Ok(_) => println!("信息：交易 {} (MINT: {}) 的跟单操作已成功处理。", transaction_signature, tx_info_for_follow_up.mint_address),
                Err(e) => {
                     println!(
                        "错误：执行交易 {} (MINT: {}) 的跟单操作时出错: {:?}。本次放弃。",
                        transaction_signature, tx_info_for_follow_up.mint_address, e
                    );
                }
            }
        }
        Ok(None) => {
            println!(
                "信息：交易 {} 的数据不完整（例如缺少价格）。放弃跟单。",
                transaction_signature
            );
        }
        Err(e) => {
            println!(
                "错误：解析交易 {} 的数据时发生严重错误: {:?}。放弃跟单。",
                transaction_signature, e
            );
        }
    }

    Ok(())
}

// Redis轮询器，实现真正的低延迟实时监控，完全依赖键空间通知
pub async fn start_redis_monitor_loop(
    redis_client: redis::Client,
    config: MonitorConfig,
) -> Result<()> {
    println!("开始实时监控 Redis 中的交易数据...");
    
    // 验证配置中的地址格式是否符合Solana地址规范
    for address in config.monitored_addresses.iter() {
        let is_valid_address = address.chars().all(|c| 
            (c >= 'A' && c <= 'Z') || 
            (c >= 'a' && c <= 'z') || 
            (c >= '1' && c <= '9') ||
            c == '0'
        );
        
        if !is_valid_address {
            return Err(anyhow::anyhow!("无效的监控地址格式: {}", address));
        }
    }
    
    println!("监控的钱包地址: {:?}", config.monitored_addresses);
    
    // 创建多个连接以提高并发处理能力
    let mut main_conn = redis_client.get_async_connection().await?;
    
    // 记录已处理的交易，避免重复处理
    let processed_txs = Arc::new(tokio::sync::Mutex::new(std::collections::HashSet::new()));
    
    // 执行初始扫描 - 确保不错过任何交易，只获取现有键名
    println!("执行快速初始扫描...");
    let existing_keys: Vec<String> = redis::cmd("KEYS")
        .arg("*")
        .query_async(&mut main_conn)
        .await
        .context("获取现有 Redis 键失败")?;
    
    // 过滤出可能的交易键，不做时间筛选
    let tx_keys: Vec<String> = existing_keys.into_iter()
        .filter(|k| k.len() > 20 && !k.contains(":"))
        .collect();
    
    println!("找到 {} 个可能的交易键，初始化处理...", tx_keys.len());
    
    // 创建一个管道传输新的键
    let (tx, rx) = tokio::sync::mpsc::channel::<String>(10000);
    
    // 设置键空间通知系统 - 这是唯一的数据来源，确保实时性
    let keyspace_pattern = "__keyspace@0__:*"; // 监听默认数据库中所有键的事件
    let tx_clone = tx.clone();
    let client_clone = redis_client.clone();
    
    tokio::spawn(async move {
        // 确保键空间通知系统始终开启
        let mut retry_interval = tokio::time::interval(tokio::time::Duration::from_millis(100));
        
        loop {
            retry_interval.tick().await;
            
            println!("正在设置键空间通知监听...");
            
            // 强制开启键空间通知
            let mut config_conn = match client_clone.get_async_connection().await {
                Ok(conn) => conn,
                Err(e) => {
                    println!("无法获取Redis配置连接: {:?}，100ms后重试", e);
                    continue;
                }
            };
            
            // 确保Redis配置了键空间通知
            if let Err(e) = redis::cmd("CONFIG")
                .arg("SET")
                .arg("notify-keyspace-events")
                .arg("KEA") // K=键空间事件，E=过期事件，A=所有命令
                .query_async::<_, ()>(&mut config_conn)
                .await 
            {
                println!("配置Redis键空间通知失败: {:?}，100ms后重试", e);
                continue;
            }
            
            // 创建PubSub连接
            let pubsub_conn = match client_clone.get_async_connection().await {
                Ok(conn) => conn,
                Err(e) => {
                    println!("无法获取Redis PubSub连接: {:?}，100ms后重试", e);
                    continue;
                }
            };
            
            // 正确处理PubSub创建 - 直接获取PubSub对象，无需match
            let mut pubsub = pubsub_conn.into_pubsub();
            
            if let Err(e) = pubsub.psubscribe(keyspace_pattern).await {
                println!("订阅键空间通知失败: {:?}，100ms后重试", e);
                continue;
            }
            
            println!("===========================================");
            println!("★★★ 实时通知系统已激活 - 超低延迟模式 ★★★");
            println!("===========================================");
            
            let mut pubsub_stream = pubsub.on_message();
            
            // 已成功建立监听，开始接收通知
            while let Some(msg) = pubsub_stream.next().await {
                let channel: String = match msg.get_channel() {
                    Ok(channel) => channel,
                    Err(e) => {
                        println!("获取通知频道失败: {:?}", e);
                        continue;
                    }
                };
                
                // 提取键名
                if let Some(key) = channel.strip_prefix("__keyspace@0__:") {
                    // 只关注 "set" 操作，表示新建或更新键
                    let operation: String = match msg.get_payload() {
                        Ok(op) => op,
                        Err(e) => {
                            println!("获取操作类型失败: {:?}", e);
                            continue;
                        }
                    };
                    
                    if operation == "set" && key.len() > 20 && !key.contains(":") {
                        // 添加更严格的过滤条件，判断是否为有效的交易签名
                        // Solana交易签名为base58编码的字符串，通常满足一定格式
                        // 检查键是否只包含base58字符集中的字符（字母数字，不包含特殊字符）
                        let is_valid_signature = key.chars().all(|c| 
                            (c >= 'A' && c <= 'Z') || 
                            (c >= 'a' && c <= 'z') || 
                            (c >= '1' && c <= '9') ||
                            c == '0'
                        );
                        
                        if is_valid_signature {
                            // 这是一个新的交易签名键，立即高优先级处理
                            println!("★ 实时捕获: {} 键被设置，立即处理", key);
                            
                            // 立即发送到处理队列
                            if let Err(e) = tx_clone.send(key.to_string()).await {
                                println!("发送键 {} 到处理队列失败: {:?}", key, e);
                            }
                        } else {
                            println!("忽略不符合交易签名格式的键: {}", key);
                        }
                    }
                }
            }
            
            // 如果流已关闭，输出错误并继续下一个循环
            println!("⚠ 键空间通知流已中断，立即重新连接...");
        }
    });
    
    // 将初始扫描的键发送到处理队列
    for key in tx_keys {
        let _ = tx.send(key).await;
    }
    
    // 创建一个处理通道，增加并发处理能力
    let (process_tx, process_rx) = tokio::sync::mpsc::channel::<(String, String)>(10000);
    
    // 启动多个工作线程处理交易
    let worker_count = 16; // 使用16个工作线程并行处理交易
    let processed_txs_clone = Arc::clone(&processed_txs);
    
    // 使用多个任务共享同一个接收器
    let process_rx = Arc::new(tokio::sync::Mutex::new(process_rx));
    
    for worker_id in 0..worker_count {
        let client_clone = redis_client.clone();
        let config_clone = config.clone();
        // 共享接收器
        let process_rx = Arc::clone(&process_rx);
        let processed_txs_clone = Arc::clone(&processed_txs_clone);
        
        tokio::spawn(async move {
            println!("工作线程 #{} 已启动，等待处理交易...", worker_id);
            
            loop {
                // 获取一个消息
                let message = {
                    let mut rx_lock = process_rx.lock().await;
                    rx_lock.recv().await
                };
                
                // 处理消息
                if let Some((key, address)) = message {
                    let worker_client = client_clone.clone();
                    let worker_config = config_clone.clone();
                    let processed_txs = Arc::clone(&processed_txs_clone);
                    
                    // 在独立任务中处理，确保不阻塞工作线程
                    tokio::spawn(async move {
                        // 检查是否已处理过此交易
                        {
                            let mut processed = processed_txs.lock().await;
                            if processed.contains(&key) {
                                return; // 已处理过，跳过
                            }
                            processed.insert(key.clone());
                            
                            // 如果已处理交易数过多，清理较旧的记录
                            if processed.len() > 50000 {
                                // 保留最近的20000条记录
                                let to_remove: Vec<String> = processed.iter()
                                    .take(processed.len() - 20000)
                                    .cloned()
                                    .collect();
                                
                                for old_key in to_remove {
                                    processed.remove(&old_key);
                                }
                            }
                        }
                        
                        // 创建独立的Redis连接
                        let mut conn = match worker_client.get_async_connection().await {
                            Ok(conn) => conn,
                            Err(e) => {
                                println!("工作线程获取Redis连接失败: {:?}", e);
                                return;
                            }
                        };
                        
                        let start_time = std::time::Instant::now();
                        
                        // 处理交易
                        if let Err(e) = handle_direct_follow_up(&key, &address, &mut conn, &worker_config).await {
                            println!("处理交易 {} 失败: {:?}", key, e);
                        } else {
                            let elapsed = start_time.elapsed();
                            if elapsed.as_millis() > 5 {
                                // 如果处理时间超过5毫秒，记录警告
                                println!("⚠ 警告: 交易 {} 处理时间: {:?}", key, elapsed);
                            }
                        }
                    });
                } else {
                    // 通道关闭，任务结束
                    break;
                }
            }
        });
    }
    
    // 专门启动一个任务来分发交易到工作线程
    let mut rx_clone = rx;
    tokio::spawn(async move {
        println!("分发器启动，准备分发交易到工作线程...");
        
        while let Some(key) = rx_clone.recv().await {
            // 并行处理每个监控地址的交易
            for monitored_address in config.monitored_addresses.iter() {
                let address = monitored_address.clone();
                let tx_key = key.clone();
                
                // 发送到工作线程队列，高优先级处理
                if let Err(e) = process_tx.try_send((tx_key, address)) {
                    println!("警告: 工作队列已满，丢弃交易 {}，错误: {:?}", key, e);
                }
            }
        }
    });
    
    // 保持主线程存活
    loop {
        tokio::time::sleep(std::time::Duration::from_secs(60)).await;
        println!("主监控循环运行中...");
    }
} 