# 钱包配置管理 API 使用文档

本文档描述了钱包配置管理 API 的使用方法，该 API 允许用户动态调整跟单行为和配置。

## 1. 健康检查 API

**端点:** `GET /api/health`

**描述:** 检查服务运行状态。

**响应示例:**
```json
{
  "status": "ok",
  "version": "1.0.0",
  "message": "服务运行正常"
}
```

## 2. 钱包配置管理 API

### 2.1 获取钱包配置列表

**端点:** `GET /api/wallet-configs`

**描述:** 获取所有钱包的配置信息，包括跟单状态、比例等。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    },
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": {
      "is_active": false,
      "follow_percentage": 50.0,
      "fee_increase_percentage": 15.0
    }
  }
}
```

### 2.2 更新钱包配置

**端点:** `POST /api/wallet-configs`

**描述:** 更新指定钱包的配置信息，包括跟单状态、比例等。

**请求参数 (`application/json`):**
```json
{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",  // 必需: 钱包地址
    "config": {
        "is_active": true,                    // 必需: 是否激活钱包跟单
        "follow_percentage": 30.0,            // 必需: 跟单比例 (0-100)
        "fee_increase_percentage": 10.0       // 必需: 手续费增加比例 (0-100)
    }
}
```

**参数说明:**
- `wallet_address`: (必需) 要更新的钱包地址，必须是有效的 Solana 公钥格式
- `config.is_active`: (必需) 是否激活该钱包的跟单功能，true 表示激活，false 表示暂停
- `config.follow_percentage`: (必需) 跟单比例，表示跟单金额占原交易金额的百分比，范围 0-100
- `config.fee_increase_percentage`: (必需) 手续费增加比例，表示跟单交易的手续费比原交易增加的百分比，范围 0-100

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包配置更新成功",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    },
    "updated_at": "2023-06-15T08:45:22Z"
  }
}
```

**失败响应示例:**
```json
{
  "success": false,
  "message": "钱包配置更新失败",
  "error": "无效的钱包地址格式"
}
```

### 2.3 暂停钱包跟单

**端点:** `POST /api/wallet-configs/pause`

**描述:** 暂停指定钱包的跟单操作，不会影响正在进行的交易。

**请求参数 (`application/json`):**
```json
{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"  // 必需: 钱包地址
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包跟单已暂停",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "status": "paused",
    "updated_at": "2023-06-15T08:48:35Z"
  }
}
```

**失败响应示例:**
```json
{
  "success": false,
  "message": "暂停钱包跟单失败",
  "error": "钱包配置不存在"
}
```

### 2.4 恢复钱包跟单

**端点:** `POST /api/wallet-configs/resume`

**描述:** 恢复指定钱包的跟单操作。

**请求参数 (`application/json`):**
```json
{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"  // 必需: 钱包地址
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包跟单已恢复",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "status": "active",
    "updated_at": "2023-06-15T08:50:12Z"
  }
}
```

**失败响应示例:**
```json
{
  "success": false,
  "message": "恢复钱包跟单失败",
  "error": "钱包配置不存在"
}
```

### 2.5 获取单个钱包配置

**端点:** `GET /api/wallet-configs/{wallet_address}`

**描述:** 获取指定钱包的配置信息。

**URL 参数:**
- `wallet_address`: 要查询的钱包地址

**成功响应示例:**
```json
{
  "success": true,
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    },
    "last_updated": "2023-06-15T08:45:22Z"
  }
}
```

**失败响应示例:**
```json
{
  "success": false,
  "message": "获取钱包配置失败",
  "error": "钱包配置不存在"
}
```

### 2.6 获取钱包余额

**端点:** `GET /api/wallet-balance`

**描述:** 获取当前钱包的 SOL 和代币余额。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "sol_balance": 1.23456789,
    "tokens": {
      "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": {
        "amount": 1000.0,
        "decimals": 6,
        "symbol": "USDC"
      },
      "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump": {
        "amount": 5000.0,
        "decimals": 9,
        "symbol": "PUMP"
      }
    },
    "last_updated": "2023-06-15T08:45:22Z"
  }
}
```

### 2.7 获取交易历史

**端点:** `GET /api/transaction-history`

**描述:** 获取最近的交易历史记录。

**查询参数:**
- `limit`: (可选) 返回的交易数量，默认为 10
- `offset`: (可选) 分页偏移量，默认为 0

**响应示例:**
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "signature": "4YPSijemh8jg2gG9Gg1j6SXEYegdb2cifoSGLuWuaghyb9uMDbnHZCb9ApbK7a2AJbF6GnwMqhpCv2iEZoy6V7Xp",
        "type": "buy",
        "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
        "amount": 1000.0,
        "price": 0.0001,
        "timestamp": "2023-06-15T08:45:22Z",
        "status": "success"
      },
      {
        "signature": "5XPSijemh8jg2gG9Gg1j6SXEYegdb2cifoSGLuWuaghyb9uMDbnHZCb9ApbK7a2AJbF6GnwMqhpCv2iEZoy6V7Xp",
        "type": "sell",
        "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
        "amount": 500.0,
        "price": 0.0002,
        "timestamp": "2023-06-15T08:40:22Z",
        "status": "success"
      }
    ],
    "total": 2,
    "limit": 10,
    "offset": 0
  }
}
```

## 使用示例

### 使用 cURL 调用示例

```bash
# 获取钱包配置列表
curl -X GET http://localhost:3000/api/wallet-configs

# 获取单个钱包配置
curl -X GET http://localhost:3000/api/wallet-configs/9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump

# 更新钱包配置
curl -X POST http://localhost:3000/api/wallet-configs \
  -H "Content-Type: application/json" \
  -d '{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    }
  }'

# 暂停钱包跟单
curl -X POST http://localhost:3000/api/wallet-configs/pause \
  -H "Content-Type: application/json" \
  -d '{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
  }'

# 恢复钱包跟单
curl -X POST http://localhost:3000/api/wallet-configs/resume \
  -H "Content-Type: application/json" \
  -d '{
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
  }'

# 获取钱包余额
curl -X GET http://localhost:3000/api/wallet-balance

# 获取交易历史
curl -X GET "http://localhost:3000/api/transaction-history?limit=10&offset=0"
```

### 使用 Node.js/Fetch API 调用示例

```javascript
// 获取钱包配置列表
async function getWalletConfigs() {
  const response = await fetch('http://localhost:3000/api/wallet-configs');
  const data = await response.json();
  console.log(data);
}

// 更新钱包配置
async function updateWalletConfig() {
  const response = await fetch('http://localhost:3000/api/wallet-configs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      wallet_address: '9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump',
      config: {
        is_active: true,
        follow_percentage: 30.0,
        fee_increase_percentage: 10.0
      }
    }),
  });
  
  const data = await response.json();
  console.log(data);
}

// 暂停钱包跟单
async function pauseWallet() {
  const response = await fetch('http://localhost:3000/api/wallet-configs/pause', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      wallet_address: '9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump'
    }),
  });
  
  const data = await response.json();
  console.log(data);
}

// 恢复钱包跟单
async function resumeWallet() {
  const response = await fetch('http://localhost:3000/api/wallet-configs/resume', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      wallet_address: '9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump'
    }),
  });
  
  const data = await response.json();
  console.log(data);
}

// 获取钱包余额
async function getWalletBalance() {
  const response = await fetch('http://localhost:3000/api/wallet-balance');
  const data = await response.json();
  console.log(data);
}

// 获取交易历史
async function getTransactionHistory(limit = 10, offset = 0) {
  const response = await fetch(`http://localhost:3000/api/transaction-history?limit=${limit}&offset=${offset}`);
  const data = await response.json();
  console.log(data);
}
```

### 使用 Python 调用示例

```python
import requests

# 获取钱包配置列表
def get_wallet_configs():
    response = requests.get('http://localhost:3000/api/wallet-configs')
    print(response.json())

# 更新钱包配置
def update_wallet_config():
    payload = {
        "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
        "config": {
            "is_active": True,
            "follow_percentage": 30.0,
            "fee_increase_percentage": 10.0
        }
    }
    response = requests.post('http://localhost:3000/api/wallet-configs', json=payload)
    print(response.json())

# 暂停钱包跟单
def pause_wallet():
    payload = {
        "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
    }
    response = requests.post('http://localhost:3000/api/wallet-configs/pause', json=payload)
    print(response.json())

# 恢复钱包跟单
def resume_wallet():
    payload = {
        "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
    }
    response = requests.post('http://localhost:3000/api/wallet-configs/resume', json=payload)
    print(response.json())

# 获取钱包余额
def get_wallet_balance():
    response = requests.get('http://localhost:3000/api/wallet-balance')
    print(response.json())

# 获取交易历史
def get_transaction_history(limit=10, offset=0):
    params = {
        'limit': limit,
        'offset': offset
    }
    response = requests.get('http://localhost:3000/api/transaction-history', params=params)
    print(response.json())
```

## 使用环境变量配置 API 服务器

API 服务器可以通过环境变量进行配置：

- `API_PORT`: API 服务器端口，默认为 3000
- `WALLET_CONFIG_PATH`: 钱包配置文件路径，默认为 "wallet_configs.json"
- `FOLLOW_PERCENTAGE`: 默认跟单比例，默认为 30.0
- `FEE_INCREASE_PERCENTAGE`: 默认手续费增加比例，默认为 10.0
- `USE_ZERO_SLOT`: 是否启用 0-Slot 模式，默认为 false
- `ZERO_SLOT_API_KEY`: 0-Slot API 密钥（如果启用 0-Slot 模式）
- `ZERO_SLOT_ENDPOINT`: 0-Slot 服务器端点（如果启用 0-Slot 模式）

## 注意事项

1. 钱包地址必须是有效的 Solana 公钥格式
2. 跟单比例和手续费增加比例必须在 0-100 之间
3. 配置更新会立即生效，无需重启服务
4. 暂停/恢复操作会立即生效，但不会影响正在进行的交易
5. 建议在更新配置前先获取当前配置，避免覆盖其他设置
6. 所有金额相关的配置都使用浮点数表示百分比
7. 配置数据会持久化到 `WALLET_CONFIG_PATH` 指定的 JSON 文件中
8. 钱包余额数据会定期更新，但可能存在延迟
9. 交易历史记录会保留最近 1000 条记录

## 错误处理

API 使用统一的错误响应格式：
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

常见错误码：
- 400: 请求参数错误
- 404: 钱包地址不存在
- 500: 服务器内部错误

## 安全建议

1. 建议在生产环境中启用 HTTPS
2. 实现适当的访问控制和认证机制
3. 对敏感操作（如暂停/恢复）添加额外的验证步骤
4. 定期备份钱包配置数据
5. 监控 API 调用频率，防止滥用
6. 使用环境变量存储敏感信息
7. 定期轮换 API 密钥和访问令牌

## 数据结构

`WalletConfig` 数据结构：
```rust
pub struct WalletConfig {
    pub is_active: bool,                // 是否激活
    pub follow_percentage: f64,         // 跟单比例
    pub fee_increase_percentage: f64,   // 手续费增加比例
}
```

`TokenInfo` 数据结构：
```rust
pub struct TokenInfo {
    pub amount: f64,                    // 代币数量
    pub decimals: u8,                   // 小数位数
    pub symbol: Option<String>,         // 代币符号
}
```

`TransactionInfo` 数据结构：
```rust
pub struct TransactionInfo {
    pub signature: String,              // 交易签名
    pub type: String,                   // 交易类型 (buy/sell)
    pub token_address: String,          // 代币地址
    pub amount: f64,                    // 交易数量
    pub price: f64,                     // 交易价格
    pub timestamp: String,              // 交易时间
    pub status: String,                 // 交易状态
}
```

配置文件 JSON 结构：
```json
{
  "钱包地址1": {
    "is_active": true,
    "follow_percentage": 30.0,
    "fee_increase_percentage": 10.0
  },
  "钱包地址2": {
    "is_active": false,
    "follow_percentage": 50.0,
    "fee_increase_percentage": 15.0
  }
}
``` 