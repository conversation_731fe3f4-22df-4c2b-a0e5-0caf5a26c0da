#!/bin/bash

echo "============================================="
echo "  Solana交易复制机器人 - Docker一键部署脚本"
echo "============================================="

# 检查Docker是否已安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    echo "安装指南: https://docs.docker.com/get-docker/"
    exit 1
fi

# 检查Docker Compose是否已安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    echo "安装指南: https://docs.docker.com/compose/install/"
    exit 1
fi

# 创建必要的目录
mkdir -p config data/tx_history
echo "已创建配置和数据目录"

# 检查配置文件是否存在
if [ ! -f "config/monitor_addrs.json" ]; then
    echo "注意: 未找到配置文件 config/monitor_addrs.json"
    echo "请确保在启动服务前配置正确的监控地址"
fi

# 启动服务
echo "正在构建和启动服务..."
docker-compose up -d

# 检查服务状态
echo "检查服务状态："
docker-compose ps

echo ""
echo "服务已启动!"
echo "API地址: http://localhost:3000"
echo "健康检查: http://localhost:3000/health"
echo ""
echo "使用以下命令查看日志:"
echo "docker-compose logs -f"
echo ""
echo "要停止服务，请运行:"
echo "docker-compose down" 