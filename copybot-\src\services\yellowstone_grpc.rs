use anyhow::{Result, anyhow};
use futures_util::StreamExt;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use tokio::sync::mpsc;
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::geyser::{
    CommitmentLevel,
    SubscribeRequest,
    SubscribeRequestFilterTransactions,
    subscribe_update::UpdateOneof,
};
use yellowstone_grpc_proto::prelude::TokenBalance;
use serde_json::Value;
use crate::common::logger::Logger;
use tonic::transport::ClientTlsConfig;
use std::collections::HashMap;
use tokio::time::{Duration, interval};
use futures_util::future::FutureExt;
use tokio::select;
use chrono;
use std::time::Instant;

/// 帮助宏 - 用于记录性能埋点
macro_rules! mark {
    ($logger:expr, $name:expr, $start:expr) => {{
        let dur = $start.elapsed().as_secs_f64() * 1_000.0;
        $logger.debug(format!("[TIMING] {}: {:.2} ms", $name, dur));
    }};
}

/// 交易解析结果
#[derive(Debug, Default)]
pub struct ParsedResult {
    pub direction: Option<String>,
    pub mint: String,
    pub amount_in: f64,
    pub amount_out: f64,
    pub price: Option<f64>,
}

/// 交易数据结构
#[derive(Clone)]
pub struct TransactionData {
    pub signature: String,
    pub data: Value,
    pub tx_type: String,
    pub mint: String,
    pub signer: String,
    pub amount: u64,
    pub price: Option<f64>,
    pub sol_value: Option<f64>,  // 添加SOL相关值字段，对于Buy是SOL COST，对于Sell是MIN SOL OUTPUT
    pub token_info: Option<Value>, // 添加 token_info 字段
    pub slot: u64, // 添加slot字段，用于记录交易所在的区块高度
}

/// Yellowstone gRPC监控服务
pub struct YellowstoneGrpcMonitor {
    endpoint: String,
    target_wallet: Pubkey,
    logger: Logger,
}

impl YellowstoneGrpcMonitor {
    /// 创建新的监控服务实例
    pub fn new(endpoint: &str, target_wallet: &str) -> Result<Self> {
        let target = Pubkey::from_str(target_wallet)
            .map_err(|e| anyhow!("无效的目标钱包地址: {}", e))?;
        
        Ok(Self {
            endpoint: endpoint.to_string(),
            target_wallet: target,
            logger: Logger::new("YellowstoneGRPC".to_string()),
        })
    }
    
    /// 启动监控服务
    pub async fn start(&self) -> Result<mpsc::Receiver<TransactionData>> {
        self.logger.log(format!("启动Yellowstone gRPC监控，目标钱包: {}", self.target_wallet));
        
        // 创建一个通道用于传递交易数据
        let (tx, rx) = mpsc::channel::<TransactionData>(100);
        
        // 创建gRPC客户端
        self.logger.log(format!("连接到gRPC端点: {}", self.endpoint));
        
        // 创建一个新的tokio任务来处理连接和监听
        let endpoint = self.endpoint.clone();
        let target_wallet = self.target_wallet;
        let logger_clone = self.logger.clone();
        
        // 使用tokio::spawn启动监控任务
        tokio::spawn(async move {
            Self::run_monitor(endpoint, target_wallet, tx, logger_clone).await;
        });
        
        Ok(rx)
    }
    
    /// 运行监控循环的具名函数
    async fn run_monitor(
        endpoint: String,
        target_wallet: Pubkey,
        tx: mpsc::Sender<TransactionData>,
        logger: Logger,
    ) {
        // 连接重试次数
        let mut retry_count = 0;
        let max_retries = 10; // 最大重试次数
        let mut backoff_seconds = 5; // 初始重试等待时间
        
        loop {
            // 尝试创建客户端并启动监控
            match Self::create_client(endpoint.clone(), &logger).await {
                Ok(client) => {
                    // 重置重试计数
                    retry_count = 0;
                    backoff_seconds = 5;
                    
                    // 启动监控任务
                    let monitor_logger = logger.clone();
                    match Self::monitor_transactions(client, &endpoint, target_wallet, tx.clone(), monitor_logger).await {
                        Ok(_) => {
                            // 监控正常结束，通常不会发生
                            logger.log("监控任务正常结束，准备重新启动");
                        },
                        Err(e) => {
                            logger.error(format!("监控任务出错: {}", e));
                        }
                    }
                },
                Err(e) => {
                    logger.error(format!("创建客户端失败: {}", e));
                }
            }
            
            // 增加重试计数
            retry_count += 1;
            
            // 检查是否超过最大重试次数
            if retry_count > max_retries {
                logger.error(format!("已达到最大重试次数 {}，停止重试", max_retries));
                break;
            }
            
            // 使用指数退避策略计算下一次重试的等待时间
            let wait_time = backoff_seconds;
            backoff_seconds = std::cmp::min(backoff_seconds * 2, 60); // 最长等待60秒
            
            logger.log(format!("第 {} 次重试，等待 {} 秒后重新连接...", retry_count, wait_time));
            tokio::time::sleep(tokio::time::Duration::from_secs(wait_time)).await;
        }
        
        logger.error("监控服务停止，无法重新连接");
    }
    
    /// 创建gRPC客户端
    async fn create_client(endpoint: String, logger: &Logger) -> Result<GeyserGrpcClient<impl tonic::service::Interceptor>> {
        logger.log(format!("正在连接到gRPC端点: {}", endpoint));
        
        // 尝试方式1: 使用TLS配置，带域名验证，增加消息大小限制
        let domain = endpoint.split(':').next().unwrap_or(&endpoint);
        // logger.debug(format!("尝试方式1: 使用带域名验证({})的TLS配置", domain));
        
        let formatted_endpoint = if !endpoint.starts_with("http") {
            if endpoint.contains(":443") {
                format!("https://{}", endpoint)
            } else {
                format!("http://{}", endpoint)
            }
        } else {
            endpoint.clone()
        };
        
        // logger.debug(format!("使用格式化后的端点: {}", formatted_endpoint));
        
        // 使用不同的TLS配置选项
        let tls_config = ClientTlsConfig::new()
            .domain_name(domain);
            
        match GeyserGrpcClient::connect(
            formatted_endpoint.clone(),
            None::<String>,
            Some(tls_config),
        ) {
            Ok(client) => {
                logger.log("成功连接到Yellowstone gRPC服务 (方式1)");
                return Ok(client);
            },
            Err(e) => {
                let error_msg = format!("连接方式1失败: {}", e);
                // logger.debug(error_msg);
            }
        }
        
        // 尝试方式2: 不使用TLS配置
        // logger.debug("尝试方式2: 不使用TLS配置");
        match GeyserGrpcClient::connect(
            formatted_endpoint.clone(),
            None::<String>,
            None,
        ) {
            Ok(client) => {
                logger.log("成功连接到Yellowstone gRPC服务 (方式2)");
                return Ok(client);
            },
            Err(e) => {
                let error_msg = format!("连接方式2失败: {}", e);
                // logger.debug(error_msg);
            }
        }
        
        // 尝试方式3: 使用不带域名验证的TLS
        logger.debug("尝试方式3: 使用不带域名验证的TLS");
        let basic_tls = ClientTlsConfig::new();
        match GeyserGrpcClient::connect(
            formatted_endpoint.clone(),
            None::<String>,
            Some(basic_tls),
        ) {
            Ok(client) => {
                logger.log("成功连接到Yellowstone gRPC服务 (方式3)");
                return Ok(client);
            },
            Err(e) => {
                let error_msg = format!("连接方式3失败: {}", e);
                // logger.debug(error_msg);
            }
        }
        
        // 尝试方式4: 尝试使用原始端点
        logger.debug("尝试方式4: 使用原始端点不带格式化");
        match GeyserGrpcClient::connect(
            endpoint.clone(),
            None::<String>,
            None,
        ) {
            Ok(client) => {
                logger.log("成功连接到Yellowstone gRPC服务 (方式4)");
                return Ok(client);
            },
            Err(e) => {
                let error_msg = format!("连接方式4失败: {}", e);
                // logger.debug(error_msg);
            }
        }
        
        // 尝试方式5: 使用特定的端口风格
        let specific_endpoint = if endpoint.contains(":443") {
            endpoint.replace(":443", ":443")
        } else if !endpoint.contains(":") {
            format!("{}:443", endpoint)
        } else {
            endpoint.clone()
        };
        
        logger.debug(format!("尝试方式5: 使用特定端口风格 {}", specific_endpoint));
        match GeyserGrpcClient::connect(
            specific_endpoint,
            None::<String>,
            None,
        ) {
            Ok(client) => {
                logger.log("成功连接到Yellowstone gRPC服务 (方式5)");
                return Ok(client);
            },
            Err(e) => {
                let error_msg = format!("连接方式5失败: {}", e);
                // logger.debug(error_msg);
            }
        }
        
        // 如果所有尝试都失败，返回错误
        let fallback_error = "所有连接方式都失败";
        logger.error(fallback_error);
        Err(anyhow!(fallback_error))
    }
    
    /// 监控交易数据
    async fn monitor_transactions<I>(
        mut client: GeyserGrpcClient<I>,
        _endpoint: &str,
        target_wallet: Pubkey,
        tx: mpsc::Sender<TransactionData>,
        logger: Logger,
    ) -> Result<()>
    where
        I: tonic::service::Interceptor + Send + Sync + 'static,
    {
        logger.log("开始监控交易...");
        
        // 创建订阅请求的交易过滤器
        let mut transactions_filter = HashMap::new();
        transactions_filter.insert(
            "default".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                signature: None,
                account_include: vec![target_wallet.to_string()],
                account_exclude: vec![],
                account_required: vec![],
            },
        );
        
        // 创建订阅请求 - 根据最新版本的API调整
        let request = SubscribeRequest {
            accounts: HashMap::new(),
            slots: HashMap::new(),
            transactions: transactions_filter,
            blocks: HashMap::new(),
            blocks_meta: HashMap::new(),
            entry: HashMap::new(),
            commitment: Some(CommitmentLevel::Confirmed as i32),
            accounts_data_slice: vec![],
            ping: None,
            subscribe_banking_transaction_results: false,
        };
        
        // 创建ping请求 - 用于保持连接活跃
        let ping_request = SubscribeRequest {
            accounts: HashMap::new(),
            slots: HashMap::new(),
            transactions: HashMap::new(),
            blocks: HashMap::new(),
            blocks_meta: HashMap::new(),
            entry: HashMap::new(),
            commitment: None,
            accounts_data_slice: vec![],
            ping: Some(yellowstone_grpc_proto::geyser::SubscribeRequestPing {
                id: 1,
            }),
            subscribe_banking_transaction_results: false,
        };
        
        // 创建订阅流并发送请求
        logger.log("发送订阅请求...");
        let sub_result = client.subscribe_with_request(Some(request)).await;
        
        let mut subscription = match sub_result {
            Ok((_, stream)) => {
                logger.log("成功创建订阅流");
                stream // 只使用stream部分
            },
            Err(e) => {
                let error_msg = format!("创建订阅流失败: {}", e);
                logger.error(error_msg.clone());
                return Err(anyhow!(error_msg));
            }
        };
        
        logger.log("订阅已创建，等待数据...");
        
        // 创建一个每5秒发送一次ping的定时器
        let mut ping_interval = interval(Duration::from_secs(5));
        
        // 处理订阅数据
        let mut heartbeat_counter = 0;
        let start_time = std::time::Instant::now();
        
        loop {
            select! {
                // 处理接收到的消息
                msg = subscription.next().fuse() => {
                    match msg {
                        Some(Ok(message)) => {
                            // 打印心跳信息
                            let elapsed = start_time.elapsed().as_secs();
                            if elapsed > heartbeat_counter * 30 {
                                heartbeat_counter += 1;
                                logger.log(format!("监控服务运行中，已监听 {} 秒，等待交易...", elapsed));
                            }
                            
                            // 1. 消息处理起点
                            let t0 = Instant::now();
                            
                            if let Some(update) = message.update_oneof {
                                match update {
                                    UpdateOneof::Transaction(txn) => {
                                        // 获取交易签名
                                        if let Some(transaction_info) = &txn.transaction {
                                            // 2. 处理签名
                                            let t_sig = Instant::now();
                                            // 处理签名 - 检查signature，它是Vec<u8>类型
                                            if !transaction_info.signature.is_empty() {
                                                // 直接使用transaction_info.signature，它已经是Vec<u8>
                                                let sig_str = bs58::encode(&transaction_info.signature).into_string();
                                                // logger.log(format!("收到交易 {}", sig_str)); // 注释掉重复日志
                                                println!("----------------------------------");
                                                mark!(logger, "签名处理", t_sig);
                                                
                                                // 3. 转换交易数据
                                                let t_convert = Instant::now();
                                                // 将交易数据转换为JSON以便解析
                                                let transaction_json = match Self::convert_transaction_to_json(&txn) {
                                                    Ok(json) => json,
                                                    Err(e) => {
                                                        logger.error(format!("转换交易数据失败: {}", e));
                                                        mark!(logger, "交易数据转换失败", t0);
                                                        continue;
                                                    }
                                                };
                                                mark!(logger, "交易数据转换", t_convert);
                                                
                                                // 4. 发送交易数据
                                                let t_send = Instant::now();
                                                // 发送交易数据到通道
                                                if let Err(e) = tx.send(TransactionData {
                                                    signature: sig_str,
                                                    data: transaction_json,
                                                    tx_type: String::new(),
                                                    mint: String::new(),
                                                    signer: String::new(),
                                                    amount: 0,
                                                    price: None,
                                                    sol_value: None,
                                                    token_info: None,
                                                    slot: 0,
                                                }).await {
                                                    logger.error(format!("发送交易数据失败: {}", e));
                                                    mark!(logger, "发送交易数据失败", t_send);
                                                } else {
                                                    mark!(logger, "发送交易数据", t_send);
                                                }
                                                
                                                // 5. 总耗时
                                                mark!(logger, "总消息处理耗时", t0);
                                            }
                                        }
                                    },
                                    UpdateOneof::Pong(pong) => {
                                        // 接收到服务器的pong响应
                                        logger.debug(format!("收到pong响应: id={}", pong.id));
                                    },
                                    _ => {
                                        // 其他更新类型，忽略
                                    }
                                }
                            }
                        },
                        Some(Err(e)) => {
                            logger.error(format!("接收消息时出错: {}", e));
                            
                            // 短暂等待后尝试重连
                            tokio::time::sleep(Duration::from_secs(5)).await;
                            return Err(anyhow!("连接中断，需要重新建立连接"));
                        },
                        None => {
                            // 流已结束
                            logger.error("连接流已关闭");
                            return Err(anyhow!("连接流已关闭，需要重新建立连接"));
                        }
                    }
                },
                
                // 定期发送ping请求
                _ = ping_interval.tick().fuse() => {
                    logger.debug("发送ping请求...");
                    // 发送ping请求
                    if let Err(e) = client.subscribe_with_request(Some(ping_request.clone())).await {
                        logger.error(format!("发送ping请求失败: {}", e));
                        // 如果ping失败，可能表示连接已断开，尝试重连
                        return Err(anyhow!("ping失败，需要重新建立连接"));
                    } else {
                        logger.debug("发送ping请求成功");
                    }
                }
            }
        }
    }
    
    /// 将交易数据转换为JSON格式
    fn convert_transaction_to_json(txn: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransaction) -> Result<Value> {
        // 创建交易数据结构
        let mut tx_data = serde_json::json!({});
        
        // 转换meta数据
        if let Some(transaction_info) = &txn.transaction {
            if let Some(meta) = &transaction_info.meta {
                let mut meta_json = serde_json::json!({});
                
                // 转换日志消息
                if !meta.log_messages.is_empty() {
                    meta_json["logMessages"] = serde_json::json!(meta.log_messages);
                }
                
                // 转换内部指令
                if !meta.inner_instructions.is_empty() {
                    let inner_instructions: Vec<Value> = meta.inner_instructions.iter().map(|ii| {
                        let instructions: Vec<Value> = ii.instructions.iter().map(|inst| {
                            serde_json::json!({
                                "programIdIndex": inst.program_id_index,
                                "accounts": inst.accounts.clone(),
                                "data": inst.data.clone(),
                            })
                        }).collect();
                        
                        serde_json::json!({
                            "index": ii.index,
                            "instructions": instructions,
                        })
                    }).collect();
                    
                    meta_json["innerInstructions"] = serde_json::json!(inner_instructions);
                }
                
                // 转换token余额
                if !meta.pre_token_balances.is_empty() || !meta.post_token_balances.is_empty() {
                    meta_json["preTokenBalances"] = Self::convert_token_balances(&meta.pre_token_balances);
                    meta_json["postTokenBalances"] = Self::convert_token_balances(&meta.post_token_balances);
                }
                
                // 设置meta到主数据结构
                tx_data["meta"] = meta_json;
            }
            
            // 转换交易数据
            if let Some(transaction) = &transaction_info.transaction {
                let mut tx_json = serde_json::json!({});
                
                // 设置签名
                if !transaction.signatures.is_empty() {
                    let signatures = transaction.signatures.iter().map(|sig| {
                        bs58::encode(sig).into_string()
                    }).collect::<Vec<String>>();
                    tx_json["signatures"] = serde_json::json!(signatures);
                }
                
                // 转换消息
                if let Some(message) = &transaction.message {
                    let mut message_json = serde_json::json!({});
                    
                    // 设置header
                    if let Some(header) = &message.header {
                        message_json["header"] = serde_json::json!({
                            "numRequiredSignatures": header.num_required_signatures,
                            "numReadonlySignedAccounts": header.num_readonly_signed_accounts,
                            "numReadonlyUnsignedAccounts": header.num_readonly_unsigned_accounts,
                        });
                    }
                    
                    // 设置账户键
                    if !message.account_keys.is_empty() {
                        let account_keys = message.account_keys.iter().map(|key| {
                            bs58::encode(key).into_string()
                        }).collect::<Vec<String>>();
                        message_json["accountKeys"] = serde_json::json!(account_keys);
                    }
                    
                    // 设置最近的blockhash
                    message_json["recentBlockhash"] = serde_json::json!(
                        bs58::encode(&message.recent_blockhash).into_string()
                    );
                    
                    // 设置指令
                    if !message.instructions.is_empty() {
                        let instructions = message.instructions.iter().map(|inst| {
                            let mut inst_json = serde_json::json!({
                                "programIdIndex": inst.program_id_index,
                                "accounts": inst.accounts.clone(),
                            });
                            
                            if !inst.data.is_empty() {
                                inst_json["data"] = serde_json::json!(
                                    bs58::encode(&inst.data).into_string()
                                );
                            }
                            
                            inst_json
                        }).collect::<Vec<Value>>();
                        
                        message_json["instructions"] = serde_json::json!(instructions);
                    }
                    
                    tx_json["message"] = message_json;
                }
                
                tx_data["transaction"] = tx_json;
            }
        }
        
        Ok(tx_data)
    }
    
    /// 转换Token余额为JSON格式
    fn convert_token_balances(balances: &[TokenBalance]) -> Value {
        let balance_array = balances.iter().map(|tb| {
            let mut balance_json = serde_json::json!({
                "accountIndex": tb.account_index,
                "mint": bs58::encode(&tb.mint).into_string(),
            });
            
            // 转换UI金额
            if let Some(ui_token_amount) = &tb.ui_token_amount {
                balance_json["uiTokenAmount"] = serde_json::json!({
                    "amount": ui_token_amount.amount.clone(),
                    "decimals": ui_token_amount.decimals,
                    "uiAmount": ui_token_amount.ui_amount,
                    "uiAmountString": ui_token_amount.ui_amount_string.clone(),
                });
            }
            
            // 设置owner和programId - 修正类型检查
            if !tb.owner.is_empty() {
                balance_json["owner"] = serde_json::json!(bs58::encode(&tb.owner).into_string());
            }
            
            if !tb.program_id.is_empty() {
                balance_json["programId"] = serde_json::json!(bs58::encode(&tb.program_id).into_string());
            }
            
            balance_json
        }).collect::<Vec<Value>>();
        
        serde_json::json!(balance_array)
    }
    
    /// 将lamports转换为UI金额
    fn lamports_to_ui_amount(amount_str: &str, decimals: u32) -> f64 {
        if let Ok(amount) = amount_str.parse::<u64>() {
            amount as f64 / 10f64.powi(decimals as i32)
        } else {
            0.0
        }
    }
}

/// 交易解析服务
pub struct TransactionParserService {
    logger: Logger,
}

impl TransactionParserService {
    /// 创建新的交易解析服务
    pub fn new() -> Self {
        Self {
            logger: Logger::new("TransactionParser".to_string()),
        }
    }
    
    /// 解析交易数据
    pub async fn parse_transaction(&self, tx_data: &Value, _target_pubkey: &Pubkey) -> Result<ParsedResult> {
        // 1. 总起点
        let t0 = Instant::now();

        let mut result = ParsedResult::default();
        
        // 2. 处理原始日志
        let t_logs = Instant::now();
        // 直接输出原始日志数据，不进行解析
        if let Some(meta) = tx_data.get("meta") {
            if let Some(logs) = meta.get("logMessages").and_then(|l| l.as_array()) {
                // 直接打印完整的原始日志
                self.logger.log(format!("原始交易日志: {}", serde_json::to_string_pretty(logs).unwrap_or_default()));
                
                // 如果需要保存原始数据到文件
                let log_time = chrono::Local::now().format("%Y%m%d_%H%M%S").to_string();
                let log_file = format!("transaction_log_{}.json", log_time);
                
                // 文件保存功能临时禁用
                self.logger.log(format!("文件保存功能已禁用，跳过保存原始交易数据到: {}", log_file));
                
                /* 
                // 以下代码保留但暂时不执行
                // 尝试保存原始交易数据
                if let Ok(data_json) = serde_json::to_string_pretty(tx_data) {
                    if let Ok(mut file) = std::fs::File::create(&log_file) {
                        use std::io::Write;
                        if let Err(e) = file.write_all(data_json.as_bytes()) {
                            self.logger.error(format!("无法写入日志文件: {}", e));
                        } else {
                            self.logger.log(format!("原始交易数据已保存到: {}", log_file));
                        }
                    }
                }
                */
            }
        }
        mark!(self.logger, "处理原始日志", t_logs);
        
        // 3. 提取余额信息
        let t_balances = Instant::now();
        // 尝试从交易元数据中提取基本信息
        if let Some(meta) = tx_data.get("meta") {
            // 提取并打印完整的预后余额信息
            if let Some(pre_balances) = meta.get("preTokenBalances") {
                self.logger.log(format!("预交易代币余额: {}", serde_json::to_string_pretty(pre_balances).unwrap_or_default()));
            }
            
            if let Some(post_balances) = meta.get("postTokenBalances") {
                self.logger.log(format!("交易后代币余额: {}", serde_json::to_string_pretty(post_balances).unwrap_or_default()));
            }
        }
        mark!(self.logger, "提取余额信息", t_balances);
        
        // 4. 序列化完整交易数据
        let t_serialize = Instant::now();
        // 打印完整的交易数据
        self.logger.log(format!("完整交易数据: {}", serde_json::to_string(tx_data).unwrap_or_default()));
        mark!(self.logger, "序列化交易数据", t_serialize);
        
        // 5. 总耗时
        mark!(self.logger, "总交易解析耗时", t0);
        
        // 返回空的结果
        Ok(result)
    }
} 