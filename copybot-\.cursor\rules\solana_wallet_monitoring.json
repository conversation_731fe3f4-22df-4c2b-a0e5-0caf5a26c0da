{"name": "Solana钱包监控规则", "description": "Solana钱包余额监控和gRPC实时更新配置", "config": {"endpoints": {"rpc": "https://mainnet.helius-rpc.com/?api-key=40b3da8c-5adb-4822-a333-44cbfb58d3c5", "grpc": "solana-yellowstone-grpc.publicnode.com:443"}, "development": {"warning_policy": "ignore_unless_critical", "fix_priority": "errors_first", "code_style": "follow_existing_patterns"}, "runtime": {"tokio_flavor": "multi_thread", "cache_ttl_seconds": 60}}, "testing": {"test_attributes": ["#[tokio::test(flavor = \"multi_thread\")]"], "mock_redis": false, "use_real_connections": true}, "wallet_monitoring": {"enable_caching": true, "invalidate_on_transaction": true, "invalidate_on_token_update": true, "retry_failed_connections": true}}