use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use anyhow::{Result, anyhow};
use log;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SpecialWalletConfig {
    pub slippage_percentage: f64,
    pub tip_percentage: f64, 
    pub priority_fee_multiplier: f64,
    pub compute_limit: u64,
    pub note: Option<String>, // 备注字段
}

impl Default for SpecialWalletConfig {
    fn default() -> Self {
        Self {
            slippage_percentage: 0.1,   // 默认0.1%
            tip_percentage: 1.0,        // 默认1%
            priority_fee_multiplier: 6.0, // 默认6倍
            compute_limit: 70000,       // 默认70000
            note: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SpecialWalletConfigs {
    pub wallets: HashMap<String, SpecialWalletConfig>
}

impl SpecialWalletConfigs {
    // 加载配置
    pub fn load() -> Result<Self> {
        let config_path = "special_wallet_configs.json";
        if Path::new(config_path).exists() {
            let content = fs::read_to_string(config_path)?;
            let configs: SpecialWalletConfigs = serde_json::from_str(&content)?;
            Ok(configs)
        } else {
            // 如果文件不存在，创建默认配置
            let default_config = Self::default();
            default_config.save()?;
            Ok(default_config)
        }
    }

    // 保存配置
    pub fn save(&self) -> Result<()> {
        let config_path = "special_wallet_configs.json";
        let json = serde_json::to_string_pretty(self)?;
        fs::write(config_path, json)?;
        Ok(())
    }

    // 获取钱包配置
    pub fn get_wallet_config(&self, wallet_address: &str) -> Option<&SpecialWalletConfig> {
        self.wallets.get(wallet_address)
    }

    // 添加钱包配置
    pub fn add_wallet_config(&mut self, wallet_address: String, config: SpecialWalletConfig) -> Result<()> {
        self.wallets.insert(wallet_address, config);
        self.save()?;
        Ok(())
    }

    // 删除钱包配置
    pub fn remove_wallet_config(&mut self, wallet_address: &str) -> Result<bool> {
        let removed = self.wallets.remove(wallet_address).is_some();
        if removed {
            self.save()?;
        }
        Ok(removed)
    }
}

// 便捷函数，用于在trade_handler中使用
pub fn get_special_wallet_config(wallet_address: &str) -> Option<SpecialWalletConfig> {
    match SpecialWalletConfigs::load() {
        Ok(configs) => configs.get_wallet_config(wallet_address).cloned(),
        Err(e) => {
            log::error!("无法加载专用钱包配置: {}", e);
            None
        }
    }
} 