# API接口使用文档

## 1. 健康检查API

**端点:** `GET /api/health`

**响应示例:**
```json
{
  "status": "ok",
  "version": "1.0.0",
  "message": "服务运行正常"
}
```

## 2. 卖出代币API

**端点:** `POST /api/sell`

### 请求参数 (`application/json`)

```json
{
    "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump", // 必需: 要卖出的代币地址
    "percentage": 100,                           // 必需: 卖出数量百分比 (25, 50, 75, 100)
    "slippage_bps": 500,                         // 可选: 滑点基点 (0.5% -> 50)
    "priority_fee": 20000,                       // 可选: 优先费 (微lamports)
    // --- 小费参数 (可选, 优先级: zero_slot_tip > tip_fixed > tip_percentage) ---
    // "zero_slot_tip": 0.0015,                  // 可选: 0-Slot固定小费金额 (SOL, 最高优先级)
    "tip_fixed": 0.003525,                       // 可选: 固定小费金额 (SOL, 第二优先级)
    // "tip_percentage": 0.5,                    // 可选: 小费百分比 (例如 0.5 表示 0.5%, 最低优先级)
    // -------------------------------------------------------------------------
    "follow_system_defaults": true             // 可选: 是否在未提供 slippage_bps 和 priority_fee 时使用系统环境变量 (默认 true)
}
```

### 参数说明

- `token_address`: (必需) 要卖出的代币的 Mint 地址。
- `percentage`: (必需) 要卖出的代币数量占当前持有量的百分比。必须是 25、50、75 或 100。
- `slippage_bps`: (可选) 交易滑点，以基点（BPS）为单位。100 BPS = 1%。如果未提供且 `follow_system_defaults` 为 true，则尝试读取环境变量 `SLIPPAGE`，否则默认为 50 BPS (0.5%)。
- `priority_fee`: (可选) Solana 交易的优先费，以微 Lamports (micro-lamports) 为单位。如果未提供且 `follow_system_defaults` 为 true，则尝试读取环境变量 `COMPUTE_UNIT_PRICE`，否则默认为 10000。

- **小费设置 (可选，按优先级顺序):**
    1.  `zero_slot_tip`: (可选, SOL) **最高优先级**。如果提供，将使用此固定 SOL 金额作为 **0-Slot 交易**的小费，并忽略 `tip_fixed` 和 `tip_percentage`。此参数仅在系统启用 0-Slot 时生效 (`USE_ZERO_SLOT=true`)。金额限制在 0.0005 - 1.0 SOL 之间。
    2.  `tip_fixed`: (可选, SOL) **第二优先级**。如果未提供 `zero_slot_tip` 但提供了此参数，将使用此固定的 SOL 金额作为交易小费（无论是普通 RPC 还是 0-Slot 交易），并忽略 `tip_percentage`。金额限制在 0.0 - 1.0 SOL 之间。
    3.  `tip_percentage`: (可选, %) **最低优先级**。只有在 `zero_slot_tip` 和 `tip_fixed` 都**未**提供时，此参数才生效。系统会根据此百分比和估算的交易 SOL 价值计算小费金额。计算出的小费会与环境变量 `TIP_PERCENTAGE` 的值比较，取较大者，并限制在 0.0005 - 1.0 SOL 之间。百分比限制在 0 - 100 之间。

- **小费回退逻辑 (若请求中未提供任何小费参数):**
    1.  尝试读取环境变量 `FIXED_TIP_SOL` 并使用其值。
    2.  若 `FIXED_TIP_SOL` 未设置，则尝试读取环境变量 `TIP_PERCENTAGE` (默认为 1%) 并基于交易金额计算小费。
    3.  若上述环境变量均未设置，则使用硬编码的默认最小小费 0.0005 SOL。

- `follow_system_defaults`: (可选, boolean) 如果为 `true` (默认)，当请求中未提供 `slippage_bps` 或 `priority_fee` 时，系统会尝试从环境变量中读取它们的默认值。如果为 `false`，则直接使用代码中的硬编码默认值。

### 响应格式

**成功响应示例:**
```json
{
  "success": true,
  "message": "卖出交易成功完成 (25% 的代币 9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump)",
  "signatures": ["4YPSijemh8jg2gG9Gg1j6SXEYegdb2cifoSGLuWuaghyb9uMDbnHZCb9ApbK7a2AJbF6GnwMqhpCv2iEZoy6V7Xp"],
  "error": null,
  "status": "已完成",
  "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
  "token_symbol": null,
  "sell_percentage": 25,
  "sell_amount": 125801.78098375,
  "sell_amount_raw": 125801780983750,
  "tip_amount": 0.001,
  "slippage_bps": 50,
  "priority_fee": 20000,
  "submitted_at": "2025-05-06 13:04:23"
}
```

**失败响应示例:**
```json
{
  "success": false,
  "message": "卖出交易失败: 代币余额不足",
  "signatures": null,
  "error": "代币余额不足",
  "status": "失败",
  "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
  "token_symbol": null,
  "sell_percentage": 25,
  "sell_amount": 125801.78098375,
  "sell_amount_raw": 125801780983750,
  "tip_amount": 0.001,
  "slippage_bps": 50,
  "priority_fee": 20000,
  "submitted_at": "2025-05-06 13:04:23"
}
```

**响应字段说明:**
- `success`: 交易是否成功
- `message`: 响应消息
- `signatures`: 交易签名列表，成功时包含签名，失败时为null
- `error`: 错误信息，成功时为null
- `status`: 交易状态（已完成、失败）
- `token_address`: 代币地址
- `token_symbol`: 代币符号（如有，当前版本中始终为null）
- `sell_percentage`: 卖出百分比
- `sell_amount`: 卖出数量（UI单位，经过小数位处理）
- `sell_amount_raw`: 卖出数量（原始单位，无小数）
- `tip_amount`: 小费数量（SOL）
- `slippage_bps`: 滑点设置（基点）
- `priority_fee`: 优先费设置（微lamports）
- `submitted_at`: 提交时间

## 使用cURL调用示例

```bash
curl -X POST http://localhost:3000/api/sell \
  -H "Content-Type: application/json" \
  -d '{
    "token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "percentage": 50,
    "slippage_bps": 200,
    "tip_percentage": 1.0,
    "priority_fee": 10000
  }'
```

## 使用Node.js/Fetch API调用示例

```javascript
// 使用Fetch API
async function sellToken() {
  const response = await fetch('http://localhost:3000/api/sell', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      token_address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      percentage: 75,
      slippage_bps: 100, // 1% 滑点
      tip_fixed: 0.001,
      follow_system_defaults: true
    }),
  });
  
  const data = await response.json();
  console.log(data);
}

sellToken();
```

## 使用Python调用示例

```python
import requests

url = "http://localhost:3000/api/sell"
payload = {
    "token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "percentage": 100,
    "slippage_bps": 300, # 3% 滑点
    "priority_fee": 15000
}

response = requests.post(url, json=payload)
print(response.json())
```

## 使用环境变量配置API服务器

API服务器可以通过环境变量进行配置：

- `API_PORT`: API服务器端口，默认为3000
- `COMPUTE_UNIT_PRICE`: 默认计算单元价格，以微lamports为单位
- `SLIPPAGE`: 默认滑点值，以基点为单位（50表示0.5%），默认为50

## 注意事项

1. 卖出百分比必须是25、50、75或100之一
2. 代币地址必须是有效的Solana公钥格式
3. 如果同时提供了多个小费参数 (`zero_slot_tip`, `tip_fixed`, `tip_percentage`)，系统将按照 `zero_slot_tip` > `tip_fixed` > `tip_percentage` 的优先级顺序选择一个使用。
4. priority_fee参数用于设置交易优先级，数值越高，交易被打包的优先级越高
5. slippage_bps参数表示滑点百分比，以基点(basis points)为单位：
   - 1基点 = 0.01%
   - 100基点 = 1%
   - 1000基点 = 10%
   - 范围：0-5000（0-50%）
   - 默认值：50（0.5%）
   - 价格波动大的代币建议设置更高的滑点 

## 3. 监控地址管理API

这些API接口允许您在不重启服务的情况下管理系统监控的钱包地址，包括添加、更新、删除和查询监控地址配置。所有监控地址配置会被保存到`monitor_addrs.json`文件，同时系统会实时监控该文件的变化以随时更新内存中的配置。

### 3.1 获取所有监控地址

**端点:** `GET /api/monitor-addresses`

**响应示例:**
```json
{
  "success": true,
  "message": "成功获取监控地址",
  "data": {
    "sol_address": "So11111111111111111111111111111111111111112",
    "unwanted_key": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",
    "targets": [
      "WaLLeTAddReSS1111111111111111111111111111111",
      "WaLLeTAddReSS2222222222222222222222222222222"
    ],
    "wallets": {
      "WaLLeTAddReSS1111111111111111111111111111111": {
        "follow_percentage": 30.0,
        "slippage_percentage": 2.0,
        "tip_percentage": 1.0,
        "min_price_multiplier": 0.8,
        "max_price_multiplier": 1.2,
        "priority_fee": 50000,
        "compute_unit_limit": 200000,
        "note": "重要钱包，高回报率"
      },
      "WaLLeTAddReSS2222222222222222222222222222222": {
        "follow_percentage": 20.0,
        "slippage_percentage": 1.5,
        "tip_percentage": 0.8,
        "min_price_multiplier": 0.9,
        "max_price_multiplier": 1.1,
        "priority_fee": 40000,
        "compute_unit_limit": 150000,
        "note": "测试钱包"
      }
    }
  }
}
```

### 3.2 更新所有监控地址

**端点:** `POST /api/monitor-addresses`

**请求参数 (`application/json`):**
```json
{
  "sol_address": "So11111111111111111111111111111111111111112",
  "unwanted_key": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN", 
  "targets": [
    "WaLLeTAddReSS1111111111111111111111111111111",
    "WaLLeTAddReSS2222222222222222222222222222222"
  ],
  "wallets": {
    "WaLLeTAddReSS1111111111111111111111111111111": {
      "follow_percentage": 30.0,
      "slippage_percentage": 2.0,
      "tip_percentage": 1.0,
      "min_price_multiplier": 0.8,
      "max_price_multiplier": 1.2,
      "priority_fee": 50000,
      "compute_unit_limit": 200000,
      "note": "重要钱包，高回报率"
    },
    "WaLLeTAddReSS2222222222222222222222222222222": {
      "follow_percentage": 20.0,
      "slippage_percentage": 1.5,
      "tip_percentage": 0.8,
      "min_price_multiplier": 0.9,
      "max_price_multiplier": 1.1,
      "priority_fee": 40000,
      "compute_unit_limit": 150000,
      "note": "测试钱包" 
    }
  }
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "监控地址配置已更新"
}
```

### 3.3 获取单个监控地址

**端点:** `GET /api/monitor-addresses/{address}`

**路径参数:**
- `address`: 要查询的钱包地址

**响应示例:**
```json
{
  "success": true,
  "data": {
    "follow_percentage": 30.0,
    "slippage_percentage": 2.0,
    "tip_percentage": 1.0,
    "min_price_multiplier": 0.8,
    "max_price_multiplier": 1.2,
    "priority_fee": 50000,
    "compute_unit_limit": 200000,
    "note": "重要钱包，高回报率"
  }
}
```

### 3.4 添加监控地址

**端点:** `POST /api/monitor-addresses/add`

**请求参数 (`application/json`):**
```json
{
  "address": "WaLLeTAddReSS3333333333333333333333333333333",
  "follow_percentage": 25.0,
  "slippage_percentage": 2.5,
  "tip_percentage": 1.2,
  "min_price_multiplier": 0.85,
  "max_price_multiplier": 1.15,
  "priority_fee": 45000,
  "compute_unit_limit": 180000
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "监控地址已添加"
}
```

### 3.5 更新监控地址

**端点:** `PUT /api/monitor-addresses/update`

**请求参数 (`application/json`):**
```json
{
  "address": "WaLLeTAddReSS1111111111111111111111111111111",
  "follow_percentage": 35.0,
  "slippage_percentage": 2.2,
  "tip_percentage": 1.5,
  "min_price_multiplier": 0.75,
  "max_price_multiplier": 1.25,
  "priority_fee": 55000,
  "compute_unit_limit": 220000
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "监控地址已更新"
}
```

### 3.6 删除监控地址

**端点:** `DELETE /api/monitor-addresses/delete`

**请求参数 (`application/json`):**
```json
{
  "address": "WaLLeTAddReSS2222222222222222222222222222222"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "监控地址已删除"
}
```

### 3.7 更新监控地址备注

**端点:** `POST /api/monitor-addresses/update-note`

**请求参数 (`application/json`):**
```json
{
  "address": "WaLLeTAddReSS1111111111111111111111111111111",
  "note": "非常重要的高盈利钱包，密切关注"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "监控地址备注已更新"
}
```

## 4. 钱包配置管理API

### 4.1 获取钱包配置列表

**端点:** `GET /api/wallet_configs`

**响应示例:**
```json
{
  "success": true,
  "data": {
    "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    },
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": {
      "is_active": false,
      "follow_percentage": 50.0,
      "fee_increase_percentage": 15.0
    }
  }
}
```

### 4.2 获取单个钱包配置

**端点:** `GET /api/wallet_configs/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    }
  }
}
```

### 4.3 创建钱包配置

**端点:** `POST /api/wallet_configs`

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
  "config": {
    "is_active": true,
    "follow_percentage": 30.0,
    "fee_increase_percentage": 10.0
  }
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包配置已创建",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 30.0,
      "fee_increase_percentage": 10.0
    }
  }
}
```

### 4.4 更新钱包配置

**端点:** `PUT /api/wallet_configs/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
  "config": {
    "is_active": true,
    "follow_percentage": 35.0,
    "fee_increase_percentage": 12.0
  }
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包配置已更新",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
    "config": {
      "is_active": true,
      "follow_percentage": 35.0,
      "fee_increase_percentage": 12.0
    }
  }
}
```

### 4.5 暂停钱包跟单

**端点:** `POST /api/wallet_configs/pause`

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包跟单已暂停",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
  }
}
```

### 4.6 恢复钱包跟单

**端点:** `POST /api/wallet_configs/resume`

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包跟单已恢复",
  "data": {
    "wallet_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump"
  }
}
```

### 4.7 获取钱包余额

**端点:** `GET /api/wallet_balance`

**响应示例:**
```json
{
  "success": true,
  "data": {
    "sol_balance": 1.23456789,
    "tokens": {
      "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": {
        "amount": 1000.0,
        "decimals": 6,
        "symbol": "USDC",
        "last_price": 1.0
      },
      "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump": {
        "amount": 5000.0,
        "decimals": 9,
        "symbol": "PUMP",
        "last_price": 0.00123
      }
    }
  }
}
```

### 4.8 获取交易历史

**端点:** `GET /api/transaction_history`

**查询参数:**
- `limit`: (可选) 返回的交易数量，默认为10
- `offset`: (可选) 分页偏移量，默认为0
- `token`: (可选) 按代币地址筛选交易

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total": 25,
    "transactions": [
      {
        "signature": "4YPSijemh8jg2gG9Gg1j6SXEYegdb2cifoSGLuWuaghyb9uMDbnHZCb9ApbK7a2AJbF6GnwMqhpCv2iEZoy6V7Xp",
        "type": "buy",
        "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
        "amount": 1000.0,
        "price": 0.00123,
        "timestamp": "2023-06-15T08:45:22Z",
        "status": "成功",
        "profit_loss": null,
        "profit_loss_percentage": null,
        "sol_amount": 1.23,
        "formatted_amount": "1,000.00",
        "formatted_price": "0.001230"
      },
      {
        "signature": "3XPSijemh8jg2gG9Gg1j6SXEYegdb2cifoSGLuWuaghyb9uMDbnHZCb9ApbK7a2AJbF6GnwMqhpCv2iEZoy6V7Xy",
        "type": "sell",
        "token_address": "9XeY9hMjGhD2s34cm6b9J6F1Hpvno8PY2ZF5TcGNpump",
        "amount": 500.0,
        "price": 0.00135,
        "timestamp": "2023-06-16T10:22:45Z",
        "status": "成功",
        "profit_loss": 0.06,
        "profit_loss_percentage": 9.76,
        "sol_amount": 0.675,
        "formatted_amount": "500.00",
        "formatted_price": "0.001350"
      }
    ]
  }
}
```

## 5. 跟单控制API

### 5.1 暂停所有跟单

**端点:** `POST /api/follow/pause`

**描述:** 暂停系统的所有跟单操作。

**响应示例:**
```json
{
  "success": true,
  "message": "跟单已暂停"
}
```

### 5.2 恢复所有跟单

**端点:** `POST /api/follow/resume`

**描述:** 恢复系统的所有跟单操作。

**响应示例:**
```json
{
  "success": true,
  "message": "跟单已恢复"
}
```

### 5.3 获取跟单状态

**端点:** `GET /api/follow/status`

**描述:** 获取当前跟单状态。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "is_paused": false
  }
}
```

## 6. 钱包监控API

### 6.1 获取钱包监控配置

**端点:** `GET /api/wallet-monitor/config`

**描述:** 获取钱包监控的配置参数。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "consecutive_loss_threshold": 3,
    "loss_amount_threshold": 0.5,
    "auto_remove_wallet": true
  }
}
```

### 6.2 更新钱包监控配置

**端点:** `PUT /api/wallet-monitor/config`

**请求参数 (`application/json`):**
```json
{
  "consecutive_loss_threshold": 5,
  "loss_amount_threshold": 0.8,
  "auto_remove_wallet": true
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "钱包监控配置已更新"
}
```

### 6.3 获取所有钱包监控记录

**端点:** `GET /api/wallet-monitor/records`

**描述:** 获取所有被监控钱包的记录。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "WaLLeTAddReSS1111111111111111111111111111111": {
      "consecutive_losses": 2,
      "total_loss_amount": 0.45,
      "last_updated": "2023-06-16T10:22:45Z"
    },
    "WaLLeTAddReSS2222222222222222222222222222222": {
      "consecutive_losses": 0,
      "total_loss_amount": 0.0,
      "last_updated": "2023-06-15T08:45:22Z"
    }
  }
}
```

### 6.4 获取单个钱包监控记录

**端点:** `GET /api/wallet-monitor/records/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "data": {
    "consecutive_losses": 2,
    "total_loss_amount": 0.45,
    "last_updated": "2023-06-16T10:22:45Z"
  }
}
```

### 6.5 重置钱包监控记录

**端点:** `POST /api/wallet-monitor/records/{wallet_address}/reset`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "message": "钱包监控记录已重置"
}
```

### 6.6 删除钱包监控记录

**端点:** `DELETE /api/wallet-monitor/records/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "message": "钱包监控记录已删除"
}
```

## 7. 特殊钱包配置API

### 7.1 获取所有特殊钱包配置

**端点:** `GET /api/special-wallets`

**描述:** 获取所有特殊钱包的配置信息。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "WaLLeTAddReSS1111111111111111111111111111111": {
      "wallet_address": "WaLLeTAddReSS1111111111111111111111111111111",
      "slippage_percentage": 3.0,
      "tip_percentage": 2.0,
      "priority_fee_multiplier": 5.0,
      "compute_limit": 300000,
      "note": "高风险高回报钱包"
    },
    "WaLLeTAddReSS2222222222222222222222222222222": {
      "wallet_address": "WaLLeTAddReSS2222222222222222222222222222222",
      "slippage_percentage": 1.5,
      "tip_percentage": 1.0,
      "priority_fee_multiplier": 2.0,
      "compute_limit": 200000,
      "note": "稳健型钱包"
    }
  }
}
```

### 7.2 获取单个特殊钱包配置

**端点:** `GET /api/special-wallets/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "data": {
    "wallet_address": "WaLLeTAddReSS1111111111111111111111111111111",
    "slippage_percentage": 3.0,
    "tip_percentage": 2.0,
    "priority_fee_multiplier": 5.0,
    "compute_limit": 300000,
    "note": "高风险高回报钱包"
  }
}
```

### 7.3 添加特殊钱包配置

**端点:** `POST /api/special-wallets`

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "WaLLeTAddReSS3333333333333333333333333333333",
  "slippage_percentage": 2.5,
  "tip_percentage": 1.5,
  "priority_fee_multiplier": 3.0,
  "compute_limit": 250000,
  "note": "中风险钱包"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "特殊钱包配置已添加"
}
```

### 7.4 更新特殊钱包配置

**端点:** `PUT /api/special-wallets/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**请求参数 (`application/json`):**
```json
{
  "wallet_address": "WaLLeTAddReSS1111111111111111111111111111111",
  "slippage_percentage": 3.5,
  "tip_percentage": 2.5,
  "priority_fee_multiplier": 6.0,
  "compute_limit": 350000,
  "note": "更新：极高风险钱包"
}
```

**成功响应示例:**
```json
{
  "success": true,
  "message": "特殊钱包配置已更新"
}
```

### 7.5 删除特殊钱包配置

**端点:** `DELETE /api/special-wallets/{wallet_address}`

**路径参数:**
- `wallet_address`: 钱包地址

**响应示例:**
```json
{
  "success": true,
  "message": "特殊钱包配置已删除"
}
```

## 注意事项

1. 所有API接口返回的JSON格式统一包含`success`字段，表示操作是否成功。
2. 当操作成功时，`success`为`true`，通常会包含`data`字段提供具体数据。
3. 当操作失败时，`success`为`false`，通常会包含`error`字段提供错误信息。
4. 所有修改配置的接口会自动保存配置到对应的配置文件中，无需手动重启服务。
5. 参数验证失败会返回400错误，内部服务错误会返回500错误。
6. 钱包地址和代币地址必须是有效的Solana地址格式。
7. 部分特定参数（如跟单比例、滑点、优先费等）有数值范围限制，请参考各接口的参数说明。 