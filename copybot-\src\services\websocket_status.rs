// 临时禁用WebSocket状态推送服务
// 这是一个空实现，仅提供API所需的接口结构
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc::{self, Sender};
use anyhow::Result;

// 交易状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionStatus {
    Submitted,           // 已提交
    Processing,          // 处理中
    ConfirmingOnchain,   // 链上确认中
    Completed,           // 已完成
    Failed(String),      // 失败，带错误信息
}

// 交易状态更新消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionStatusUpdate {
    pub request_id: String,           // 请求ID
    pub transaction_type: String,     // 交易类型 (buy, sell)
    pub token_address: String,        // 代币地址
    pub status: TransactionStatus,    // 交易状态
    pub signatures: Option<Vec<String>>, // 交易签名
    pub amount: Option<f64>,          // 交易数量
    pub timestamp: String,            // 时间戳
    pub message: String,              // 消息
}

// 获取状态更新发送器 - 返回None表示服务未启用
pub fn get_status_sender() -> Option<Sender<TransactionStatusUpdate>> {
    None
}

// 创建交易状态更新 - 空实现
pub fn create_status_update(
    request_id: String,
    token_address: String,
    status: TransactionStatus,
    signatures: Option<Vec<String>>,
    amount: Option<f64>,
    message: String,
) -> TransactionStatusUpdate {
    TransactionStatusUpdate {
        request_id,
        transaction_type: "sell".to_string(),
        token_address,
        status,
        signatures,
        amount,
        timestamp: chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
        message,
    }
}

// 初始化WebSocket服务 - 返回空发送器
pub async fn init_websocket_service(_port: u16) -> Result<Sender<TransactionStatusUpdate>> {
    // 创建一个通道，但实际上不会被使用
    let (tx, _rx) = mpsc::channel(1);
    Ok(tx)
} 