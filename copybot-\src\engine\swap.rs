use crate::common::utils::{AppState, LatestBlockhash, HashWithTimestamp};
use crate::dex::pump_fun::{Pump, PumpCurveState};
use crate::dex::raydium::{Raydium, AmmInfo};
use crate::services::zero_slot::ZeroSlotService;
use anyhow::{Result, anyhow};
use clap::ValueEnum;
use serde::Deserialize;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use solana_sdk::transaction::Transaction;
use solana_sdk::signer::Signer;
use std::env;
use std::str::FromStr;
use solana_sdk::hash::Hash;
use std::time::{Instant, Duration, SystemTime, UNIX_EPOCH};
use crate::common::error_translator;
use solana_sdk::commitment_config::CommitmentConfig;
use tokio::time::sleep;
use std::collections::VecDeque;
use bincode;
use base64;
use crate::common::logger;
use std::sync::Arc;
use crate::services::special_wallet_config;

#[derive(Debug, Clone, Deserialize, PartialEq)]
pub enum SwapDirection {
    #[serde(rename = "buy")]
    Buy,
    #[serde(rename = "sell")]
    Sell,
}
impl From<SwapDirection> for u8 {
    fn from(value: SwapDirection) -> Self {
        match value {
            SwapDirection::Buy => 0,
            SwapDirection::Sell => 1,
        }
    }
}

// Implement FromStr for easier conversion
impl FromStr for SwapDirection {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "buy" => Ok(SwapDirection::Buy),
            "sell" => Ok(SwapDirection::Sell),
            _ => Err(anyhow!("无效的交易方向: {}", s)),
        }
    }
}

#[derive(ValueEnum, Debug, Clone, Deserialize)]
pub enum SwapInType {
    /// Quantity
    #[serde(rename = "qty")]
    Qty,
    /// Percentage
    #[serde(rename = "pct")]
    Pct,
}

pub async fn raydium_swap(
    state: AppState,
    amount_in: f64,
    swap_direction: &str,
    in_type: &str,
    slippage: u64,
    use_jito: bool,
    amm_pool_id: Pubkey,
    pool_state: AmmInfo,
) -> Result<Vec<String>> {
    let swap_direction = match swap_direction {
        "buy" => SwapDirection::Buy,
        "sell" => SwapDirection::Sell,
        _ => todo!(),
    };
    let in_type = match in_type {
        "qty" => SwapInType::Qty,
        "pct" => SwapInType::Pct,
        _ => todo!(),
    };
    let swapx = Raydium::new(state.rpc_nonblocking_client, state.rpc_client, state.wallet);
    let res = match swapx
        .swap(
            amount_in,
            swap_direction,
            in_type,
            slippage,
            use_jito,
            amm_pool_id,
            pool_state,
        )
        .await
    {
        Ok(res) => res,
        Err(e) => {
            return Err(e);
        }
    };
    Ok(res)
}

pub async fn pump_swap(
    state: Arc<AppState>,
    _amount_in: f64,
    swap_direction: &str,
    _in_type: &str,
    slippage_bps: u64,
    _use_jito: bool,
    mint: &str,
    _price_override: Option<f64>,
    original_compute_price: Option<u64>,
    token_amount_param_from_main: Option<u64>,
    limit_amount_param_from_main: Option<u64>,
    api_fixed_tip_sol: Option<f64>,
    api_tip_percentage: Option<f64>,
    transaction_originator: Option<String>,
) -> Result<Vec<String>> {
    let direction = SwapDirection::from_str(swap_direction)?;
    
    // 记录入口参数
    let logger = logger::Logger::new("PumpSwap".to_string());
    logger.log(format!(
        "【DEBUG-SWAP】进入pump_swap函数 - 方向: {:?}, 代币: {}, 滑点: {} BPS", 
        direction, mint, slippage_bps
    ));
    
    if let Some(token) = token_amount_param_from_main {
        logger.log(format!("【DEBUG-SWAP】收到token_amount_param: {}", token));
        if token == u64::MAX {
            logger.warn("【警告-SWAP】token_amount_param等于u64::MAX，这可能会导致交易失败");
        }
    } else {
        logger.warn("【警告-SWAP】未收到token_amount_param");
    }
    
    if let Some(limit) = limit_amount_param_from_main {
        logger.log(format!("【DEBUG-SWAP】收到limit_amount_param: {}", limit));
        if limit == u64::MAX {
            logger.warn("【警告-SWAP】limit_amount_param等于u64::MAX，这可能会导致交易失败");
        }
    } else {
        logger.warn("【警告-SWAP】未收到limit_amount_param");
    }

    // 使用缓存的Pump服务实例或创建新实例
    let swapx = match &state.pump_service {
        Some(cached_pump) => {
            // 使用缓存的实例
            let logger = logger::Logger::new("PumpFun".to_string());
            logger.log("使用已缓存的Pump服务实例".to_string());
            cached_pump.lock().await.clone()
        },
        None => {
            // 如果缓存中没有，则创建新实例（应急情况）
            let logger = logger::Logger::new("PumpFun".to_string());
            logger.log("警告：缓存中未找到Pump服务实例，创建新实例".to_string());
            Pump::new(
                state.rpc_nonblocking_client.clone(),
                state.rpc_client.clone(),
                state.wallet.clone(),
                state.wallet_cache.clone(),
            )
        }
    };
    let logger = swapx.logger.clone();

    let (token_amount_param, limit_amount_param) =
        match (token_amount_param_from_main, limit_amount_param_from_main) {
            (Some(token), Some(limit)) => {
                // 日志记录，不修改原始参数
                logger.log(format!(
                    "接收到计算的参数: direction={:?}, token_amount={}, limit_amount={} (derived from slippage {} BPS)",
                    direction,
                    token,
                    limit,
                    slippage_bps
                ));
                
                // 检查数值问题
                if token == u64::MAX {
                    logger.warn("【警告】token_amount_param是u64::MAX，这通常表示计算溢出");
                }
                
                if limit == u64::MAX {
                    logger.warn("【警告】limit_amount_param是u64::MAX，这通常表示计算溢出");
                }
                
                // 严格按照原代码，保持原有逻辑，只处理0值情况
                let token = if token == 0 { 1 } else { token };
                let limit = if limit == 0 { 1 } else { limit };
                
                logger.log(format!(
                    "【DEBUG】处理后的参数: token_amount={}, limit_amount={}",
                    token,
                    limit
                ));
                
                (token, limit)
            }
            _ => {
                logger.error("pump_swap Error: Missing required calculated parameters (token_amount/limit_amount) from main logic.");
                return Err(anyhow!("Internal error: Missing calculated swap parameters"));
            }
        };

    // 检查是否使用0slot
    let use_zero_slot = ZeroSlotService::is_enabled();
    if use_zero_slot {
        logger.log(format!("使用0slot服务进行交易提交，计算单元限制: {}", 
            if use_zero_slot { 250_000 } else { 300_000 }));
    }

    // 获取钱包地址用于日志
    let wallet_pubkey = state.wallet.pubkey().to_string();
    logger.log(format!("当前钱包地址: {}", wallet_pubkey));
    
    // 只查专用钱包配置，不查监控配置
    let mut compute_unit_limit: Option<u32> = None;
    let mut compute_unit_price_micro_lamports: Option<u64> = None;

    if let Some(special) = special_wallet_config::get_special_wallet_config(&wallet_pubkey) {
        logger.log("找到专用钱包配置:");
        compute_unit_limit = Some(special.compute_limit as u32);
        compute_unit_price_micro_lamports = Some((special.priority_fee_multiplier * 1000.0) as u64);
        logger.log(format!("专用配置-优先费: {:?}，计算单元: {:?}", compute_unit_price_micro_lamports, compute_unit_limit));
    }

    // 如果没找到，使用默认
    if compute_unit_limit.is_none() { compute_unit_limit = Some(70000); }
    if compute_unit_price_micro_lamports.is_none() { compute_unit_price_micro_lamports = Some(60000); }

    logger.log(format!("最终使用的计算单元限制: {:?}", compute_unit_limit));
    logger.log(format!("最终使用的优先费: {:?} 微lamports", compute_unit_price_micro_lamports));

    let instructions = match swapx.build_swap_instructions(
        direction.clone(),
        token_amount_param,
        limit_amount_param,
        mint,
        compute_unit_limit,
        compute_unit_price_micro_lamports,
        use_zero_slot,
        api_fixed_tip_sol,
        api_tip_percentage,
        transaction_originator,
    ).await {
        Ok(ixs) => {
            logger.log(format!("成功构建交易指令，包含优先费: {:?} 微lamports", compute_unit_price_micro_lamports));
            ixs
        },
        Err(e) => {
            logger.error(format!("Failed to build swap instructions for mint {}: {}", mint, e));
            return Err(e);
        }
    };

    let payer = state.wallet.pubkey();
    
    logger.log("========== CRITICAL TIMING SEQUENCE BEGINS ==========");
    
    let mut recent_blockhash_opt: Option<Hash> = None;
    let mut force_refresh_retries = 0;
    const MAX_RETRIES_FORCE_REFRESH: u32 = 3;
    const CACHE_STALE_THRESHOLD_MS: u128 = 1800;
    const BACKGROUND_TASK_INTERVAL_MS: u128 = 120;

    loop {
        let mut found_valid_hash = false;
        let mut needs_force_refresh = false;
        let mut refresh_reason = "";

        let cache_deque = state.latest_blockhash.read().await;

        if !cache_deque.is_empty() {
            for cached_data in cache_deque.iter() {
                let age = cached_data.timestamp.elapsed().as_millis();
                if age < CACHE_STALE_THRESHOLD_MS {
                    recent_blockhash_opt = Some(cached_data.hash);
                    found_valid_hash = true;
                    break;
                }
            }

            if !found_valid_hash {
                needs_force_refresh = true;
                refresh_reason = "所有缓存项过期";
            }
        } else {
            needs_force_refresh = true;
            refresh_reason = "缓存队列为空";
        }

        if found_valid_hash {
            break;
        }

        if needs_force_refresh {
            match state.rpc_client.get_latest_blockhash_with_commitment(CommitmentConfig::processed()) {
                Ok((hash, _)) => {
                    recent_blockhash_opt = Some(hash);
                    let new_blockhash_data = HashWithTimestamp::new(hash);

                    let mut cache_deque = state.latest_blockhash.write().await;
                    if cache_deque.front().map_or(true, |latest| latest.hash != hash) {
                        const CACHE_CAPACITY: usize = 3;
                        if cache_deque.len() >= CACHE_CAPACITY {
                            cache_deque.pop_back();
                        }
                        cache_deque.push_front(new_blockhash_data);
                    }
                    drop(cache_deque);

                    break;
                }
                Err(e) => {
                    logger.error(format!("强制刷新 blockhash 失败: {}. 重试...", e));
                    force_refresh_retries += 1;
                    if force_refresh_retries >= MAX_RETRIES_FORCE_REFRESH {
                        logger.error("强制刷新达到最大重试次数，无法获取 blockhash.");
                    }
                    if force_refresh_retries < MAX_RETRIES_FORCE_REFRESH {
                        sleep(Duration::from_millis(100)).await;
                    } else {
                        break;
                    }
                }
            }
        }
    }

    let recent_blockhash = match recent_blockhash_opt {
        Some(hash) => hash,
        None => {
            logger.error("循环结束后未能获取到有效的 Blockhash");
            return Err(anyhow!("未能获取 Blockhash，即使在强制刷新重试后"));
        }
    };

    let tx = Transaction::new_signed_with_payer(
        &instructions,
        Some(&payer),
        &[&*state.wallet],
        recent_blockhash,
    );
    
    if use_zero_slot {
        let zero_slot_service = ZeroSlotService::new(None);
            
        let zero_slot_result = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                zero_slot_service.submit_transaction(&tx).await
            })
        });
        
        match zero_slot_result {
            Ok(signature) => {
                // 零时槽服务内部已经记录了debug日志，这里不再重复记录
                // 只保留一行关键日志，用于业务逻辑跟踪
                logger.debug(format!("0slot交易处理结果：成功 - {}", signature));
                Ok(vec![signature])
            },
            Err(e) => {
                let error_message = e.to_string();
                logger.error(format!("0slot交易提交失败，详细错误: {}", e));
                
                if error_message.contains("403") || error_message.contains("expired") {
                    logger.error("0slot错误分析：API密钥已过期，请更新ZERO_SLOT_API_KEY环境变量");
                }
                
                return Err(anyhow!("0slot交易提交失败: {}", error_message));
            }
        }
    } else {
        logger.log("========== CRITICAL TIMING SEQUENCE ENDS ==========");
        
        // 执行交易，返回交易签名
        logger.log(format!("开始执行交易: mint={}, direction={:?}", mint, direction));
        let result = swapx.execute_swap_transaction(instructions, recent_blockhash, _use_jito, None).await;
        
        logger.log("========== CRITICAL TIMING SEQUENCE ENDS ==========");
        
        match result {
            Ok(signature) => {
                logger.log(format!("交易成功，签名: {}", signature));
                
                // 买入交易成功后，尝试获取最新价格并缓存
                if let SwapDirection::Buy = direction {
                    // 尝试获取并更新代币价格
                    match swapx.get_token_price(mint).await {
                        Ok(price_result) => {
                            logger.log(format!("成功获取代币价格: {} SOL，已更新缓存", price_result.simplified_price));
                        },
                        Err(e) => {
                            logger.warn(format!("买入成功但无法更新代币价格缓存: {}", e));
                        }
                    }
                }
                
                Ok(vec![signature])
            },
            Err(e) => {
                logger.error(format!("交易失败: {}", e));
                Err(e)
            }
        }
    }
}
