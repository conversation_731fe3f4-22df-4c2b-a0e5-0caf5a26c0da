use anyhow::{Context, Result};
use futures_util::StreamExt;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;
use tokio::time::sleep;

/// 启动Redis长连接监控
pub async fn start_redis_long_connection_monitor(redis_url: &str) -> Result<()> {
    println!("启动Redis长连接监控...");
    println!("连接到Redis: {}", redis_url);
    
    // 创建Redis客户端
    let client = redis::Client::open(redis_url)?;
    
    // 创建共享数据连接
    let shared_conn = match client.get_async_connection().await {
        Ok(conn) => Arc::new(Mutex::new(conn)),
        Err(e) => {
            eprintln!("无法连接到Redis: {:?}", e);
            return Err(anyhow::anyhow!("Redis连接失败"));
        }
    };
    
    // 创建专用心跳连接
    let heartbeat_conn = match client.get_async_connection().await {
        Ok(conn) => Arc::new(Mutex::new(conn)),
        Err(e) => {
            eprintln!("无法创建心跳连接: {:?}", e);
            return Err(anyhow::anyhow!("Redis心跳连接失败"));
        }
    };
    
    // 创建PubSub专用连接管理器
    let pubsub_conn_manager = Arc::new(Mutex::new(None::<redis::aio::Connection>));
    
    // 配置Redis键空间通知
    println!("配置Redis键空间通知...");
    {
        let mut conn = shared_conn.lock().await;
        redis::cmd("CONFIG")
            .arg("SET")
            .arg("notify-keyspace-events")
            .arg("KEA") // K=键空间事件, E=过期事件, A=所有命令
            .query_async::<_, ()>(&mut *conn)
            .await
            .context("配置Redis键空间通知失败")?;
    }
    
    // 统计信息
    let msg_counter = Arc::new(Mutex::new(0));
    let start_time = Arc::new(Mutex::new(Instant::now()));
    
    // 记录已处理的消息，避免重复处理
    let processed_keys = Arc::new(Mutex::new(std::collections::HashSet::new()));
    
    // 启动PubSub监听任务
    let client_clone = client.clone();
    let msg_counter_clone = Arc::clone(&msg_counter);
    let start_time_clone = Arc::clone(&start_time);
    let processed_keys_clone = Arc::clone(&processed_keys);
    let shared_conn_clone = Arc::clone(&shared_conn);
    let pubsub_conn_manager_clone = Arc::clone(&pubsub_conn_manager);
    
    let pubsub_handle = tokio::spawn(async move {
        // 确保键空间通知系统始终开启
        let mut retry_interval = tokio::time::interval(Duration::from_millis(100));
        let keyspace_pattern = "__keyspace@0__:*"; // 监听默认数据库中所有键的事件
        
        loop {
            retry_interval.tick().await;
            
            println!("正在设置键空间通知监听...");
            
            // 检查现有PubSub连接
            let mut need_new_connection = false;
            {
                let pubsub_conn_guard = pubsub_conn_manager_clone.lock().await;
                if pubsub_conn_guard.is_none() {
                    need_new_connection = true;
                }
            }
            
            // 仅在需要时创建新连接
            if need_new_connection {
                match client_clone.get_async_connection().await {
                    Ok(conn) => {
                        let mut pubsub_conn_guard = pubsub_conn_manager_clone.lock().await;
                        *pubsub_conn_guard = Some(conn);
                        println!("已创建新的PubSub连接");
                    },
                    Err(e) => {
                        println!("无法获取Redis PubSub连接: {:?}，100ms后重试", e);
                        continue;
                    }
                };
            }
            
            // 获取连接并转换为PubSub
            let conn_option = {
                let mut pubsub_conn_guard = pubsub_conn_manager_clone.lock().await;
                pubsub_conn_guard.take()
            };
            
            let pubsub_conn = match conn_option {
                Some(conn) => conn,
                None => {
                    println!("无法获取PubSub连接，100ms后重试");
                    continue;
                }
            };
            
            // 转换为PubSub对象
            let mut pubsub = pubsub_conn.into_pubsub();
            
            // 订阅键空间通知
            if let Err(e) = pubsub.psubscribe(keyspace_pattern).await {
                println!("订阅键空间通知失败: {:?}，100ms后重试", e);
                // 转换回普通连接并放回管理器
                let conn = pubsub.into_connection();
                let mut pubsub_conn_guard = pubsub_conn_manager_clone.lock().await;
                *pubsub_conn_guard = Some(conn);
                continue;
            }
            
            println!("===========================================");
            println!("★★★ 实时通知系统已激活 - 长连接模式 ★★★");
            println!("===========================================");
            
            // 获取消息流
            let mut pubsub_stream = pubsub.on_message();
            
            // 处理消息
            while let Some(msg) = pubsub_stream.next().await {
                // 获取通道名称
                let channel: String = match msg.get_channel() {
                    Ok(channel) => channel,
                    Err(e) => {
                        println!("获取通知频道失败: {:?}", e);
                        continue;
                    }
                };
                
                // 提取键名
                if let Some(key) = channel.strip_prefix("__keyspace@0__:") {
                    // 获取操作类型
                    let operation: String = match msg.get_payload() {
                        Ok(op) => op,
                        Err(e) => {
                            println!("获取操作类型失败: {:?}", e);
                            continue;
                        }
                    };
                    
                    // 只关注 "set" 操作，表示新建或更新键
                    if operation == "set" {
                        // 增加消息计数
                        {
                            let mut counter = msg_counter_clone.lock().await;
                            *counter += 1;
                            
                            // 每100条消息显示一次统计信息
                            if *counter % 100 == 0 {
                                let elapsed = start_time_clone.lock().await.elapsed();
                                println!("===========================================");
                                println!("已接收 {} 条消息，运行时间: {:?}", *counter, elapsed);
                                println!("平均每秒 {:.2} 条消息", *counter as f64 / elapsed.as_secs_f64());
                                println!("===========================================");
                            }
                        }
                        
                        // 检查是否已处理过此键
                        let is_new_key = {
                            let mut processed = processed_keys_clone.lock().await;
                            if !processed.contains(key) {
                                processed.insert(key.to_string());
                                
                                // 如果已处理键过多，清理一部分
                                if processed.len() > 10000 {
                                    // 这里简单地清除所有键，实际应用中可能需要更复杂的策略
                                    processed.clear();
                                    println!("已清理处理过的键记录");
                                }
                                
                                true
                            } else {
                                false
                            }
                        };
                        
                        // 对于新键，获取并处理数据
                        if is_new_key {
                            let current_time = SystemTime::now()
                                .duration_since(UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs();
                                
                            println!("★ 实时捕获 [{}]: {} 键被设置，准备处理", 
                                current_time, key);
                                
                            // 使用共享连接获取键值，而不是创建新连接
                            let mut conn = shared_conn_clone.lock().await;
                            match redis::cmd("GET").arg(key).query_async::<_, Option<String>>(&mut *conn).await {
                                Ok(Some(value)) => {
                                    // 打印值的前100个字符
                                    let preview = if value.len() > 100 {
                                        format!("{}... (共{} 字节)", &value[..100], value.len())
                                    } else {
                                        value.clone()
                                    };
                                    
                                    println!("★ 键 {} 的值: {}", key, preview);
                                    
                                    // 获取数据后释放锁，然后处理数据
                                    let value_clone = value.clone();
                                    drop(conn); // 主动释放连接锁
                                    
                                    // 这里可以添加您的具体业务处理逻辑
                                    process_data(key, &value_clone).await;
                                },
                                Ok(None) => println!("键 {} 的值不存在或已被删除", key),
                                Err(e) => println!("获取键 {} 的值失败: {:?}", key, e),
                            }
                        }
                    }
                }
            }
            
            // 如果流已关闭，转换回普通连接并重置
            println!("⚠ 键空间通知流已中断，立即重新连接...");
            // PubSub流结束时，重置连接管理器状态
            let mut pubsub_conn_guard = pubsub_conn_manager_clone.lock().await;
            *pubsub_conn_guard = None;
        }
    });
    
    // 启动心跳检测任务 - 使用专用心跳连接
    let heartbeat_conn_clone = Arc::clone(&heartbeat_conn);
    let client_clone = client.clone();
    let heartbeat_handle = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(30));
        
        loop {
            interval.tick().await;
            
            // 使用专用心跳连接发送PING命令
            let mut conn_guard = heartbeat_conn_clone.lock().await;
            match redis::cmd("PING").query_async::<_, String>(&mut *conn_guard).await {
                Ok(response) => println!("❤ Redis连接心跳正常 - {} - 响应: {}", 
                    SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                    response),
                Err(e) => {
                    println!("⚠ Redis连接心跳异常: {:?}，尝试重连", e);
                    // 尝试重新建立心跳连接
                    match client_clone.get_async_connection().await {
                        Ok(new_conn) => {
                            *conn_guard = new_conn;
                            println!("✓ 心跳连接已重新建立");
                        },
                        Err(e) => println!("✗ 重新建立心跳连接失败: {:?}", e)
                    }
                }
            }
        }
    });
    
    // 等待任务完成（实际上不会完成，除非发生错误）
    tokio::select! {
        result = pubsub_handle => {
            if let Err(e) = result {
                println!("PubSub任务异常退出: {:?}", e);
            }
        }
        result = heartbeat_handle => {
            if let Err(e) = result {
                println!("心跳任务异常退出: {:?}", e);
            }
        }
    }
    
    println!("Redis长连接监控已停止");
    Ok(())
}

/// 处理获取的数据
async fn process_data(key: &str, value: &str) {
    // 这里可以添加您的业务逻辑处理
    println!("正在处理键 {} 的数据，长度: {} 字节", key, value.len());
    
    // 模拟处理耗时
    sleep(Duration::from_millis(10)).await;
    
    println!("键 {} 的数据处理完成", key);
}

/// 主函数示例
#[tokio::main]
async fn main() -> Result<()> {
    // 设置Redis URL
    let redis_url = "redis://127.0.0.1:6379";
    
    // 启动长连接监控
    start_redis_long_connection_monitor(redis_url).await?;
    
    Ok(())
} 