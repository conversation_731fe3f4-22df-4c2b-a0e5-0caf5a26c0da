#!/usr/bin/env python3
"""
Solana代币ATA账户销毁脚本 - 快速批量回收租金
"""

from solana.rpc.api import Client
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.types import TokenAccountOpts
from solders.transaction import VersionedTransaction
from solders.instruction import Instruction, AccountMeta
from solders.message import MessageV0
from solders.hash import Hash
import base58
import json
import time

# 配置
RPC_URL = "https://api.mainnet-beta.solana.com"  # 主网
RETRY_DELAY = 1  # 请求间隔时间(秒)

# SPL Token程序ID
TOKEN_PROGRAM_ID = Pubkey.from_string("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")

# 初始化客户端
client = Client(RPC_URL)

# 加载您的私钥
private_key = "5JrjbXAyTfbVYg1MHnDZymNgnvLWnUm8vZn6hhXnofUQNo5MXYyKniq3eES3rPcHXP3bwoHhtA3CBTt2SLKf7Qw2"
keypair_bytes = base58.b58decode(private_key)
# 使用from_seed方法而不是from_bytes
keypair = Keypair.from_seed(keypair_bytes[:32])

# 获取公钥
wallet_address = keypair.pubkey()
print(f"钱包地址: {wallet_address}")

# 休眠函数，避免请求过快
def sleep_between_requests():
    time.sleep(RETRY_DELAY)

# 创建关闭账户指令的函数
def create_close_account_instruction(token_account, destination, owner):
    # 关闭账户的指令ID为9
    command = 9
    
    # 创建关闭账户指令
    keys = [
        AccountMeta(pubkey=token_account, is_signer=False, is_writable=True),
        AccountMeta(pubkey=destination, is_signer=False, is_writable=True),
        AccountMeta(pubkey=owner, is_signer=True, is_writable=False),
    ]
    data = bytes([command, 0, 0, 0])  # 使用bytes表示命令
    
    return Instruction(program_id=TOKEN_PROGRAM_ID, accounts=keys, data=data)

try:
    # 快速获取所有代币账户并直接处理
    token_program_filter = TokenAccountOpts(program_id=TOKEN_PROGRAM_ID)
    response = client.get_token_accounts_by_owner(wallet_address, token_program_filter)
    token_accounts = response.value
    account_pubkeys = [account.pubkey for account in token_accounts]
    
    print(f"找到 {len(account_pubkeys)} 个代币账户，准备直接处理")
    
    # 允许用户选择继续或退出
    confirmation = input("是否继续销毁所有账户? (y/n): ")
    if confirmation.lower() != 'y':
        print("操作已取消")
        exit(0)
    
    # 将账户分成多个批次处理以避免交易大小限制
    MAX_ACCOUNTS_PER_TX = 2  # 每个交易最多处理2个账户
    
    # 将账户分组
    def chunks(lst, n):
        """将列表分成n个元素一组"""
        for i in range(0, len(lst), n):
            yield lst[i:i + n]
    
    # 分批处理所有账户
    account_batches = list(chunks(account_pubkeys, MAX_ACCOUNTS_PER_TX))
    print(f"账户将被分成 {len(account_batches)} 个批次处理")
    
    # 批量处理
    total_closed = 0
    for batch_idx, account_batch in enumerate(account_batches):
        print(f"处理批次 {batch_idx+1}/{len(account_batches)}, 包含 {len(account_batch)} 个账户")
        
        try:
            # 创建当前批次的关闭账户指令
            instructions = []
            
            for account_pubkey in account_batch:
                instruction = create_close_account_instruction(
                    account_pubkey,
                    wallet_address,  # 租金返回到主钱包
                    wallet_address   # 授权人
                )
                instructions.append(instruction)
            
            # 获取最近的区块哈希
            blockhash_response = client.get_latest_blockhash()
            blockhash = blockhash_response.value.blockhash
            
            # 使用MessageV0创建消息 (VersionedTransaction)
            msg = MessageV0.try_compile(
                payer=wallet_address,
                instructions=instructions,
                address_lookup_table_accounts=[],
                recent_blockhash=blockhash
            )
            
            # 使用VersionedTransaction创建交易
            tx = VersionedTransaction(msg, [keypair])
            
            # 发送交易
            tx_resp = client.send_transaction(tx)
            tx_sig = tx_resp.value
            
            print(f"✓ 交易已发送: {tx_sig}")
            total_closed += len(account_batch)
            
        except Exception as e:
            print(f"✗ 交易失败: {e}")
        
        # 批次间短暂等待
        time.sleep(0.5)
    
    print(f"\n操作完成! 发送了 {total_closed}/{len(account_pubkeys)} 个账户的关闭交易")
    print(f"预计回收租金: 约 {len(account_pubkeys) * 0.00204:.6f} SOL")
    
except KeyboardInterrupt:
    print("\n程序被用户中断")
except Exception as e:
    print(f"\n发生错误: {e}")

print("\n操作完成!")