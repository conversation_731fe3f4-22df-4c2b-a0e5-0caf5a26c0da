use anyhow::Result;
use copy_trading_bot::services::hash_monitor::{HashMonitor, HashMonitorConfig};

/// 测试获取最新区块哈希并存储在Redis
#[tokio::test]
async fn test_hash_monitor() -> Result<()> {
    println!("===== 开始测试哈希监控服务 =====");
    
    // 创建默认配置的监控实例
    let hash_monitor = HashMonitor::new().await?;
    
    println!("哈希监控服务已创建，按Ctrl+C终止测试...");
    
    // 启动监控服务
    hash_monitor.start_monitoring().await?;
    
    Ok(())
}

// 方便直接运行
#[tokio::main]
async fn main() -> Result<()> {
    test_hash_monitor().await
} 