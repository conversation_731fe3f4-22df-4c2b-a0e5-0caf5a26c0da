use anyhow::Result;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{signature::Signature, transaction::Transaction};
use std::sync::Arc;
use std::time::Duration;
use reqwest;
use serde_json::{json, Value};
use base64;
use std::env;

// 发送交易并等待确认
pub fn send_txn(client: &Arc<RpcClient>, txn: &Transaction, wait_confirm: bool) -> Result<Signature> {
    // 发送交易
    let signature = client.send_transaction_with_config(
        txn,
        solana_client::rpc_config::RpcSendTransactionConfig {
            skip_preflight: false,
            preflight_commitment: Some(solana_sdk::commitment_config::CommitmentLevel::Processed),
            encoding: None,
            max_retries: Some(3),
            min_context_slot: None,
        },
    )?;

    // 如果需要等待确认
    if wait_confirm {
        // 使用可用的confirm_transaction方法
        let status = client.confirm_transaction(&signature)?;
        if !status {
            println!("交易未确认: {}", signature);
        }

        // 如果需要超时功能，可以手动实现
        // 例如, 最多等待60秒
        let start = std::time::Instant::now();
        let timeout = Duration::from_secs(60);
        let mut confirmed = status;

        while !confirmed && start.elapsed() < timeout {
            std::thread::sleep(Duration::from_millis(500));
            confirmed = client.confirm_transaction(&signature)?;
        }

        if !confirmed {
            println!("交易确认超时: {}", signature);
        }
    }

    Ok(signature)
}

/// 0slot客户端结构体
pub struct ZeroSlotClient {
    api_key: String,
    endpoint: String,
    client: reqwest::Client,
}

impl ZeroSlotClient {
    /// 创建新的0slot客户端
    pub fn new(api_key: &str, endpoint: Option<String>) -> Self {
        // 如果没有提供端点，则使用固定的ny1.0slot.trade端点
        let endpoint = Some("http://ny1.0slot.trade".to_string()).unwrap_or_else(|| "http://ny1.0slot.trade".to_string());
        
        // 创建带超时设置的HTTP客户端
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());
        
        Self {
            api_key: api_key.to_string(),
            endpoint,
            client,
        }
    }
    
    /// 从环境变量获取配置并创建客户端
    pub fn from_env() -> Result<Self> {
        let api_key = env::var("ZERO_SLOT_API_KEY")
            .map_err(|_| anyhow::anyhow!("环境变量ZERO_SLOT_API_KEY未设置"))?;
        
        let endpoint = env::var("ZERO_SLOT_ENDPOINT").ok();
        
        Ok(Self::new(&api_key, endpoint))
    }
    
    /// 发送交易到0slot服务
    pub async fn send_transaction(&self, txn: &Transaction) -> Result<String> {
        // 序列化交易为base64
        let serialized_tx = bincode::serialize(txn)?;
        let tx_base64 = base64::encode(&serialized_tx);
        
        // 构建请求体
        let request_body = json!({
            "jsonrpc": "2.0",
            "id": format!("{}", uuid::Uuid::new_v4()),
            "method": "sendTransaction",
            "params": [
                tx_base64,
                {
                    "encoding": "base64"
                }
            ]
        });
        
        // 构建URL（包含API密钥）
        let url = format!("{}?api-key={}", self.endpoint, self.api_key);
        
        // 发送请求
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;
        
        // 处理响应
        let response_status = response.status();
        let response_text = response.text().await?;
        
        if !response_status.is_success() {
            return Err(anyhow::anyhow!(
                "0slot服务请求失败: 状态码 {}，响应: {}", 
                response_status, response_text
            ));
        }
        
        // 解析JSON响应
        let response_json: Value = serde_json::from_str(&response_text)?;
        
        // 检查错误
        if let Some(error) = response_json.get("error") {
            let error_code = error.get("code").and_then(|c| c.as_i64()).unwrap_or(0);
            let error_message = error.get("message").and_then(|m| m.as_str()).unwrap_or("未知错误");
            
            return Err(anyhow::anyhow!(
                "0slot服务返回错误: 代码 {}，消息: {}", 
                error_code, error_message
            ));
        }
        
        // 提取交易签名
        let signature = response_json
            .get("result")
            .ok_or_else(|| anyhow::anyhow!("响应中没有result字段"))?
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("交易签名不是字符串"))?
            .to_string();
        
        Ok(signature)
    }
    
    /// 测试不同服务器节点的速度
    pub async fn test_server_speeds() -> Result<Vec<(String, Duration)>> {
        let servers = vec![
            "https://ny.0slot.trade".to_string(),
            "https://de.0slot.trade".to_string(),
            "https://ams.0slot.trade".to_string(),
        ];
        
        let client = reqwest::Client::new();
        let mut results = Vec::new();
        
        for server in servers {
            let start_time = std::time::Instant::now();
            
            // 发送简单请求测试响应时间
            match client.get(&server).timeout(Duration::from_secs(5)).send().await {
                Ok(_) => {
                    let elapsed = start_time.elapsed();
                    results.push((server, elapsed));
                }
                Err(_) => {
                    // 如果请求失败，添加最大持续时间
                    results.push((server, Duration::from_secs(5)));
                }
            }
        }
        
        // 按响应时间排序
        results.sort_by(|a, b| a.1.cmp(&b.1));
        
        Ok(results)
    }
    
    /// 获取最快的服务器节点
    pub async fn get_fastest_server() -> Result<String> {
        let speeds = Self::test_server_speeds().await?;
        
        if speeds.is_empty() {
            return Err(anyhow::anyhow!("无法获取任何服务器的速度信息"));
        }
        
        // 返回最快的服务器
        Ok(speeds[0].0.clone())
    }
} 