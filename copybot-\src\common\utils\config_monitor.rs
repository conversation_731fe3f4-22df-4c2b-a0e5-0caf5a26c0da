use std::path::{Path, PathBuf};
use std::sync::Arc;
use notify::{Watcher, RecursiveMode, Config, Event, EventKind, recommended_watcher};
use log::{info, debug, error, warn};
use tokio::sync::RwLock;
use std::fs;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use anyhow::{Result, anyhow};

/// 钱包配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletConfig {
    pub follow_percentage: f64,
    pub slippage_percentage: f64,
    pub tip_percentage: f64,
    pub min_price_multiplier: f64,
    pub max_price_multiplier: f64,
    #[serde(default)]
    pub priority_fee: Option<u64>,
    #[serde(default)]
    pub compute_unit_limit: Option<u32>,
    #[serde(default)]
    pub note: Option<String>,
}

/// 监控地址配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MonitorAddresses {
    pub sol_address: String,
    pub unwanted_key: String,
    pub targets: Vec<String>,
    #[serde(default)]
    pub wallets: HashMap<String, WalletConfig>,
}

/// 全局配置缓存
lazy_static::lazy_static! {
    pub static ref MONITOR_CONFIG: Arc<RwLock<MonitorAddresses>> = Arc::new(RwLock::new(MonitorAddresses {
        sol_address: "So11111111111111111111111111111111111111112".to_string(),
        unwanted_key: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN".to_string(),
        targets: Vec::new(),
        wallets: HashMap::new(),
    }));
    
    pub static ref CONFIG_LAST_UPDATED: Arc<RwLock<SystemTime>> = Arc::new(RwLock::new(SystemTime::now()));
    
    // 新增：用于HTTP接口的专用内存列表，与文件I/O完全解耦
    pub static ref MONITOR_TARGETS: Arc<RwLock<MonitorAddresses>> = Arc::new(RwLock::new(MonitorAddresses {
        sol_address: "So11111111111111111111111111111111111111112".to_string(),
        unwanted_key: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN".to_string(), 
        targets: Vec::new(),
        wallets: HashMap::new(),
    }));
}

/// 获取监控地址配置文件路径
pub fn get_monitor_config_path() -> PathBuf {
    // 首先尝试当前工作目录
    let current_dir_path = PathBuf::from("monitor_addrs.json");
    if current_dir_path.exists() {
        return current_dir_path;
    }
    
    // 其次尝试获取可执行文件所在目录
    match std::env::current_exe() {
        Ok(exe_path) => {
            if let Some(exe_dir) = exe_path.parent() {
                let exe_dir_path = exe_dir.join("monitor_addrs.json");
                if exe_dir_path.exists() {
                    return exe_dir_path;
                }
            }
        },
        Err(e) => {
            error!("无法获取可执行文件路径: {}", e);
        }
    }
    
    // 如果以上路径都不存在，返回当前目录下的配置文件路径
    PathBuf::from("monitor_addrs.json")
}

/// 读取监控地址配置文件
pub async fn read_monitor_addresses() -> Result<MonitorAddresses> {
    // 获取配置文件路径
    let config_path = get_monitor_config_path();
    
    info!("读取配置文件: {}", config_path.display());
    
    // 如果配置文件不存在，创建默认配置
    if !config_path.exists() {
        let default_config = MonitorAddresses {
            sol_address: "So11111111111111111111111111111111111111112".to_string(),
            unwanted_key: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN".to_string(),
            targets: vec!["在这里填写要监控的钱包地址".to_string()],
            wallets: HashMap::new(),
        };
        
        // 创建JSON字符串
        let config_json = serde_json::to_string_pretty(&default_config)?;
        
        // 写入文件
        fs::write(&config_path, config_json)?;
        
        info!("已创建默认监控地址配置文件: {}", config_path.display());
        warn!("请编辑该文件，添加您要监控的钱包地址");
    }
    
    // 读取文件内容
    let content = fs::read_to_string(&config_path)?;
    
    // 解析JSON
    let monitor_addrs: MonitorAddresses = serde_json::from_str(&content)?;
    
    // 验证配置
    if monitor_addrs.sol_address.is_empty() {
        return Err(anyhow!("SOL地址不能为空"));
    }
    
    if monitor_addrs.unwanted_key.is_empty() {
        return Err(anyhow!("排除地址不能为空"));
    }
    
    Ok(monitor_addrs)
}

/// 保存监控地址配置文件
pub async fn save_monitor_addresses(config: &MonitorAddresses) -> Result<()> {
    // 获取配置文件路径
    let config_path = get_monitor_config_path();
    
    // 创建JSON字符串
    let config_json = serde_json::to_string_pretty(config)?;
    
    // 写入文件
    fs::write(&config_path, config_json)?;
    
    info!("配置文件已保存: {}", config_path.display());
    
    Ok(())
}

/// 获取当前缓存的监控地址配置
pub async fn get_monitor_addresses() -> MonitorAddresses {
    MONITOR_CONFIG.read().await.clone()
}

/// 获取专用于HTTP接口的监控地址列表（与文件I/O完全解耦）
pub async fn get_cached_monitor_addresses() -> MonitorAddresses {
    MONITOR_TARGETS.read().await.clone()
}

/// 更新缓存的监控地址配置
pub async fn update_monitor_addresses(config: MonitorAddresses) -> Result<()> {
    // 保存到文件
    save_monitor_addresses(&config).await?;
    
    // 更新缓存
    *MONITOR_CONFIG.write().await = config.clone();
    // 同时更新HTTP专用缓存
    *MONITOR_TARGETS.write().await = config;
    *CONFIG_LAST_UPDATED.write().await = SystemTime::now();
    
    info!("监控地址配置已更新");
    
    Ok(())
}

/// 初始化配置监控服务
pub async fn init_config_monitor() -> Result<()> {
    // 读取初始配置
    match read_monitor_addresses().await {
        Ok(config) => {
            let config_clone = config.clone();
            *MONITOR_CONFIG.write().await = config;
            // 同时初始化HTTP专用缓存
            *MONITOR_TARGETS.write().await = config_clone;
            *CONFIG_LAST_UPDATED.write().await = SystemTime::now();
            info!("初始配置已加载");
        }
        Err(e) => {
            error!("读取初始配置失败: {}", e);
            // 不再返回错误，而是创建默认配置
            let default_config = MonitorAddresses {
                sol_address: "So11111111111111111111111111111111111111112".to_string(),
                unwanted_key: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN".to_string(),
                targets: Vec::new(), // 空的目标列表
                wallets: HashMap::new(), // 空的钱包映射
            };
            
            // 更新内存中的配置
            *MONITOR_CONFIG.write().await = default_config.clone();
            // 同时更新HTTP专用缓存
            *MONITOR_TARGETS.write().await = default_config.clone();
            *CONFIG_LAST_UPDATED.write().await = SystemTime::now();
            
            // 保存到文件
            if let Err(save_err) = save_monitor_addresses(&default_config).await {
                error!("保存默认配置失败: {}", save_err);
                // 这里不返回错误，因为我们希望程序继续运行
            } else {
                info!("已创建空的默认配置，可以通过API添加监控地址");
            }
        }
    }
    
    // 获取配置文件路径
    let config_path = get_monitor_config_path();
    
    // 创建一个配置，启用去抖动功能
    let config = Config::default()
        .with_poll_interval(std::time::Duration::from_millis(500)); // 添加500毫秒的去抖动时间
    
    // 启动监控任务
    tokio::spawn(async move {
        // 创建一个通道来接收文件变更事件
        let (tx, rx) = std::sync::mpsc::channel();
        
        // 创建带配置的事件处理器
        let event_handler = move |res: Result<Event, notify::Error>| {
            match res {
                Ok(event) => {
                    if let Err(e) = tx.send(event) {
                        error!("发送事件错误: {}", e);
                    }
                }
                Err(e) => error!("监控错误: {}", e),
            }
        };
        
        // 创建文件监控器
        let mut watcher = match recommended_watcher(event_handler) {
            Ok(w) => w,
            Err(e) => {
                error!("创建文件监控器失败: {}", e);
                return;
            }
        };
        
        // 应用配置
        if let Err(e) = watcher.configure(config) {
            error!("配置文件监控器失败: {}", e);
            // 不返回，继续尝试使用默认配置监控
        }
        
        // 开始监控配置文件
        if let Err(e) = watcher.watch(Path::new(&config_path), RecursiveMode::NonRecursive) {
            error!("监控配置文件失败: {}", e);
            return;
        }
        
        info!("配置文件监控服务已启动，监控文件: {}", config_path.display());
        
        // 用于跟踪上次处理事件的时间，实现去抖动
        let mut last_processed = SystemTime::now();
        let debounce_duration = std::time::Duration::from_millis(500);
        
        // 处理文件变更事件
        while let Ok(event) = rx.recv() {
            // 检查是否为监控文件的事件
            let is_target_file = event.paths.iter().any(|path| {
                path.file_name()
                    .and_then(|name| name.to_str())
                    .map(|name| name == "monitor_addrs.json")
                    .unwrap_or(false)
            });
            
            if is_target_file {
                match event.kind {
                    EventKind::Create(_) | EventKind::Modify(_) => {
                        // 检查是否需要去抖动
                        let now = SystemTime::now();
                        if now.duration_since(last_processed).unwrap_or_default() < debounce_duration {
                            // 如果距离上次处理的时间小于去抖动时间，跳过此事件
                            continue;
                        }
                        
                        // 更新最后处理时间
                        last_processed = now;
                        
                        // 配置文件被创建或修改，重新加载
                        info!("检测到配置文件变更，异步重新加载");
                        
                        // 使用tokio::spawn异步更新，不阻塞监控线程
                        tokio::spawn(async move {
                            // 等待一小段时间，确保文件写入完成
                            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                            
                            match read_monitor_addresses().await {
                                Ok(config) => {
                                    // 更新主配置缓存
                                    *MONITOR_CONFIG.write().await = config.clone();
                                    // 同时更新HTTP专用缓存
                                    *MONITOR_TARGETS.write().await = config;
                                    *CONFIG_LAST_UPDATED.write().await = SystemTime::now();
                                    info!("配置已重新加载并更新内存缓存");
                                }
                                Err(e) => {
                                    error!("重新加载配置失败: {}", e);
                                }
                            }
                        });
                    }
                    _ => {}
                }
            }
        }
        
        error!("配置文件监控服务已停止");
    });
    
    Ok(())
} 