use anyhow::Result;
use futures_util::StreamExt;
use std::time::Instant;
use regex::Regex;
use chrono::{DateTime, Local, TimeZone};

#[tokio::main]
async fn main() -> Result<()> {
    let redis_url = "redis://127.0.0.1:6379";
    println!("连接到 Redis: {}", redis_url);
    
    let client = redis::Client::open(redis_url)?;
    
    // 连接到Redis
    let mut conn = match client.get_async_connection().await {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("无法连接到Redis: {:?}", e);
            return Err(anyhow::anyhow!("Redis连接失败"));
        }
    };
    
    // 配置Redis键空间通知
    println!("配置Redis键空间通知...");
    redis::cmd("CONFIG")
        .arg("SET")
        .arg("notify-keyspace-events")
        .arg("KEA") // K=键空间事件, E=过期事件, A=所有命令
        .query_async::<_, ()>(&mut conn)
        .await?;
    
    // 创建PubSub连接
    let pubsub_conn = client.get_async_connection().await?;
    let mut pubsub = pubsub_conn.into_pubsub();
    
    // 订阅所有键空间事件
    let pattern = "__keyspace@*__:*";
    println!("订阅模式: {}", pattern);
    pubsub.psubscribe(pattern).await?;
    
    println!("===========================================");
    println!("★★★ 交易监控已启动 - 显示关键交易信息 ★★★");
    println!("===========================================");
    
    // 预编译正则表达式用于提取交易信息和识别数据类型
    let signer_regex = Regex::new(r"签名者地址:\s*([^\s\r\n]+)").unwrap();
    let mint_regex = Regex::new(r"MINT:\s*([^\s\r\n]+)").unwrap();
    let price_regex = Regex::new(r"当前价格:\s*([\d\.e\-\+]+)(?:\s*SOL)?").unwrap();
    let token_amount_regex = Regex::new(r"TOKEN AMOUNT:\s*(\d+)").unwrap();
    let tx_type_regex = Regex::new(r"TYPE:\s*(Buy|Sell)").unwrap();
    let sol_cost_regex = Regex::new(r"SOL COST:\s*([\d\.]+)\s*SOL").unwrap();
    let min_sol_output_regex = Regex::new(r"MIN SOL OUTPUT:\s*([\d\.]+)\s*SOL").unwrap();
    let time_regex = Regex::new(r"TIME:\s*([\d\-]+T[\d:\.]+\+[\d:]+)").unwrap();
    
    // 用于识别数据类型的正则表达式
    let bonding_curve_regex = Regex::new(r"ACCOUNT TYPE:\s*BondingCurve").unwrap();
    
    // 目标钱包地址
    let target_wallet = "********************************************";
    println!("特别关注钱包地址: {}", target_wallet);
    
    let mut stream = pubsub.on_message();
    let start_time = Instant::now();
    let mut tx_count = 0;
    
    // 接收并分析所有消息
    while let Some(msg) = stream.next().await {
        // 获取通道名称 (格式: __keyspace@0__:KEY)
        let channel: String = match msg.get_channel() {
            Ok(ch) => ch,
            Err(e) => {
                println!("获取通道名称失败: {:?}", e);
                continue;
            }
        };
        
        // 获取操作类型 (如 "set", "del" 等)
        let operation: String = match msg.get_payload() {
            Ok(op) => op,
            Err(e) => {
                println!("获取操作类型失败: {:?}", e);
                continue;
            }
        };
        
        // 提取实际的键名
        let key = if let Some(key_part) = channel.split(':').nth(1) {
            key_part
        } else {
            &channel
        };
        
        // 只关注 "set" 操作的键，并且键名看起来像交易签名
        if operation == "set" && key.len() > 20 && !key.contains(":") {
            // 尝试获取键的值
            let mut value_conn = match client.get_async_connection().await {
                Ok(conn) => conn,
                Err(e) => {
                    println!("获取值连接失败: {:?}", e);
                    continue;
                }
            };
            
            match redis::cmd("GET").arg(key).query_async::<_, Option<String>>(&mut value_conn).await {
                Ok(Some(value)) => {
                    // 判断数据类型
                    let is_transaction = tx_type_regex.is_match(&value);
                    let is_bonding_curve_only = bonding_curve_regex.is_match(&value) && !is_transaction;
                    
                    // 只处理交易数据，忽略纯曲线数据
                    if is_transaction {
                        // 提取关键信息
                        let tx_type = tx_type_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let signer = signer_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let mint = mint_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let token_amount = token_amount_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let price = price_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let sol_cost = sol_cost_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        let min_sol_output = min_sol_output_regex.captures(&value)
                            .and_then(|cap| cap.get(1))
                            .map(|m| m.as_str())
                            .unwrap_or("未知");
                        
                        // 获取交易时间和延迟信息
                        let now = Local::now();
                        let mut tx_time_str = "未知";
                        let mut delay_ms = 0;
                        
                        if let Some(time_cap) = time_regex.captures(&value) {
                            if let Some(time_str) = time_cap.get(1).map(|m| m.as_str()) {
                                tx_time_str = time_str;
                                // 解析交易时间
                                if let Ok(tx_time) = DateTime::parse_from_str(time_str, "%Y-%m-%dT%H:%M:%S%.3f%z") {
                                    // 计算延迟（毫秒）
                                    let tx_time_local = tx_time.with_timezone(&Local);
                                    delay_ms = (now - tx_time_local).num_milliseconds();
                                }
                            }
                        }
                        
                        // 交易计数增加
                        tx_count += 1;
                        
                        // 打印提取出的交易信息
                        let is_target = signer == target_wallet;
                        let prefix = if is_target { "🔴🔴🔴 " } else { "" };
                        
                        println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                        println!("{}交易 #{} | 接收时间: {} | 类型: {}", 
                            prefix,
                            tx_count, 
                            now.format("%H:%M:%S.%3f"),
                            tx_type
                        );
                        println!("{}签名: {}", prefix, key);
                        println!("{}签名者地址: {}", prefix, signer);
                        println!("{}MINT地址: {}", prefix, mint);
                        println!("{}代币数量: {}", prefix, token_amount);
                        
                        // 价格可能只存在于完整键值数据中
                        if price != "未知" {
                            println!("{}价格: {}", prefix, price);
                        }
                        
                        // 显示交易时间和延迟信息
                        println!("{}交易时间: {}", prefix, tx_time_str);
                        if delay_ms > 0 {
                            println!("{}系统延迟: {} 毫秒", prefix, delay_ms);
                        }
                        
                        println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                    } else if is_bonding_curve_only {
                        // 对于纯曲线数据，只显示简单的更新信息，不计入交易
                        println!("曲线账户更新: {}", key);
                    }
                },
                Ok(None) => {},
                Err(e) => println!("获取键 {} 的值失败: {:?}", key, e),
            }
        }
    }
    
    println!("监控已停止");
    Ok(())
} 