{"version": "0.1.0", "name": "pump", "instructions": [{"name": "initialize", "accounts": [{"name": "global", "isMut": true, "isSigner": false}, {"name": "user", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "feeInSol", "type": "u64"}, {"name": "feeBasisPoints", "type": "u16"}, {"name": "createFeeInSol", "type": "u64"}, {"name": "accountStorageFeeInSol", "type": "u64"}, {"name": "nTokenTypes", "type": "u16"}, {"name": "createFeeDistributeBasisPoints", "type": "u16"}], "discriminator": [123, 69, 28, 5, 36, 99, 104, 147]}, {"name": "buy", "docs": ["购买绑定曲线上的代币。"], "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": false}, {"name": "bondingCurve", "isMut": true, "isSigner": false}, {"name": "user", "isMut": true, "isSigner": true}, {"name": "userTokenAccount", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}, {"name": "maxSolCost", "type": "u64"}], "discriminator": [102, 6, 61, 18, 1, 218, 235, 234]}, {"name": "sell", "docs": ["将代币卖回绑定曲线。"], "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": false}, {"name": "bondingCurve", "isMut": true, "isSigner": false}, {"name": "user", "isMut": true, "isSigner": true}, {"name": "userTokenAccount", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}, {"name": "minSolOutput", "type": "u64"}], "discriminator": [51, 230, 133, 164, 1, 127, 131, 173]}]}