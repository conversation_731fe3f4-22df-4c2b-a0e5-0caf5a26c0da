# Docker一键部署指南

本指南将帮助你使用Docker和Docker Compose一键部署Solana交易复制机器人。

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)

## 部署步骤

### 1. 准备配置文件

首先，确保你有所需的配置文件：

```bash
# 创建配置目录
mkdir -p config data/tx_history
```

将你的`monitor_addrs.json`配置文件放入`config`目录。如果还没有配置文件，可以创建一个基本的配置：

```json
{
  "sol_address": "So11111111111111111111111111111111111111112",
  "unwanted_key": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",
  "targets": ["你要监控的钱包地址"],
  "wallets": {
    "你要监控的钱包地址": {
      "follow_percentage": 100,
      "slippage_percentage": 5,
      "tip_percentage": 5,
      "min_price_multiplier": 0.1,
      "max_price_multiplier": 0.2,
      "priority_fee": 50000,
      "compute_unit_limit": 200000
    }
  }
}
```

### 2. 使用Docker Compose构建和启动服务

在项目根目录下执行以下命令：

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 3. 访问API

服务启动后，API将在以下地址可用：

- API地址: http://localhost:3000
- 健康检查: http://localhost:3000/health

## 常见问题解决

### 服务无法启动

检查日志以获取更多信息：

```bash
docker-compose logs app
```

### 更新配置文件

修改`config`目录中的配置文件后，重启服务：

```bash
docker-compose restart app
```

### 完全重建服务

如果需要完全重建服务：

```bash
docker-compose down
docker-compose up -d --build
```

### 数据持久化

所有交易历史数据将存储在`data/tx_history`目录中，Redis数据将存储在Docker卷中，即使容器重启也不会丢失。

## 更多资源

- Docker文档: https://docs.docker.com/
- Docker Compose文档: https://docs.docker.com/compose/ 