use anyhow::Result;
use futures_util::StreamExt;
use std::time::{Instant, Duration};

#[tokio::main]
async fn main() -> Result<()> {
    let redis_url = "redis://127.0.0.1:6379";
    println!("连接到 Redis: {}", redis_url);
    
    let client = redis::Client::open(redis_url)?;
    
    // 连接到Redis
    let mut conn = match client.get_async_connection().await {
        Ok(conn) => conn,
        Err(e) => {
            eprintln!("无法连接到Redis: {:?}", e);
            return Err(anyhow::anyhow!("Redis连接失败"));
        }
    };
    
    // 配置Redis键空间通知
    println!("配置Redis键空间通知...");
    redis::cmd("CONFIG")
        .arg("SET")
        .arg("notify-keyspace-events")
        .arg("KEA") // K=键空间事件, E=过期事件, A=所有命令
        .query_async::<_, ()>(&mut conn)
        .await?;
    
    // 创建PubSub连接
    let pubsub_conn = client.get_async_connection().await?;
    let mut pubsub = pubsub_conn.into_pubsub();
    
    // 订阅所有键空间事件
    let pattern = "__keyspace@*__:*";
    println!("订阅模式: {}", pattern);
    pubsub.psubscribe(pattern).await?;
    
    println!("===========================================");
    println!("★★★ 实时监控已启动 - 输出所有Redis键通知 ★★★");
    println!("===========================================");
    
    let mut stream = pubsub.on_message();
    let start_time = Instant::now();
    let mut msg_count = 0;
    
    // 接收并显示所有消息
    while let Some(msg) = stream.next().await {
        msg_count += 1;
        
        // 获取通道名称 (格式: __keyspace@0__:KEY)
        let channel: String = match msg.get_channel() {
            Ok(ch) => ch,
            Err(e) => {
                println!("获取通道名称失败: {:?}", e);
                continue;
            }
        };
        
        // 获取操作类型 (如 "set", "del" 等)
        let operation: String = match msg.get_payload() {
            Ok(op) => op,
            Err(e) => {
                println!("获取操作类型失败: {:?}", e);
                continue;
            }
        };
        
        // 提取实际的键名
        let key = if let Some(key_part) = channel.split(':').nth(1) {
            key_part
        } else {
            &channel
        };
        
        // 计算运行时间
        let elapsed = start_time.elapsed();
        
        // 显示收到的通知
        println!(
            "[{}] 捕获到键空间事件 - 键: {} | 操作: {} | 运行时间: {:?} | 消息计数: {}",
            chrono::Local::now().format("%H:%M:%S.%3f"),
            key,
            operation,
            elapsed,
            msg_count
        );
        
        // 如果是 "set" 操作，尝试获取键的值
        if operation == "set" {
            let mut value_conn = client.get_async_connection().await.unwrap_or_else(|e| {
                println!("获取值连接失败: {:?}", e);
                panic!("无法创建Redis连接");
            });
            
            match redis::cmd("GET").arg(key).query_async::<_, Option<String>>(&mut value_conn).await {
                Ok(Some(value)) => {
                    // 如果值太长，只显示前100个字符
                    let display_value = if value.len() > 100 {
                        format!("{}... (长度: {})", &value[..100], value.len())
                    } else {
                        value
                    };
                    println!("键 {} 的值: {}", key, display_value);
                },
                Ok(None) => println!("键 {} 没有值或已被删除", key),
                Err(e) => println!("获取键 {} 的值失败: {:?}", key, e),
            }
        }
        
        // 每100条消息显示一次统计信息
        if msg_count % 100 == 0 {
            println!("===========================================");
            println!("已接收 {} 条消息，运行时间: {:?}", msg_count, elapsed);
            println!("平均每秒 {:.2} 条消息", msg_count as f64 / elapsed.as_secs_f64());
            println!("===========================================");
        }
    }
    
    println!("监控已停止");
    Ok(())
} 