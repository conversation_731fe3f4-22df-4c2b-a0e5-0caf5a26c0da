use std::{future::Future, str::FromStr, sync::{Arc, LazyLock}, time::Duration};

use anyhow::{anyhow, Result};
use indicatif::{ProgressBar, ProgressStyle};
use rand::{seq::IteratorRandom, thread_rng};
use serde::Deserialize;
use serde_json::Value;
use solana_sdk::pubkey::Pubkey;
use tokio::{
    sync::RwLock,
    time::{sleep, Instant},
};

use crate::common::utils::import_env_var;
use crate::common::logger::Logger;

// 导入多节点管理模块
use crate::services::jito_multi_node::{JitoNodeManager, JITO_NODES};

// 全局节点管理器
pub static NODE_MANAGER: LazyLock<Arc<JitoNodeManager>> = 
    LazyLock::new(|| {
        let manager = Arc::new(JitoNodeManager::new());
        // 不在静态初始化时启动健康检查，改为在程序启动时手动初始化
        manager
    });

// 从环境变量读取指定区域，如果没有，则使用动态选择
pub static PREFERRED_REGION: LazyLock<Option<String>> = LazyLock::new(|| {
    std::env::var("JITO_PREFERRED_REGION").ok()
});

// 指示是否启用动态节点选择
pub static ENABLE_DYNAMIC_NODE_SELECTION: LazyLock<bool> = LazyLock::new(|| {
    std::env::var("JITO_ENABLE_DYNAMIC_NODE")
        .map(|v| v.to_lowercase() == "true")
        .unwrap_or(true)
});

pub static BLOCK_ENGINE_URL: LazyLock<String> =
    LazyLock::new(|| match std::env::var("JITO_BLOCK_ENGINE_URL") {
        Ok(url) => url,
        Err(_) => "https://frankfurt.mainnet.block-engine.jito.wtf".to_string(),
    });

pub static TIP_STREAM_URL: LazyLock<String> =
    LazyLock::new(|| import_env_var("JITO_TIP_STREAM_URL"));
pub static TIP_PERCENTILE: LazyLock<String> =
    LazyLock::new(|| import_env_var("JITO_TIP_PERCENTILE"));

pub static TIP_ACCOUNTS: LazyLock<RwLock<Vec<String>>> = LazyLock::new(|| RwLock::new(vec![]));

#[derive(Debug)]
pub struct TipAccountResult {
    pub accounts: Vec<String>,
}

pub async fn init_tip_accounts() -> Result<()> {
    let accounts = TipAccountResult {
        accounts: vec![
            "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL".to_string(),
            "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5".to_string(),
            "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh".to_string(),
            "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49".to_string(),
            "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY".to_string(),
        ],
    };
    let mut tip_accounts = TIP_ACCOUNTS.write().await;
    tip_accounts.clear();
    
    accounts
        .accounts
        .iter()
        .for_each(|account| tip_accounts.push(account.to_string()));
    Ok(())
}

pub async fn get_tip_account() -> Result<Pubkey> {
    let accounts = TIP_ACCOUNTS.read().await;
    if accounts.is_empty() {
        if let Err(e) = init_tip_accounts().await {
            return Err(anyhow!("初始化tip账户失败: {:?}", e));
        }
        
        let accounts = TIP_ACCOUNTS.read().await;
        if accounts.is_empty() {
            return Err(anyhow!("没有可用的tip账户"));
        }
        
        let mut rng = thread_rng();
        match accounts.iter().choose(&mut rng) {
            Some(acc) => Ok(Pubkey::from_str(acc).inspect_err(|err| {
                println!("jito: failed to parse Pubkey: {:?}", err);
            })?),
            None => Err(anyhow!("jito: no tip accounts available")),
        }
    } else {
        let mut rng = thread_rng();
        match accounts.iter().choose(&mut rng) {
            Some(acc) => Ok(Pubkey::from_str(acc).inspect_err(|err| {
                println!("jito: failed to parse Pubkey: {:?}", err);
            })?),
            None => Err(anyhow!("jito: no tip accounts available")),
        }
    }
}

// 获取当前最适合的Block Engine URL
pub async fn get_best_block_engine_url() -> Result<String> {
    let logger = Logger::new("JitoService".to_string());

    // 如果禁用了动态节点选择，则使用静态配置
    if !*ENABLE_DYNAMIC_NODE_SELECTION {
        return Ok(BLOCK_ENGINE_URL.to_string());
    }

    // 使用新版本的节点选择方法
    let preferred_region = PREFERRED_REGION.as_ref().map(|s| s.as_str());
    let node = NODE_MANAGER.get_preferred_node(preferred_region);
    
    logger.log(format!("选择节点: {}", node.name));
    Ok(node.url)
}

// 获取Bundle API URL (现在使用最佳节点)
pub async fn get_bundle_api_url_async() -> Result<String> {
    let url = get_best_block_engine_url().await?;
    Ok(format!("{}/api/v1/bundles", url))
}

// 为了兼容性保留的同步版本
pub fn get_bundle_api_url() -> String {
    format!("{}/api/v1/bundles", *BLOCK_ENGINE_URL)
}

// 获取交易API URL
pub async fn get_transactions_api_url() -> Result<String> {
    let url = get_best_block_engine_url().await?;
    Ok(format!("{}/api/v1/transactions", url))
}

pub async fn get_tip_value() -> Result<f64> {
    if let Ok(tip_value) = std::env::var("JITO_TIP_VALUE") {
        match f64::from_str(&tip_value) {
            Ok(value) => Ok(value),
            Err(_) => {
                println!(
                    "Invalid JITO_TIP_VALUE in environment variable: '{}'. Falling back to percentile calculation.",
                    tip_value
                );
                Err(anyhow!("Invalid TIP_VALUE in environment variable"))
            }
        }
    } else {
        Err(anyhow!("JITO_TIP_VALUE environment variable not set"))
    }
}

#[derive(Deserialize, Debug)]
pub struct BundleStatus {
    pub bundle_id: String,
    pub transactions: Vec<String>,
    pub slot: Option<u64>,
    pub confirmation_status: Option<String>,
    pub err: Option<ErrorStatus>,
}

#[derive(Deserialize, Debug)]
pub struct ErrorStatus {
    #[serde(rename = "Ok")]
    pub ok: Option<()>,
}

pub async fn wait_for_bundle_confirmation<F, Fut>(
    fetch_statuses: F,
    bundle_id: String,
    interval: Duration,
    timeout: Duration,
) -> Result<Vec<String>>
where
    F: Fn(String) -> Fut,
    Fut: Future<Output = Result<Vec<Value>>>,
{
    let progress_bar = new_progress_bar();
    let start_time = Instant::now();

    loop {
        let statuses = fetch_statuses(bundle_id.clone()).await?;

        if let Some(status) = statuses.first() {
            let bundle_status_result: Result<BundleStatus, _> = serde_json::from_value(status.clone());
            
            match bundle_status_result {
                Ok(bundle_status) => {
                    println!("Bundle状态: {:?}", bundle_status);
                    
                    if let Some(status) = &bundle_status.confirmation_status {
                        match status.as_str() {
                            "finalized" | "confirmed" => {
                                progress_bar.finish_and_clear();
                                println!(
                                    "Bundle已确认 {}: {}",
                                    bundle_id, status
                                );
                                bundle_status
                                    .transactions
                                    .iter()
                                    .for_each(|tx| println!("交易链接: https://solscan.io/tx/{}", tx));
                                return Ok(bundle_status.transactions);
                            }
                            _ => {
                                progress_bar.set_message(format!(
                                    "正在等待Bundle确认 {}: {}",
                                    bundle_id, status
                                ));
                            }
                        }
                    } else {
                        progress_bar.set_message(format!("等待Bundle确认 {}: 状态未知", bundle_id));
                    }
                },
                Err(err) => {
                    println!("解析Bundle状态失败: {}, 原始数据: {:?}", err, status);
                    progress_bar.set_message(format!("无法解析Bundle状态 {}", bundle_id));
                }
            }
        } else {
            progress_bar.set_message(format!("未找到Bundle状态 {}", bundle_id));
        }

        if start_time.elapsed() > timeout {
            println!("等待超过 {:?}，退出等待", timeout);
            return Err(anyhow!("获取Bundle状态超时"));
        }

        sleep(interval).await;
    }
}

pub fn new_progress_bar() -> ProgressBar {
    let progress_bar = ProgressBar::new(42);
    progress_bar.set_style(
        ProgressStyle::default_spinner()
            .template("{spinner:.green} {wide_msg}")
            .expect("ProgressStyle::template direct input to be correct"),
    );
    progress_bar.enable_steady_tick(Duration::from_millis(100));
    progress_bar
}

// 获取所有可用的Jito节点区域
pub fn get_available_regions() -> Vec<String> {
    NODE_MANAGER.get_available_regions()
}

// 初始化节点管理器的健康检查
pub async fn init_node_manager() {
    // 访问 LazyLock 以确保初始化
    let _ = &*NODE_MANAGER;
    // 不需要显式调用健康检查启动
}

// 初始化Jito服务
pub async fn init_jito_service() -> Result<()> {
    let logger = Logger::new("JitoService".to_string());
    logger.log("开始初始化Jito服务...".to_string());

    // 初始化Tip账户
    if let Err(e) = init_tip_accounts().await {
        logger.error(format!("初始化Tip账户失败: {:?}", e));
        return Err(e); 
    }
    logger.log("Tip账户初始化成功".to_string());

    // 如果启用了动态节点选择，则初始化节点管理器（不启动健康检查）
    if *ENABLE_DYNAMIC_NODE_SELECTION {
        init_node_manager().await;
        logger.log("Jito节点管理器已初始化 (健康检查未启动)".to_string());
        // 移除健康检查任务的启动
        /*
        let manager_clone = NODE_MANAGER.clone();
        let runtime_handle = tokio::runtime::Handle::current();
        Arc::clone(&manager_clone).start_health_check_task(
            runtime_handle
        );
        */
    } else {
        logger.log("动态Jito节点选择已禁用".to_string());
    }

    logger.log("Jito服务初始化完成".to_string());
    Ok(())
}
