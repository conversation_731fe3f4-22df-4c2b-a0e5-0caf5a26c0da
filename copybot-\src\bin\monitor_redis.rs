use anyhow::Result;
use std::sync::Arc;
use copy_trading_bot::new_follow_mode::{MonitorConfig, start_redis_monitor_loop};

#[tokio::main]
async fn main() -> Result<()> {
    // 创建监控配置，特定监控地址 14UvX6hczd5m76XQyzMs9FxDy8xuwB2NTUC53tdvi5uN
    let config = MonitorConfig {
        monitored_addresses: Arc::new(vec![
            "14UvX6hczd5m76XQyzMs9FxDy8xuwB2NTUC53tdvi5uN".to_string()
        ]),
    };

    // 创建Redis客户端 - 修改连接字符串以确保匹配
    let redis_client = redis::Client::open("redis://127.0.0.1:6379")?;
    
    println!("开始运行新的Redis交易监控模式");
    println!("监控特定钱包: FNwFGfz3ZvRpmP1kGdNWXmPrZEV135VHwqhrckMd7Us2");
    
    // 启动监控循环
    start_redis_monitor_loop(redis_client, config).await?;
    
    Ok(())
} 