# Cursor规则使用指南

本项目已配置了Cursor编辑器的特定规则和设置，以提升Rust/Solana开发体验。

## 已配置的文件

1. `.cursorignore` - 定义了哪些文件和目录应被Cursor索引排除
2. `.cursor/settings.json` - Cursor编辑器的全局设置
3. `.cursor/rules/rust_rules.json` - Rust代码质量规则
4. `.cursor/snippets/rust.json` - 代码片段模板

## 索引排除规则

我们在`.cursorignore`中配置了以下规则：
- 排除构建目录和编译产物
- 排除大型二进制文件
- 排除日志和环境变量文件（已允许.env文件索引）
- 排除IDE配置文件

## 代码规则

在`.cursor/rules/rust_rules.json`中定义了以下规则：
- 文件大小限制（1500行以内）
- 导入顺序规范
- 错误处理最佳实践
- 函数大小限制
- Solana依赖版本一致性检查
- 测试文件位置规范（必须放在tests文件夹）
- 买卖逻辑错误修复建议（参考tests中的测试文件）

## 测试规范

- **所有测试文件必须放在tests文件夹中**
- 测试文件应以`_test.rs`结尾，例如`feature_test.rs`
- 处理买卖逻辑错误时，应优先参考`tests/real_buy_transaction_test.rs`和`tests/real_sell_transaction_test.rs`文件
- 创建新测试时，可使用代码片段中的测试模板（`newtest`、`buytest`、`selltest`）

## 代码片段

在`.cursor/snippets/rust.json`中提供了以下常用代码片段：
- `result` - 创建返回Result的函数
- `asyncfn` - 创建异步函数
- `solcpi` - Solana程序调用模板
- `splacc` - SPL Token关联账户创建
- `raydium` - Raydium交易模板
- `moddef` - 模块定义
- `structdef` - 结构体定义与实现
- `newtest` - 创建新测试文件模板
- `buytest` - 买交易测试模板（参考real_buy_transaction_test.rs）
- `selltest` - 卖交易测试模板（参考real_sell_transaction_test.rs）

## 使用方法

1. 在编辑Rust文件时，输入片段前缀（如`asyncfn`）并按Tab键，自动插入代码模板
2. 代码规则会自动检查，并在违反规则时在编辑器中显示警告/建议
3. 文件索引会排除配置的无关文件，提高编辑器性能
4. 开发买卖功能时，请使用`buytest`或`selltest`片段，并参考tests文件夹中的现有测试

## 自定义

如需自定义这些规则和设置，可以直接编辑相应的配置文件。 