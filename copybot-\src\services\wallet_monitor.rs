use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::RwLock;
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use log::{info, warn, error, debug};
use std::fs;
use std::env;

use crate::common::logger::Logger;
use crate::common::utils::config_monitor;

/// 钱包亏损记录配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletMonitorConfig {
    /// 连续亏损次数阈值，达到此值会触发警告
    pub consecutive_loss_threshold: u32,
    /// 亏损金额阈值（SOL），达到此值会触发警告
    pub loss_amount_threshold: f64,
    /// 是否自动删除达到阈值的钱包配置
    pub auto_remove_wallet: bool,
}

impl Default for WalletMonitorConfig {
    fn default() -> Self {
        Self {
            consecutive_loss_threshold: 2,   // 默认连续2次亏损触发
            loss_amount_threshold: 0.5,      // 默认亏损0.5 SOL触发
            auto_remove_wallet: false,       // 默认不自动删除
        }
    }
}

/// 钱包监控记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletMonitorRecord {
    /// 钱包地址
    pub wallet_address: String,
    /// 连续亏损次数
    pub consecutive_losses: u32,
    /// 连续亏损总金额（SOL）
    pub total_loss_amount: f64,
    /// 上次交易签名
    pub last_transaction_signature: Option<String>,
    /// 监控开始时间
    pub monitoring_since: String,
    /// 上次更新时间
    pub last_updated: String,
    /// 上次警告时间
    pub last_warning: Option<String>,
    /// 已通知（避免重复通知）
    pub notified: bool,
}

/// 钱包监控记录集合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletMonitorRecords {
    /// 监控配置
    pub config: WalletMonitorConfig,
    /// 钱包监控记录
    pub wallets: HashMap<String, WalletMonitorRecord>,
}

impl Default for WalletMonitorRecords {
    fn default() -> Self {
        Self {
            config: WalletMonitorConfig::default(),
            wallets: HashMap::new(),
        }
    }
}

/// 钱包监控服务
#[derive(Clone)]
pub struct WalletMonitor {
    records: Arc<RwLock<WalletMonitorRecords>>,
    records_path: PathBuf,
    logger: Logger,
}

impl WalletMonitor {
    /// 创建新的钱包监控服务
    pub fn new() -> Self {
        let records_path = PathBuf::from(
            env::var("WALLET_MONITOR_PATH").unwrap_or_else(|_| "data/wallet_monitor.json".to_string())
        );
        
        let logger = Logger::new("WalletMonitor".to_string());
        logger.log("初始化钱包监控服务".to_string());
        
        let monitor = Self {
            records: Arc::new(RwLock::new(WalletMonitorRecords::default())),
            records_path,
            logger,
        };
        
        monitor
    }
    
    /// 初始化，加载现有记录
    pub async fn init(&self) -> Result<()> {
        // 尝试加载现有记录
        if self.records_path.exists() {
            match fs::read_to_string(&self.records_path) {
                Ok(content) => {
                    match serde_json::from_str::<WalletMonitorRecords>(&content) {
                        Ok(records) => {
                            let mut guard = self.records.write().await;
                            *guard = records;
                            self.logger.log(format!("已加载钱包监控记录: {} 个钱包", guard.wallets.len()));
                            return Ok(());
                        },
                        Err(e) => {
                            self.logger.error(format!("解析钱包监控记录失败: {}", e));
                            // 继续使用默认配置
                        }
                    }
                },
                Err(e) => {
                    self.logger.error(format!("读取钱包监控记录文件失败: {}", e));
                    // 继续使用默认配置
                }
            }
        }
        
        // 使用默认配置
        self.save_records().await?;
        self.logger.log("已创建默认钱包监控配置".to_string());
        
        Ok(())
    }
    
    /// 保存监控记录
    async fn save_records(&self) -> Result<()> {
        let records = self.records.read().await;
        
        // 确保目录存在
        if let Some(parent) = self.records_path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)?;
            }
        }
        
        // 序列化并保存
        let content = serde_json::to_string_pretty(&*records)?;
        fs::write(&self.records_path, content)?;
        
        Ok(())
    }
    
    /// 更新钱包监控配置
    pub async fn update_config(&self, config: WalletMonitorConfig) -> Result<()> {
        let mut records = self.records.write().await;
        records.config = config;
        drop(records);
        
        self.save_records().await?;
        self.logger.log("已更新钱包监控配置".to_string());
        
        Ok(())
    }
    
    /// 获取钱包监控配置
    pub async fn get_config(&self) -> WalletMonitorConfig {
        let records = self.records.read().await;
        records.config.clone()
    }
    
    /// 记录卖出交易并检查是否达到亏损阈值
    pub async fn record_sell_transaction(
        &self, 
        wallet_address: &str, 
        signature: &str, 
        profit_loss: f64
    ) -> Result<(bool, Option<String>)> {
        let now = chrono::Utc::now().to_rfc3339();
        let mut records = self.records.write().await;
        let config = records.config.clone();
        
        // 获取或创建钱包记录
        let record = records.wallets.entry(wallet_address.to_string()).or_insert_with(|| {
            WalletMonitorRecord {
                wallet_address: wallet_address.to_string(),
                consecutive_losses: 0,
                total_loss_amount: 0.0,
                last_transaction_signature: None,
                monitoring_since: now.clone(),
                last_updated: now.clone(),
                last_warning: None,
                notified: false,
            }
        });
        
        // 更新交易记录
        record.last_transaction_signature = Some(signature.to_string());
        record.last_updated = now.clone();
        
        let mut should_warn = false;
        let mut warning_message = None;
        
        if profit_loss < 0.0 {
            // 亏损交易
            record.consecutive_losses += 1;
            record.total_loss_amount += profit_loss.abs();
            self.logger.log(format!(
                "钱包 {} 卖出交易亏损: {:.5} SOL, 连续亏损: {}, 总亏损: {:.5} SOL",
                wallet_address, profit_loss.abs(), record.consecutive_losses, record.total_loss_amount
            ));
            
            // 检查是否达到阈值
            if record.consecutive_losses >= config.consecutive_loss_threshold &&
               record.total_loss_amount >= config.loss_amount_threshold &&
               !record.notified {
                
                should_warn = true;
                record.notified = true;
                record.last_warning = Some(now.clone());
                
                let message = format!(
                    "钱包 {} 已达到亏损阈值: 连续亏损 {} 次，总亏损 {:.5} SOL",
                    wallet_address, record.consecutive_losses, record.total_loss_amount
                );
                warning_message = Some(message.clone());
                self.logger.warn(message);
                
                // 如果配置了自动删除
                if config.auto_remove_wallet {
                    // 删除监控地址配置
                    match self.remove_wallet_from_config(wallet_address).await {
                        Ok(_) => {
                            self.logger.log(format!("已自动删除钱包配置: {}", wallet_address));
                        },
                        Err(e) => {
                            self.logger.error(format!("自动删除钱包配置失败: {}", e));
                        }
                    }
                }
            }
        } else {
            // 盈利交易，重置连续亏损计数
            self.logger.log(format!(
                "钱包 {} 卖出交易盈利: {:.5} SOL，重置连续亏损计数",
                wallet_address, profit_loss
            ));
            record.consecutive_losses = 0;
            record.total_loss_amount = 0.0;
            record.notified = false;
        }
        
        // 保存记录
        drop(records);
        self.save_records().await?;
        
        Ok((should_warn, warning_message))
    }
    
    /// 从配置中删除钱包
    async fn remove_wallet_from_config(&self, wallet_address: &str) -> Result<()> {
        let mut monitor_config = config_monitor::get_monitor_addresses().await;
        
        // 从目标列表中移除
        monitor_config.targets.retain(|addr| addr != wallet_address);
        
        // 从钱包配置中移除
        monitor_config.wallets.remove(wallet_address);
        
        // 更新配置
        config_monitor::update_monitor_addresses(monitor_config).await
            .map_err(|e| anyhow!("更新监控地址配置失败: {}", e))?;
        
        self.logger.log(format!("已从配置中删除钱包: {}", wallet_address));
        
        Ok(())
    }
    
    /// 获取所有钱包监控记录
    pub async fn get_all_records(&self) -> HashMap<String, WalletMonitorRecord> {
        let records = self.records.read().await;
        records.wallets.clone()
    }
    
    /// 获取钱包监控记录
    pub async fn get_wallet_record(&self, wallet_address: &str) -> Option<WalletMonitorRecord> {
        let records = self.records.read().await;
        records.wallets.get(wallet_address).cloned()
    }
    
    /// 重置钱包监控记录
    pub async fn reset_wallet_record(&self, wallet_address: &str) -> Result<()> {
        let now = chrono::Utc::now().to_rfc3339();
        let mut records = self.records.write().await;
        
        if let Some(record) = records.wallets.get_mut(wallet_address) {
            record.consecutive_losses = 0;
            record.total_loss_amount = 0.0;
            record.last_updated = now;
            record.notified = false;
            
            self.logger.log(format!("已重置钱包 {} 的监控记录", wallet_address));
        } else {
            return Err(anyhow!("找不到钱包 {} 的监控记录", wallet_address));
        }
        
        drop(records);
        self.save_records().await?;
        
        Ok(())
    }
    
    /// 删除钱包监控记录
    pub async fn delete_wallet_record(&self, wallet_address: &str) -> Result<()> {
        let mut records = self.records.write().await;
        
        if records.wallets.remove(wallet_address).is_some() {
            self.logger.log(format!("已删除钱包 {} 的监控记录", wallet_address));
        } else {
            return Err(anyhow!("找不到钱包 {} 的监控记录", wallet_address));
        }
        
        drop(records);
        self.save_records().await?;
        
        Ok(())
    }
} 