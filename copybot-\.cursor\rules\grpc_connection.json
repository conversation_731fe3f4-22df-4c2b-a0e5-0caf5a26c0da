{"name": "gRPC连接配置", "description": "Yellowstone gRPC连接和安全设置", "connection": {"endpoint": "solana-yellowstone-grpc.publicnode.com:443", "use_tls": true, "timeout_ms": 30000, "reconnect_policy": {"enabled": true, "max_attempts": 5, "backoff_strategy": "exponential", "initial_delay_ms": 500, "max_delay_ms": 10000}}, "subscription": {"commitment_level": "confirmed", "account_filters": {"include_token_program": true, "include_system_program": false}, "transaction_filters": {"include_votes": false, "include_failed": false}}, "payload_handling": {"max_batch_size": 100, "process_interval_ms": 500, "deduplicate": true}, "security": {"validate_certificates": true, "use_domain_verification": true, "allow_insecure_fallback": false}}