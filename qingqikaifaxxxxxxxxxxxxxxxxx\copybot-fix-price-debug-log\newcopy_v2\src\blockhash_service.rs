use anyhow::{anyhow, Result};
use arc_swap::ArcSwap;
use futures_util::{SinkExt, StreamExt};
use solana_sdk::hash::Hash;
use std::{
    collections::{HashMap, VecDeque},
    str::FromStr,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::time::sleep;
use tonic::transport::ClientTlsConfig;
use tracing::{debug, error, info, warn};
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::prelude::{
    subscribe_update::UpdateOneof, CommitmentLevel, SubscribeRequest,
    SubscribeRequestFilterBlocksMeta,
};

/// 存储最新的区块哈希信息
#[derive(Clone, Debug)]
pub struct BlockhashData {
    /// 区块哈希
    pub hash: Hash,
    /// 获取到哈希的时间
    pub timestamp: Instant,
    /// 哈希对应的slot
    pub slot: u64,
}

/// 区块哈希管理器，用于通过gRPC从Geyser插件获取并缓存最新的区块哈希
#[derive(Debug, Clone)]
pub struct BlockhashService {
    /// 内部状态，使用ArcSwap以支持无锁读
    internal: Arc<ArcSwap<VecDeque<BlockhashData>>>,
}

impl Default for BlockhashService {
    fn default() -> Self {
        Self::new()
    }
}

impl BlockhashService {
    /// 创建一个新的BlockhashManager实例
    pub fn new() -> Self {
        Self {
            internal: Arc::new(ArcSwap::new(Arc::new(VecDeque::with_capacity(30)))),
        }
    }

    /// 启动后台任务，持续从gRPC端点获取区块哈希
    pub async fn start(&self, endpoints: Vec<String>) {
        let internal_clone = self.internal.clone();
        tokio::spawn(async move {
            loop {
    
                for endpoint in &endpoints {
        
                    let result = Self::run_subscription_loop(endpoint, internal_clone.clone()).await;
                    if let Err(e) = result {
                        error!(
                            "与端点 {} 的连接失败或订阅中断: {}. 10秒后尝试下一个...",
                            endpoint, e
                        );
                    }
                    sleep(Duration::from_secs(10)).await;
                }
            }
        });
    }

    /// 从缓存中获取最新的区块哈希数据
    pub fn get_latest_blockhash(&self) -> Option<BlockhashData> {
        self.internal.load().back().cloned()
    }

    /// 根据滞后slot数获取较旧的blockhash；lag=0 等同于最新
    pub fn get_blockhash_with_lag(&self, lag_slots: usize) -> Option<BlockhashData> {
        let guard = self.internal.load();
        if guard.is_empty() {
            return None;
        }
        let available_lag = if lag_slots >= guard.len() { guard.len() - 1 } else { lag_slots };
        let idx = guard.len() - 1 - available_lag;
        guard.get(idx).cloned()
    }

    /// 核心订阅逻辑：连接、订阅并更新共享状态
    async fn run_subscription_loop(
        endpoint_addr: &str,
        internal: Arc<ArcSwap<VecDeque<BlockhashData>>>,
    ) -> Result<()> {
        let domain_name = endpoint_addr.split(':').next().unwrap_or(endpoint_addr);
        let endpoint_uri = format!("https://{}", endpoint_addr);
        let tls = ClientTlsConfig::new().domain_name(domain_name);

        let mut client = GeyserGrpcClient::connect(endpoint_uri, None::<String>, Some(tls))
            .map_err(|e| anyhow!("连接到gRPC端点 {} 失败: {}", endpoint_addr, e))?;
        

        let mut filters = HashMap::new();
        filters.insert(
            "blocks_meta".to_string(),
            SubscribeRequestFilterBlocksMeta {},
        );

        let request = SubscribeRequest {
            blocks_meta: filters,
            commitment: Some(CommitmentLevel::Processed as i32),
            ..Default::default()
        };

        let (mut write, mut read) = client.subscribe().await?;
        write.send(request).await?;
        debug!("已发送区块元数据订阅请求，等待更新...");

        let stream_timeout = Duration::from_secs(30);

        while let Ok(Some(message_result)) = tokio::time::timeout(stream_timeout, read.next()).await {
            match message_result {
                Ok(msg) => {
                    if let Some(UpdateOneof::BlockMeta(meta)) = msg.update_oneof {
                        // debug!("收到新的区块元数据: Slot={}, Hash={}", meta.slot, meta.blockhash);

                        match Hash::from_str(&meta.blockhash) {
                            Ok(hash) => {
                                let current_deque = internal.load();
                                if !current_deque.back().map_or(false, |d| d.slot >= meta.slot) {
                                    let mut new_deque = (**current_deque).clone();
                                    new_deque.push_back(BlockhashData { hash, timestamp: Instant::now(), slot: meta.slot });
                                    if new_deque.len() > 30 {
                                        new_deque.pop_front();
                                    }
                                    internal.store(Arc::new(new_deque));
                                    debug!("✅ 新区块哈希已更新: Slot={}, Hash={}", meta.slot, hash);
                                }
                            }
                            Err(e) => {
                                warn!(
                                    "无法将字符串解析为区块哈希: '{}', 错误: {}",
                                    meta.blockhash, e
                                );
                            }
                        }
                    }
                }
                Err(e) => {
                    return Err(anyhow!("从gRPC流接收消息时出错: {}", e));
                }
            }
        }

        Err(anyhow!("gRPC流超时（{}秒内未收到消息）", stream_timeout.as_secs()))
    }
} 