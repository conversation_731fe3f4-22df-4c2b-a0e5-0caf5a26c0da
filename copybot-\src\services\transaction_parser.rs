use anyhow::{Result, anyhow};
use serde_json::{Value, json};
use solana_sdk::{pubkey::Pubkey, system_instruction::SystemInstruction};
use std::str::FromStr;
use crate::common::logger::Logger;
use std::collections::HashSet;
use std::time::Instant;
use tokio::task;
use std::sync::Arc;
use log::{debug, error, warn, info};
use crate::core::priority_fees::MonitorConfigs;

/// 帮助宏 - 用于记录性能埋点
macro_rules! mark {
    ($logger:expr, $name:expr, $start:expr) => {{
        let dur = $start.elapsed().as_secs_f64() * 1_000.0;
        $logger.debug(format!("[TIMING] {}: {:.2} ms", $name, dur));
    }};
}

/// 交易类型枚举
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum TransactionType {
    PumpFunBuy,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Token<PERSON>ransfer,
    Unknown,
}

/// 钱包变化信息
#[derive(Debug, Clone)]
pub struct WalletChange {
    pub wallet: String,
    pub pre_amount: f64,
    pub post_amount: f64,
    pub change_amount: f64,
}

/// 代币信息
#[derive(Debug, Clone)]
pub struct TokenInfo {
    pub mint: String,
    pub decimals: u8,
    pub symbol: Option<String>,
    pub name: Option<String>,
}

/// 解析结果结构体
#[derive(Debug, Clone)]
pub struct ParsedTransaction {
    pub signature: String,
    pub transaction_type: TransactionType,
    pub token_info: Option<TokenInfo>,
    pub amount_in: f64,
    pub amount_out: f64,
    pub price: Option<f64>,
    pub wallet_changes: Vec<WalletChange>,
    pub program_id: Option<String>,
    pub instruction_data: Option<String>,
    pub target_wallet_involved: bool,
    pub raw_data: Value,
    pub compute_unit_price: Option<u64>,
    pub original_token_amount: Option<u64>,
    pub original_limit_amount: Option<u64>,
}

impl Default for ParsedTransaction {
    fn default() -> Self {
        ParsedTransaction {
            signature: String::new(),
            transaction_type: TransactionType::Unknown,
            token_info: None,
            amount_in: 0.0,
            amount_out: 0.0,
            price: None,
            wallet_changes: Vec::new(),
            program_id: None,
            instruction_data: None,
            target_wallet_involved: false,
            raw_data: Value::Null,
            compute_unit_price: None,
            original_token_amount: None,
            original_limit_amount: None,
        }
    }
}

/// 交易解析器配置
#[derive(Clone)]
pub struct TransactionParserConfig {
    /// 是否启用详细日志
    pub verbose_logging: bool,
}

impl Default for TransactionParserConfig {
    fn default() -> Self {
        Self {
            verbose_logging: false,
        }
    }
}

/// 交易解析服务
#[derive(Clone)]
pub struct TransactionParser {
    logger: Logger,
    jito_tip_addresses: HashSet<String>,
    config: TransactionParserConfig,
}

impl TransactionParser {
    /// 创建新的交易解析服务
    pub fn new(jito_tip_addresses: HashSet<String>) -> Self {
        Self {
            logger: Logger::new("TransactionParser".to_string()),
            jito_tip_addresses,
            config: TransactionParserConfig::default(),
        }
    }
    
    /// 解析交易数据
    pub async fn parse_transaction(&self, tx_data: &Value, _target_pubkey: &Pubkey) -> Result<ParsedTransaction> {
        let mut result = ParsedTransaction::default();
        
        // 处理原始日志，只记录错误信息
        if let Some(meta) = tx_data.get("meta") {
            if let Some(logs) = meta.get("logMessages").and_then(|l| l.as_array()) {
                if logs.iter().any(|log| log.as_str().map_or(false, |s| s.contains("Error"))) {
                    error!("交易执行错误");
                    for log in logs {
                        if let Some(log_str) = log.as_str() {
                            if log_str.contains("Error") {
                                error!("{}", log_str);
                            }
                        }
                    }
                }
            }
        }

        // 解析交易数据
        if let Some(message) = tx_data.get("transaction").and_then(|t| t.get("message")) {
            if let Err(e) = self.parse_message(message, &mut result) {
                error!("解析交易消息失败: {}", e);
            }
        }

        Ok(result)
    }
    
    /// 提取交易签名
    fn extract_signature(&self, tx_data: &Value) -> Result<String> {
        if let Some(signatures) = tx_data.get("transaction")
            .and_then(|t| t.get("signatures"))
            .and_then(|s| s.as_array()) {
            
            if let Some(sig) = signatures.first().and_then(|s| s.as_str()) {
                return Ok(sig.to_string());
            }
        }
        
        Err(anyhow!("无法提取交易签名"))
    }
    
    /// 识别交易类型
    fn identify_transaction_type(&self, tx_data: &Value) -> TransactionType {
        // 检查日志消息来确定交易类型
        if let Some(logs) = tx_data.get("meta")
            .and_then(|m| m.get("logMessages"))
            .and_then(|l| l.as_array()) {
            
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    // Pump.fun买入交易
                    if log_str.contains("Program log: Instruction: Buy") {
                        return TransactionType::PumpFunBuy;
                    }
                    // Pump.fun卖出交易
                    else if log_str.contains("Program log: Instruction: Sell") {
                        return TransactionType::PumpFunSell;
                    }
                    // Raydium交易
                    else if log_str.contains("Program log: Instruction: Swap") {
                        if log_str.contains("Swap input:") {
                            return TransactionType::TokenTransfer;
                        }
                    }
                }
            }
            
            // 检查是否为SOL转账
            let mut is_sol_transfer = true;
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    if !log_str.contains("Program 11111111111111111111111111111111") && 
                       !log_str.contains("success") && 
                       !log_str.contains("invoke") {
                        is_sol_transfer = false;
                        break;
                    }
                }
            }
            
            if is_sol_transfer {
                return TransactionType::SolTransfer;
            }
        }
        
        // 检查代币余额变化
        if tx_data.get("meta")
            .and_then(|m| m.get("preTokenBalances"))
            .and_then(|b| b.as_array())
            .map_or(false, |a| !a.is_empty()) {
            
            return TransactionType::TokenTransfer;
        }
        
        TransactionType::Unknown
    }
    
    /// 提取程序ID
    fn extract_program_id(&self, tx_data: &Value) -> Option<String> {
        // 尝试从指令中找到程序ID
        if let Some(instructions) = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("instructions"))
            .and_then(|i| i.as_array()) {
            
            if let Some(first_inst) = instructions.first() {
                if let Some(program_idx) = first_inst.get("programIdIndex").and_then(|p| p.as_u64()) {
                    // 获取账户列表
                    if let Some(account_keys) = tx_data.get("transaction")
                        .and_then(|t| t.get("message"))
                        .and_then(|m| m.get("accountKeys"))
                        .and_then(|a| a.as_array()) {
                        
                        if let Some(program_key) = account_keys.get(program_idx as usize).and_then(|k| k.as_str()) {
                            return Some(program_key.to_string());
                        }
                    }
                }
            }
        }
        
        None
    }
    
    /// 提取代币信息
    fn extract_token_info(&self, tx_data: &Value) -> Option<TokenInfo> {
        // 首先，检查交易类型以确定是否需要使用特殊的 PumpFun 逻辑
        let transaction_type = self.identify_transaction_type(tx_data);
        
        match transaction_type {
            TransactionType::PumpFunBuy => {
                // 对于 Buy 交易，优先尝试从指令中提取 Mint 地址
                self.logger.debug("检测到 PumpFun Buy 交易，尝试从指令提取 Mint 地址");
                
                // 1. 首先尝试从Buy指令结构提取Mint地址 - 这是最可靠的方法
                if let Some(mint_from_instruction) = self.extract_mint_from_buy_instruction(tx_data) {
                    self.logger.debug(format!("从Buy指令结构中提取到Mint地址: {}", mint_from_instruction));
                    return Some(TokenInfo {
                        mint: mint_from_instruction,
                        decimals: 6, // PumpFun 代币通常是 6 位小数
                        symbol: None,
                        name: None,
                    });
                }
                
                // 2. 如果指令提取失败，尝试从CPI日志提取
                self.logger.debug("从指令结构提取失败，尝试从CPI日志提取Mint地址");
                if let Some(mint_from_cpi) = self.extract_mint_from_cpi_logs(tx_data) {
                    self.logger.debug(format!("从CPI日志中提取到Mint地址: {}", mint_from_cpi));
                    return Some(TokenInfo {
                        mint: mint_from_cpi,
                        decimals: 6,
                        symbol: None,
                        name: None,
                    });
                }
                
                self.logger.error("无法从PumpFun Buy交易中提取Mint地址");
                None
            },
            TransactionType::PumpFunSell => {
                // 对于Sell交易，首先尝试从preTokenBalances提取 - 这是最可靠的方法
                self.logger.debug("检测到PumpFun Sell交易，尝试从preTokenBalances提取Mint地址");
                
                // 1. 改进：遍历pre/post balances，找到属于目标钱包且减少的代币条目
                let target_wallet_str = self.get_target_wallet_from_tx(tx_data);
                if let Some(wallet_str) = target_wallet_str {
                    if let Some(balances) = tx_data.get("meta")
                        .and_then(|m| m.get("preTokenBalances"))
                        .and_then(|b| b.as_array()) {
                        let post_balances_opt = tx_data.get("meta")
                            .and_then(|m| m.get("postTokenBalances"))
                            .and_then(|b| b.as_array());

                        for pre_balance in balances {
                            if let Some(owner) = pre_balance.get("owner").and_then(|o| o.as_str()) {
                                if owner == wallet_str {
                                    // 找到目标钱包的 pre balance
                                    let pre_amount = pre_balance.get("uiTokenAmount")
                                        .and_then(|u| u.get("uiAmount"))
                                        .and_then(|a| a.as_f64());
                                    
                                    let post_amount = post_balances_opt
                                        .and_then(|post_balances| {
                                            post_balances.iter()
                                                .find(|post_balance| {
                                                    post_balance.get("accountIndex") == pre_balance.get("accountIndex")
                                                })
                                        })
                                        .and_then(|pb| pb.get("uiTokenAmount"))
                                        .and_then(|u| u.get("uiAmount"))
                                        .and_then(|a| a.as_f64());

                                    if let (Some(pre), Some(post)) = (pre_amount, post_amount) {
                                        if pre > post { // 确认是卖出（余额减少）
                                            if let Some(mint) = pre_balance.get("mint").and_then(|m| m.as_str()) {
                                                let decimals = pre_balance.get("uiTokenAmount")
                                                    .and_then(|u| u.get("decimals"))
                                                    .and_then(|d| d.as_u64())
                                                    .unwrap_or(6) as u8;
                                                
                                                self.logger.debug(format!("从目标钱包减少的余额中提取到Mint: {}", mint));
                                                return Some(TokenInfo {
                                                    mint: mint.to_string(),
                                                    decimals,
                                                    symbol: None,
                                                    name: None,
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    self.logger.warn("无法确定目标钱包地址，无法精确提取Sell Mint");
                }

                // 2. 如果上述方法失败，尝试从Sell指令结构提取
                self.logger.debug("从pre/postBalances提取失败，尝试从Sell指令结构提取");
                if let Some(mint_from_instruction) = self.extract_mint_from_sell_instruction(tx_data) {
                    self.logger.debug(format!("从Sell指令结构中提取到Mint地址: {}", mint_from_instruction));
                    return Some(TokenInfo {
                        mint: mint_from_instruction,
                        decimals: 6,
                        symbol: None,
                        name: None,
                    });
                }
                
                // 3. 最后尝试从CPI日志提取
                self.logger.debug("从指令结构提取失败，尝试从CPI日志提取");
                if let Some(mint_from_cpi) = self.extract_mint_from_cpi_logs(tx_data) {
                    self.logger.debug(format!("从CPI日志中提取到Mint地址: {}", mint_from_cpi));
                    return Some(TokenInfo {
                        mint: mint_from_cpi,
                        decimals: 6,
                        symbol: None,
                        name: None,
                    });
                }
                
                self.logger.error("无法从PumpFun Sell交易中提取Mint地址");
                None
            },
            _ => {
                // 对于其他类型的交易，使用原来的逻辑从代币余额中提取
                if let Some(token_balances) = tx_data.get("meta")
                    .and_then(|m| m.get("preTokenBalances"))
                    .and_then(|b| b.as_array()) {
                    
                    if let Some(first_balance) = token_balances.first() {
                        if let Some(mint) = first_balance.get("mint").and_then(|m| m.as_str()) {
                            let decimals = first_balance.get("uiTokenAmount")
                                .and_then(|u| u.get("decimals"))
                                .and_then(|d| d.as_u64())
                                .unwrap_or(6) as u8;
                            
                            return Some(TokenInfo {
                                mint: mint.to_string(),
                                decimals,
                                symbol: None,
                                name: None,
                            });
                        }
                    }
                }
                None
            }
        }
    }
    
    /// 从 Buy 指令中提取 Mint 地址（方法与测试脚本一致）
    fn extract_mint_from_buy_instruction(&self, tx_data: &Value) -> Option<String> {
        // 1. 获取 account_keys 和 instructions
        let account_keys = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array())?;
        
        let instructions = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("instructions"))
            .and_then(|i| i.as_array())?;
        
        // 2. 找到 PumpFun 程序ID在 account_keys 中的索引
        let pump_fun_program_id = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
        let mut pump_program_idx = None;
        
        for (idx, key) in account_keys.iter().enumerate() {
            if let Some(key_str) = key.as_str() {
                if key_str == pump_fun_program_id {
                    pump_program_idx = Some(idx);
                    break;
                }
            }
        }
        
        let pump_program_idx = pump_program_idx?;
        
        // 3. 找到调用 PumpFun 程序的 Buy 指令
        let mint_account_index_in_instruction = 2; // 根据测试脚本，Mint在Buy指令accounts中的索引为2
        
        for instruction in instructions {
            // 检查是否为 PumpFun 程序调用
            if let Some(program_idx) = instruction.get("programIdIndex").and_then(|p| p.as_u64()) {
                if program_idx as usize == pump_program_idx {
                    // 找到 PumpFun 指令
                    if let Some(instruction_accounts) = instruction.get("accounts").and_then(|a| a.as_array()) {
                        if instruction_accounts.len() > mint_account_index_in_instruction {
                            if let Some(mint_account_idx) = instruction_accounts[mint_account_index_in_instruction].as_u64() {
                                // 从account_keys中提取Mint地址
                                if mint_account_idx < account_keys.len() as u64 {
                                    if let Some(mint_address) = account_keys[mint_account_idx as usize].as_str() {
                                        return Some(mint_address.to_string());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        None
    }
    
    /// 从 Sell 指令中提取 Mint 地址
    fn extract_mint_from_sell_instruction(&self, tx_data: &Value) -> Option<String> {
        // 与Buy指令相同的逻辑，但Mint在Sell指令accounts中的索引也为2
        self.extract_mint_from_buy_instruction(tx_data)
    }
    
    /// 从 CPI 日志中动态扫描找到 Mint 地址
    fn extract_mint_from_cpi_logs(&self, tx_data: &Value) -> Option<String> {
        if let Some(logs) = tx_data.get("meta")
            .and_then(|m| m.get("logMessages"))
            .and_then(|l| l.as_array()) {
            
            // 查找包含 "Program data: vdt/" 的日志
            let cpi_log_prefix = "Program data: vdt/";
            
            for log_val in logs {
                if let Some(log_str) = log_val.as_str() {
                    if log_str.starts_with(cpi_log_prefix) {
                        let encoded_data = &log_str[cpi_log_prefix.len()..];
                        self.logger.debug("找到 CPI 日志，尝试解码 Base64 数据...");
                        
                        // 解码 Base64 数据
                        if let Ok(decoded_bytes) = base64::decode(encoded_data) {
                            // 已知 PumpFun 的 Mint 地址存储在交易数据的开头
                            // 但使用更可靠的动态验证方法
                            if decoded_bytes.len() >= 32 {
                                // 提取前 32 字节作为可能的 Mint 地址
                                let mint_bytes = &decoded_bytes[0..32];
                                // 将 [u8] 转换为 [u8; 32]，然后构建 Pubkey
                                let pubkey = match <[u8; 32]>::try_from(mint_bytes) {
                                    Ok(array) => Pubkey::new(&array),
                                    Err(_) => {
                                        self.logger.error("无法将字节转换为 32 字节数组");
                                        return None;
                                    }
                                };
                                let mint_address = pubkey.to_string();
                                
                                self.logger.debug(format!("从 CPI 日志中提取到可能的 Mint 地址: {}", mint_address));
                                
                                // 验证提取的地址格式是否合法
                                if Pubkey::from_str(&mint_address).is_ok() {
                                    return Some(mint_address);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        None
    }
    
    /// 从交易数据中提取签名者（通常是目标钱包）
    fn get_target_wallet_from_tx(&self, tx_data: &Value) -> Option<String> {
        tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array())
            .and_then(|keys| keys.first()) // 通常第一个账户是签名者/费用支付者
            .and_then(|k| k.as_str())
            .map(|s| s.to_string())
    }

    /// 提取钱包变化信息
    fn extract_wallet_changes(&self, tx_data: &Value) -> Vec<WalletChange> {
        let mut changes = Vec::new();
        
        // 获取预交易和交易后的代币余额
        let pre_balances = tx_data.get("meta")
            .and_then(|m| m.get("preTokenBalances"))
            .and_then(|b| b.as_array());
            
        let post_balances = tx_data.get("meta")
            .and_then(|m| m.get("postTokenBalances"))
            .and_then(|b| b.as_array());
        
        if let (Some(pre), Some(post)) = (pre_balances, post_balances) {
            // 处理预交易余额
            for pre_balance in pre.iter() {
                if let Some(owner) = pre_balance.get("owner").and_then(|o| o.as_str()) {
                    let pre_amount = pre_balance.get("uiTokenAmount")
                        .and_then(|u| u.get("uiAmount"))
                        .and_then(|a| a.as_f64())
                        .unwrap_or(0.0);
                    
                    // 在交易后余额中查找相同钱包
                    let post_amount = post.iter()
                        .find(|p| p.get("owner").and_then(|o| o.as_str()) == Some(owner))
                        .and_then(|p| p.get("uiTokenAmount"))
                        .and_then(|u| u.get("uiAmount"))
                        .and_then(|a| a.as_f64())
                        .unwrap_or(0.0);
                    
                    // 计算变化量
                    let change_amount = post_amount - pre_amount;
                    
                    // 只记录有变化的钱包
                    if change_amount.abs() > 0.000001 {
                        changes.push(WalletChange {
                            wallet: owner.to_string(),
                            pre_amount,
                            post_amount,
                            change_amount,
                        });
                    }
                }
            }
            
            // 查找只在交易后余额中出现的钱包
            for post_balance in post.iter() {
                if let Some(owner) = post_balance.get("owner").and_then(|o| o.as_str()) {
                    // 检查该钱包是否已经处理过
                    if !changes.iter().any(|c| c.wallet == owner) {
                        let post_amount = post_balance.get("uiTokenAmount")
                            .and_then(|u| u.get("uiAmount"))
                            .and_then(|a| a.as_f64())
                            .unwrap_or(0.0);
                        
                        changes.push(WalletChange {
                            wallet: owner.to_string(),
                            pre_amount: 0.0,
                            post_amount,
                            change_amount: post_amount,
                        });
                    }
                }
            }
        }
        
        changes
    }
    
    /// 解析金额信息 - 已被新逻辑替代，仅保留作为备用
    fn parse_amounts(&self, _tx_data: &Value, result: &mut ParsedTransaction) {
        // 禁用原来基于wallet_changes的逻辑
        // 现在在parse_pump_fun_transaction中直接计算金额
        if result.transaction_type != TransactionType::PumpFunBuy && 
           result.transaction_type != TransactionType::PumpFunSell {
            // 只对非Pump.fun交易使用wallet_changes逻辑
            for change in &result.wallet_changes {
                if change.change_amount > 0.0 {
                    // 如果钱包余额增加，记为输出金额
                    result.amount_out = result.amount_out.max(change.change_amount);
                } else if change.change_amount < 0.0 {
                    // 如果钱包余额减少，记为输入金额
                    result.amount_in = result.amount_in.max(change.change_amount.abs());
                }
            }
            
            // 尝试计算价格
            if result.amount_in > 0.0 && result.amount_out > 0.0 {
                result.price = Some(result.amount_in / result.amount_out);
            }
        }
    }
    
    /// 解析Pump.fun交易
    fn parse_pump_fun_transaction(&self, tx_data: &Value, result: &mut ParsedTransaction) {
        self.logger.debug("开始解析Pump.fun交易...");
        
        // 从日志中提取数据字段
        if let Some(logs) = tx_data.get("meta")
            .and_then(|m| m.get("logMessages"))
            .and_then(|l| l.as_array()) {
            
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    if log_str.starts_with("Program data:") {
                        result.instruction_data = Some(log_str["Program data:".len()..].trim().to_string());
                        self.logger.debug(format!("找到Program data: {}", &log_str["Program data:".len()..30.min(log_str.len())].trim()));
                        break;
                    }
                }
            }
        }
        
        // 获取账户密钥列表，用于后续转换programIdIndex
        let account_keys = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array());
            
        if account_keys.is_none() {
            self.logger.error("无法获取账户密钥列表");
            return;
        }
        
        let account_keys = account_keys.unwrap();
        
        // 初始化SOL转账金额变量
        let mut sol_amount = 0.0; // 这个变量现在主要用于通过 pre/postBalances 计算
        let mut max_non_tip_transfer_lamports: u64 = 0; // 新增变量记录最大的非 Jito 转账

        // 1. 首先从Meta中检查预后余额以直接计算SOL转账金额
        if let (Some(pre_balances), Some(post_balances)) = (
            tx_data.get("meta").and_then(|m| m.get("preBalances")).and_then(|b| b.as_array()),
            tx_data.get("meta").and_then(|m| m.get("postBalances")).and_then(|b| b.as_array())
        ) {
            self.logger.debug("从预后余额直接计算SOL转账金额");
            
            // 获取交易方向
            let is_sell = result.transaction_type == TransactionType::PumpFunSell;
            
            // 如果是卖出交易，查找交易发起者（通常是index 0）的SOL变化
            if is_sell && pre_balances.len() > 0 && post_balances.len() > 0 {
                if let (Some(pre_sol), Some(post_sol)) = (
                    pre_balances[0].as_u64(),
                    post_balances[0].as_u64()
                ) {
                    // 卖出交易中，发起者SOL余额应该增加
                    if post_sol > pre_sol {
                        let sol_change = (post_sol - pre_sol) as f64 / 1_000_000_000.0;
                        self.logger.debug(format!("计算到卖出交易SOL增加: {} SOL", sol_change));
                        sol_amount = sol_change;
                    }
                }
            }
            // 如果是买入交易，查找交易发起者SOL减少
            else if !is_sell && pre_balances.len() > 0 && post_balances.len() > 0 {
                if let (Some(pre_sol), Some(post_sol)) = (
                    pre_balances[0].as_u64(),
                    post_balances[0].as_u64()
                ) {
                    // 买入交易中，发起者SOL余额应该减少
                    if pre_sol > post_sol {
                        let sol_change = (pre_sol - post_sol) as f64 / 1_000_000_000.0;
                        self.logger.debug(format!("计算到买入交易SOL减少: {} SOL", sol_change));
                        sol_amount = sol_change;
                    }
                }
            }
        }
        
        // 如果从预后余额计算出了SOL金额，就不再进行指令分析
        // 注意：这里的 sol_amount 来自 pre/postBalances，如果它有效，我们优先使用它
        if sol_amount <= 0.001 {  
            // 2. 如果无法从预后余额计算，检查指令中的SOL转账
            // 重置 sol_amount，因为它可能来自之前的无效计算
            sol_amount = 0.0;
            self.logger.debug("预/后余额计算SOL失败或金额过小，开始检查指令...");

            // 合并检查主指令和内部指令的逻辑，寻找最大的非 Jito 转账
            let all_instructions = tx_data.get("transaction")
                                        .and_then(|t| t.get("message"))
                                        .and_then(|m| m.get("instructions"))
                                        .and_then(|i| i.as_array())
                                        .cloned()
                                        .unwrap_or_else(Vec::new);
            
            let inner_instruction_groups = tx_data.get("meta")
                                            .and_then(|m| m.get("innerInstructions"))
                                            .and_then(|i| i.as_array())
                                            .cloned()
                                            .unwrap_or_else(Vec::new);

            // 迭代所有指令（包括主指令和内部指令）
            // 主指令处理
            for (inst_idx, instruction) in all_instructions.iter().enumerate() {
                 self.process_system_transfer_instruction(
                     instruction, 
                     &account_keys, 
                     &format!("主指令[{}]", inst_idx), 
                     &mut max_non_tip_transfer_lamports
                 );
            }
            // 内部指令处理
            for (group_idx, inner_group) in inner_instruction_groups.iter().enumerate() {
                if let Some(instructions) = inner_group.get("instructions").and_then(|i| i.as_array()) {
                    for (inst_idx, instruction) in instructions.iter().enumerate() {
                        self.process_system_transfer_instruction(
                            instruction, 
                            &account_keys, 
                            &format!("内部指令组[{}]指令[{}]", group_idx, inst_idx), 
                            &mut max_non_tip_transfer_lamports
                        );
                    }
                }
            }
            
            // 如果通过指令找到了非 Jito 转账，将其作为 sol_amount
            if max_non_tip_transfer_lamports > 0 {
                sol_amount = max_non_tip_transfer_lamports as f64 / 1_000_000_000.0;
                self.logger.debug(format!("从指令中找到的最大非 Jito 转账: {} lamports = {} SOL", max_non_tip_transfer_lamports, sol_amount));
            }
        }
        
        // 记录最终用于计算的SOL转账金额 (可能是来自余额，也可能是来自指令解析)
        self.logger.debug(format!("最终用于计算的 SOL 金额: {} SOL", sol_amount));
        
        // 从代币余额变化中计算token数量
        let (pre_balances, post_balances) = match (
            tx_data.get("meta").and_then(|m| m.get("preTokenBalances")).and_then(|b| b.as_array()),
            tx_data.get("meta").and_then(|m| m.get("postTokenBalances")).and_then(|b| b.as_array())
        ) {
            (Some(pre), Some(post)) => (pre, post),
            _ => {
                self.logger.error("无法获取代币余额信息");
                return;
            }
        };
        
        // 计算token数量变化（从preTokenBalances和postTokenBalances的差值中得出）
        let mut token_change_amount = 0.0;
        
        // 创建账户索引到pre金额的映射
        let mut pre_amounts = std::collections::HashMap::new();
        
        // 首先收集所有pre余额
        for pre_balance in pre_balances {
            if let (Some(account_idx), Some(amount)) = (
                pre_balance.get("accountIndex").and_then(|i| i.as_u64()),
                pre_balance.get("uiTokenAmount")
                    .and_then(|u| u.get("uiAmount"))
                    .and_then(|a| a.as_f64())
            ) {
                pre_amounts.insert(account_idx, amount);
            }
        }
        
        // 创建账户索引到post金额的映射
        let mut post_amounts = std::collections::HashMap::new();
        
        // 收集所有post余额
        for post_balance in post_balances {
            if let (Some(account_idx), Some(amount)) = (
                post_balance.get("accountIndex").and_then(|i| i.as_u64()),
                post_balance.get("uiTokenAmount")
                    .and_then(|u| u.get("uiAmount"))
                    .and_then(|a| a.as_f64())
            ) {
                post_amounts.insert(account_idx, amount);
            }
        }
        
        // 然后计算每个账户的变化量，包括消失的账户
        for (account_idx, pre_amount) in &pre_amounts {
            // 获取post金额，如果不存在则视为0（账户可能已关闭）
            let post_amount = *post_amounts.get(account_idx).unwrap_or(&0.0);
                
                // 计算变化量
                let change = pre_amount - post_amount;
                
                // 如果是正值，说明这个账户发送了代币，累加到总变化量
                if change > 0.0 {
                    token_change_amount += change;
                if post_amount > 0.0 {
                    self.logger.debug(format!("账户索引 {} 代币变化: {} (从 {} 到 {})", 
                        account_idx, change, pre_amount, post_amount));
                } else {
                    self.logger.debug(format!("账户索引 {} 代币全部卖出: {} (账户已关闭)", 
                        account_idx, change));
                }
            }
        }
        
        // 检查post余额中不在pre余额中的新账户 - 这些可能是新创建的账户
        for (account_idx, post_amount) in &post_amounts {
            if !pre_amounts.contains_key(account_idx) && *post_amount > 0.0 {
                    // 这是一个新账户，接收了代币
                    self.logger.debug(format!("新账户索引 {} 接收代币: {}", account_idx, post_amount));
            }
        }
        
        self.logger.debug(format!("计算代币变化总量: {} tokens", token_change_amount));
        
        // 设置输入/输出金额
        match result.transaction_type {
            TransactionType::PumpFunBuy => {
                result.amount_in = sol_amount; // 使用最终确定的 sol_amount
                result.amount_out = token_change_amount;
                self.logger.debug(format!("Buy交易: 输入 {} SOL, 输出 {} tokens", sol_amount, token_change_amount));
            },
            TransactionType::PumpFunSell => {
                // 卖出交易: 代币变化是输入, SOL转账是输出
                result.amount_in = token_change_amount;  // 代币数量
                
                // === 修改卖单的 amount_out 获取方式 ===
                // 尝试直接从 CPI 日志提取精确的 SOL 输出
                let mut actual_sol_lamports: Option<u64> = None;
                if let Some(logs) = tx_data.get("meta").and_then(|m| m.get("logMessages")).and_then(|l| l.as_array()) {
                    let cpi_log_prefix = "Program data: vdt/";
                    for log_val in logs {
                        if let Some(log_str) = log_val.as_str() {
                            if log_str.starts_with(cpi_log_prefix) {
                                let encoded_data = &log_str[cpi_log_prefix.len()..];
                                // self.logger.debug("找到 CPI 日志，尝试解码 Base64 数据...");
                                match base64::decode(encoded_data) {
                                    Ok(decoded_bytes) => {
                                        const ACTUAL_SOL_OFFSET: usize = 37;
                                        const FIELD_LENGTH: usize = 8;
                                        if decoded_bytes.len() >= ACTUAL_SOL_OFFSET + FIELD_LENGTH {
                                            let bytes_slice = &decoded_bytes[ACTUAL_SOL_OFFSET..ACTUAL_SOL_OFFSET + FIELD_LENGTH];
                                            match bytes_slice.try_into() {
                                                Ok(bytes_array) => {
                                                    actual_sol_lamports = Some(u64::from_le_bytes(bytes_array));
                                                    // self.logger.debug(format!("成功从 CPI 日志偏移量 {} 提取到实际 SOL (lamports): {}", ACTUAL_SOL_OFFSET, actual_sol_lamports.unwrap()));
                                                }
                                                Err(_) => {
                                                    self.logger.error(format!("CPI 日志: 无法将偏移量 {} 的字节转换为 u64", ACTUAL_SOL_OFFSET));
                                                }
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        self.logger.error(format!("CPI 日志: Base64 解码失败: {:?}", e));
                                    }
                                }
                                break; // 假设只有一个这样的日志
                            }
                        }
                    }
                }

                // 如果成功从 CPI 日志提取，则使用该值；否则回退到之前计算的 sol_amount
                if let Some(lamports) = actual_sol_lamports {
                    result.amount_out = lamports as f64 / 1_000_000_000.0;
                    self.logger.debug(format!("Sell交易: 使用 CPI 日志提取的 SOL 输出: {} SOL", result.amount_out));
                } else {
                    // 回退逻辑：如果 CPI 日志解析失败，仍然使用之前计算的 sol_amount
                    // 注意：这可能不准确，但作为备用方案
                    result.amount_out = sol_amount; 
                    self.logger.error(format!("Sell交易: 未能从 CPI 日志提取 SOL，回退到预计算/指令解析的 SOL: {} SOL", result.amount_out));
                }
                self.logger.debug(format!("Sell交易最终: 输入 {} tokens, 输出 {} SOL", result.amount_in, result.amount_out));
            },
            _ => {
                self.logger.error("非Pump.fun交易，无法设置金额");
                return;
            }
        }
        
        // 计算价格 (现在使用更准确的 amount_out)
        if result.amount_in > 0.0 && result.amount_out > 0.0 {
            match result.transaction_type {
                TransactionType::PumpFunBuy => {
                    // 买入价格 = SOL输入 / 代币输出
                    result.price = Some(result.amount_in / result.amount_out);
                    self.logger.debug(format!("计算买入交易价格: {} SOL/token", result.price.unwrap_or(0.0)));
                    
                    if let Some(target_wallet) = self.get_target_wallet_from_tx(tx_data) {
                        // 从配置中获取价格范围
                        if let Ok(configs) = MonitorConfigs::load() {
                            if let Some(wallet_config) = configs.get_wallet_config(&target_wallet) {
                                let min_price = wallet_config.min_price_multiplier;
                                let max_price = wallet_config.max_price_multiplier;
                                
                                if let Some(price) = result.price {
                                    if price < min_price || price > max_price {
                                        self.logger.warn(format!("计算出的价格 {} 超出设定范围 [{}, {}]", 
                                            price, min_price, max_price));
                                    }
                                }
                            } else {
                                self.logger.warn(format!("未找到钱包 {} 的配置，无法验证价格", target_wallet));
                            }
                        } else {
                            self.logger.warn("无法加载钱包配置，使用默认价格范围");
                            // 使用备用的默认价格范围
                            let min_price = 0.000000001;
                            let max_price = 0.00000012;
                            
                            if let Some(price) = result.price {
                                if price < min_price || price > max_price {
                                    self.logger.warn(format!("计算出的价格 {} 超出默认范围 [{}, {}]", 
                                        price, min_price, max_price));
                                }
                            }
                        }
                    }
                },
                TransactionType::PumpFunSell => {
                    // 卖出价格 = SOL输出 / 代币输入
                    result.price = Some(result.amount_out / result.amount_in);
                    self.logger.debug(format!("计算卖出交易价格: {} SOL/token", result.price.unwrap_or(0.0)));
                },
                _ => { // 处理其他类型或保持 None
                    self.logger.debug("非买卖交易，不计算价格");
                }
            }
        } else {
            self.logger.error("无法计算价格: 输入或输出金额为零");
        }
    }
    
    /// 解析SOL转账
    fn parse_sol_transfer(&self, tx_data: &Value, result: &mut ParsedTransaction) {
        // 从指令中提取SOL转账信息
        if let Some(instructions) = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("instructions"))
            .and_then(|i| i.as_array()) {
            
            // 提取系统转账指令
            for instruction in instructions {
                // 支持字符串和数组两种形式的data
                if let Some(data_str) = instruction.get("data").and_then(|d| d.as_str()) {
                    if data_str.starts_with("3Bxs4") {
                        // 这是SOL转账指令的典型格式
                        result.instruction_data = Some(data_str.to_string());
                    }
                } else if let Some(data_array) = instruction.get("data").and_then(|d| d.as_array()) {
                    // 处理数组形式的data
                    let data_bytes: Vec<u8> = data_array.iter()
                        .filter_map(|v| v.as_u64().map(|u| u as u8))
                        .collect();
                    
                    // 尝试使用bincode反序列化
                    if let Ok(SystemInstruction::Transfer { lamports, .. }) = bincode::deserialize::<SystemInstruction>(&data_bytes) {
                        let sol = lamports as f64 / 1_000_000_000.0;
                        result.amount_out = sol;
                        self.logger.debug(format!("解析到SOL转账: {} lamports = {} SOL", lamports, sol));
                    }
                }
            }
        }
    }
    
    /// 解析代币转账
    fn parse_token_transfer(&self, tx_data: &Value, result: &mut ParsedTransaction) {
        // 从日志中提取代币转账信息
        if let Some(logs) = tx_data.get("meta")
            .and_then(|m| m.get("logMessages"))
            .and_then(|l| l.as_array()) {
            
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    if log_str.contains("Instruction: Transfer") {
                        self.logger.debug("检测到代币转账指令");
                    }
                }
            }
        }
    }
    
    /// 检查钱包是否参与交易
    fn is_wallet_involved(&self, tx_data: &Value, wallet: &str) -> bool {
        // 检查账户列表中是否包含目标钱包
        if let Some(account_keys) = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array()) {
            
            for key in account_keys {
                if let Some(key_str) = key.as_str() {
                    if key_str == wallet {
                        return true;
                    }
                }
            }
        }
        
        // 检查代币余额中是否包含目标钱包
        if let Some(pre_balances) = tx_data.get("meta")
            .and_then(|m| m.get("preTokenBalances"))
            .and_then(|b| b.as_array()) {
            
            for balance in pre_balances {
                if let Some(owner) = balance.get("owner").and_then(|o| o.as_str()) {
                    if owner == wallet {
                        return true;
                    }
                }
            }
        }
        
        // 检查交易后余额中是否包含目标钱包
        if let Some(post_balances) = tx_data.get("meta")
            .and_then(|m| m.get("postTokenBalances"))
            .and_then(|b| b.as_array()) {
            
            for balance in post_balances {
                if let Some(owner) = balance.get("owner").and_then(|o| o.as_str()) {
                    if owner == wallet {
                        return true;
                    }
                }
            }
        }
        
        false
    }
    
    /// 导出解析结果为JSON
    pub fn export_to_json(&self, result: &ParsedTransaction) -> Value {
        json!({
            "signature": result.signature,
            "transaction_type": format!("{:?}", result.transaction_type),
            "token_info": result.token_info.as_ref().map(|t| json!({
                "mint": t.mint,
                "decimals": t.decimals,
                "symbol": t.symbol,
                "name": t.name
            })),
            "amount_in": result.amount_in,
            "amount_out": result.amount_out,
            "price": result.price,
            "wallet_changes": result.wallet_changes.iter().map(|wc| json!({
                "wallet": wc.wallet,
                "pre_amount": wc.pre_amount,
                "post_amount": wc.post_amount,
                "change_amount": wc.change_amount
            })).collect::<Vec<Value>>(),
            "program_id": result.program_id,
            "instruction_data": result.instruction_data,
            "target_wallet_involved": result.target_wallet_involved,
            "compute_unit_price": result.compute_unit_price,
            "original_token_amount": result.original_token_amount,
            "original_limit_amount": result.original_limit_amount
        })
    }
    
    /// 检查钱包地址变更
    pub fn check_wallet_address_change(&self, tx_data: &Value, old_address: &str, new_address: &str) -> bool {
        // 如果是SOL转账，检查是否有从old_address到new_address的直接转账
        if let Some(account_keys) = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array()) {
            
            // 检查两个地址是否都在交易账户列表中
            let old_present = account_keys.iter().any(|k| k.as_str() == Some(old_address));
            let new_present = account_keys.iter().any(|k| k.as_str() == Some(new_address));
            
            if old_present && new_present {
                self.logger.debug("检测到旧地址和新地址都在交易中");
                
                // 检查指令类型
                if let Some(instructions) = tx_data.get("transaction")
                    .and_then(|t| t.get("message"))
                    .and_then(|m| m.get("instructions"))
                    .and_then(|i| i.as_array()) {
                    
                    for instruction in instructions {
                        if let Some(accounts) = instruction.get("accounts").and_then(|a| a.as_array()) {
                            if accounts.len() >= 2 {
                                // 获取账户索引
                                let from_idx = accounts.get(0).and_then(|a| a.as_u64()).unwrap_or(u64::MAX) as usize;
                                let to_idx = accounts.get(1).and_then(|a| a.as_u64()).unwrap_or(u64::MAX) as usize;
                                
                                // 检查索引是否有效
                                if from_idx < account_keys.len() && to_idx < account_keys.len() {
                                    // 获取from和to地址
                                    let from = account_keys.get(from_idx).and_then(|k| k.as_str());
                                    let to = account_keys.get(to_idx).and_then(|k| k.as_str());
                                    
                                    // 检查是否为从旧地址到新地址的转账
                                    if from == Some(old_address) && to == Some(new_address) {
                                        self.logger.debug("检测到从旧地址到新地址的转账");
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        false
    }
    
    /// 保存原始交易数据为 JSON 格式
    pub fn save_raw_transaction(&self, tx_data: &Value, signature: &str) -> Result<String> {
        // 不再打印调试信息
        Ok(String::new())
    }
    
    /// 格式化价格显示 - 0.后面跟着0的个数，然后是非零部分
    /// 例如: 0.0001 -> "0.(4)1", 0.00001 -> "0.(5)1", 0.000058158 -> "0.(5)58158"
    pub fn format_price_custom(price: f64) -> String {
        if price == 0.0 {
            return "0.0".to_string();
        }
        
        let s = format!("{:.10}", price); // 保留10位小数确保精度
        if let Some(dot_pos) = s.find('.') {
            let decimals = &s[dot_pos+1..];
            let mut zero_count = 0;
            
            // 计算前导0的数量
            for c in decimals.chars() {
                if c == '0' {
                    zero_count += 1;
                } else {
                    break;
                }
            }
            
            // 取非零部分
            let non_zero_part = &decimals[zero_count..];
            // 去掉末尾的0
            let non_zero_part = non_zero_part.trim_end_matches('0');
            
            if zero_count > 0 {
                // 使用新格式：0.(零的个数)非零部分
                format!("0.({}){}",  zero_count, non_zero_part)
            } else {
                // 如果没有前导零，使用普通格式
                format!("0.{}", non_zero_part)
            }
        } else {
            // 整数情况
            format!("{}.0", s)
        }
    }

    /// 格式化金额显示 - 使用k、m表示千和百万
    /// 例如: 1500 -> "1.5k", 2000000 -> "2m"
    pub fn format_amount_custom(amount: f64) -> String {
        // 处理非常大的数字
        if amount >= 1_000_000_000.0 {
            format!("{:.1}b", amount / 1_000_000_000.0)
        } else if amount >= 1_000_000.0 {
            format!("{:.1}m", amount / 1_000_000.0)
        } else if amount >= 1_000.0 {
            format!("{:.1}k", amount / 1_000.0)
        } else if amount < 0.01 && amount > 0.0 {
            // 对于非常小的数字，使用科学计数法
            format!("{:.2e}", amount)
        } else {
            // 正常范围内的数字，保留2位小数
            format!("{:.2}", amount)
        }
    }

    /// 保存交易历史记录到JSON文件
    pub async fn append_to_transaction_history(&self, tx_data: &Value, signature: &str) -> Result<()> {
        // 开始记录
        self.logger.log(format!("开始保存交易历史记录: {}", signature));
        
        // 克隆必要的数据，避免引用原始数据到新线程中
        let tx_data_cloned = tx_data.clone();
        let signature_cloned = signature.to_string();
        let logger_cloned = self.logger.clone();

        // 在后台任务中执行文件操作
        tokio::spawn(async move {
            if let Err(e) = Self::save_transaction_history_to_file(&tx_data_cloned, &signature_cloned, &logger_cloned).await {
                logger_cloned.error(format!("后台保存交易历史失败: {}", e));
            }
        });
        
        // 立即返回成功，不阻塞主程序
        Ok(())
    }

    // 新增函数，在后台执行实际的文件操作
    async fn save_transaction_history_to_file(tx_data: &Value, signature: &str, logger: &Logger) -> Result<()> {
        // 添加调试日志，打印传入的tx_data原始数据（限制大小）
        let tx_data_debug = format!("{:?}", tx_data);
        if tx_data_debug.len() > 500 {
            logger.log(format!("原始交易数据(截断): {}...(total size: {})", &tx_data_debug[0..500], tx_data_debug.len()));
        } else {
            logger.log(format!("原始交易数据: {}", tx_data_debug));
        }
        
        // 检查原始交易数据中的类型标识
        let tx_type = if let Some(tx_type) = tx_data.get("tx_type").and_then(|t| t.as_str()) {
            match tx_type {
                "pumpfunbuy" => TransactionType::PumpFunBuy,
                "pumpfunsell" => TransactionType::PumpFunSell,
                _ => TransactionType::Unknown
            }
        } else {
            // 如果原始数据中没有类型信息，尝试通过解析交易确定类型
            match TransactionParser::new(HashSet::new()).parse_transaction(tx_data, &Pubkey::default()).await {
                Ok(r) => r.transaction_type,
                Err(e) => {
                    logger.error(format!("解析交易失败: {}", e));
                    return Ok(());
                }
            }
        };
        
        // 记录交易类型
        logger.log(format!("识别到交易类型: {:?}", tx_type));
        
        // 只记录PumpFunBuy和PumpFunSell类型的交易
        if matches!(tx_type, TransactionType::PumpFunBuy | TransactionType::PumpFunSell) {
            // 解析钱包地址（从交易发送方获取）
            let wallet_address = if let Some(signer) = tx_data.get("signer").and_then(|s| s.as_str()) {
                signer.to_string()
            } else {
                TransactionParser::new(HashSet::new()).extract_wallet_address_from_transaction(tx_data)
                    .unwrap_or_else(|| "未知钱包".to_string())
            };
            
            logger.log(format!("钱包地址: {}", wallet_address));
            
            // 提取代币地址
            let token_address = if let Some(mint) = tx_data.get("mint").and_then(|m| m.as_str()) {
                mint.to_string()
            } else {
                // 如果原始数据中没有mint字段，尝试从交易中提取
                match TransactionParser::new(HashSet::new()).parse_transaction(tx_data, &Pubkey::default()).await {
                    Ok(result) => result.token_info.as_ref().map_or("unknown".to_string(), |info| info.mint.clone()),
                    Err(_) => "unknown".to_string()
                }
            };
            
            logger.log(format!("代币地址: {}", token_address));
            
            // 提取交易代币数量并格式化
            let amount = if let Some(amount_str) = tx_data.get("amount").and_then(|a| a.as_str()) {
                amount_str.parse::<f64>().unwrap_or(0.0)
            } else if let Some(amount_num) = tx_data.get("amount").and_then(|a| a.as_u64()) {
                amount_num as f64
            } else {
                // 回退到解析交易获取金额
                match TransactionParser::new(HashSet::new()).parse_transaction(tx_data, &Pubkey::default()).await {
                    Ok(result) => result.amount_out,
                    Err(_) => 0.0
                }
            };
            
            let formatted_amount = TransactionParser::format_amount_custom(amount);
            logger.log(format!("交易代币数量: {} (格式化为: {})", amount, formatted_amount));
            
            // 提取价格并格式化
            let price = if let Some(price_str) = tx_data.get("price").and_then(|p| p.as_str()) {
                price_str.parse::<f64>().ok()
            } else if let Some(price_val) = tx_data.get("price").and_then(|p| p.as_f64()) {
                Some(price_val)
            } else {
                // 回退到解析交易获取价格
                match TransactionParser::new(HashSet::new()).parse_transaction(tx_data, &Pubkey::default()).await {
                    Ok(result) => result.price,
                    Err(_) => None
                }
            };
            
            let formatted_price = price.map(TransactionParser::format_price_custom);
            logger.log(format!("交易价格: {:?} (格式化为: {:?})", price, formatted_price));
            
            // 计算交易的SOL金额
            let sol_amount = if let Some(p) = price {
                // 获取代币精度，默认为6
                let decimals = tx_data.get("meta")
                    .and_then(|m| m.get("preTokenBalances"))
                    .and_then(|b| b.as_array())
                    .and_then(|arr| arr.first())
                    .and_then(|balance| balance.get("uiTokenAmount"))
                    .and_then(|ui| ui.get("decimals"))
                    .and_then(|d| d.as_u64())
                    .unwrap_or(6) as i32;
                
                // 将原始数量转换为UI数量后再计算SOL金额
                let ui_amount = amount / 10f64.powi(decimals);
                ui_amount * p
            } else {
                0.0
            };
            logger.log(format!("交易SOL金额: {} SOL", sol_amount));
            
            // 创建交易历史记录
            let transaction_info = serde_json::json!({
                "signature": signature,
                "type": match tx_type {
                    TransactionType::PumpFunBuy => "buy",
                    TransactionType::PumpFunSell => "sell",
                    _ => "unknown"
                },
                "token_address": token_address,
                "amount": amount,  // 保存原始数值，而不是格式化后的字符串
                "formatted_amount": formatted_amount,  // 添加额外的格式化字段但不改变原有字段类型
                "price": price,  // 保持原始数值
                "formatted_price": formatted_price,  // 添加额外的格式化字段
                "sol_amount": sol_amount, // 添加SOL金额字段
                "formatted_sol_amount": format!("{:.4} SOL", sol_amount), // 格式化的SOL金额
                "timestamp": chrono::Utc::now().to_rfc3339(),
                "status": "confirmed",
                "wallet_address": wallet_address,
                "profit_loss": null,
                "profit_loss_percentage": null,
                "related_transactions": null
            });
            
            // 确保目录存在
            let history_dir = std::env::var("TRANSACTION_HISTORY_DIR").unwrap_or_else(|_| "data/tx_history".to_string());
            let history_path = std::path::Path::new(&history_dir).join("transactions.json");
            
            logger.log(format!("交易历史文件路径: {:?}", history_path));
            
            if !history_path.parent().unwrap().exists() {
                logger.log(format!("创建目录: {:?}", history_path.parent().unwrap()));
                std::fs::create_dir_all(history_path.parent().unwrap())?;
            }
            
            // 读取现有历史记录
            let mut transactions = if history_path.exists() {
                match std::fs::read_to_string(&history_path) {
                    Ok(content) => {
                        logger.log("读取现有交易历史文件成功");
                        if content.trim().is_empty() {
                            logger.log("交易历史文件为空");
                            Vec::new()
                        } else {
                            match serde_json::from_str::<Vec<Value>>(&content) {
                                Ok(txs) => {
                                    logger.log(format!("读取到 {} 条交易历史", txs.len()));
                                    txs
                                },
                                Err(e) => {
                                    logger.error(format!("解析交易历史失败: {}", e));
                                    Vec::new()
                                }
                            }
                        }
                    },
                    Err(e) => {
                        logger.error(format!("读取交易历史文件失败: {}", e));
                        Vec::new()
                    }
                }
            } else {
                logger.log("交易历史文件不存在，创建新文件");
                Vec::new()
            };
            
            // 添加新交易
            transactions.push(transaction_info);
            
            // 保存到文件
            let content = match serde_json::to_string_pretty(&transactions) {
                Ok(data) => {
                    logger.log(format!("序列化 {} 条交易历史记录成功", transactions.len()));
                    data
                },
                Err(e) => {
                    logger.error(format!("序列化交易历史失败: {}", e));
                    return Ok(());
                }
            };

            // 尝试写入文件
            match std::fs::write(&history_path, content) {
                Ok(_) => {
                    logger.log(format!("已保存交易历史记录: {}", signature));
                },
                Err(e) => {
                    logger.error(format!("写入交易历史文件失败: {}", e));
                    return Err(anyhow!("写入交易历史文件失败: {}", e));
                }
            }
        }
        
        Ok(())
    }

    /// 从交易中提取钱包地址（发送方地址）
    fn extract_wallet_address_from_transaction(&self, tx_data: &Value) -> Option<String> {
        // 尝试从meta.transaction.message.accountKeys[0]获取钱包地址
        // 通常第一个账户是交易的发送方
        let account_keys = tx_data
            .get("meta")?
            .get("transaction")?
            .get("message")?
            .get("accountKeys")?
            .as_array()?;
            
        if !account_keys.is_empty() {
            if let Some(sender) = account_keys.first() {
                if let Some(sender_str) = sender.as_str() {
                    return Some(sender_str.to_string());
                }
            }
        }
        
        // 如果上面的方法失败，尝试从transaction.message.accountKeys[0]获取
        let account_keys = tx_data
            .get("transaction")?
            .get("message")?
            .get("accountKeys")?
            .as_array()?;
            
        if !account_keys.is_empty() {
            if let Some(sender) = account_keys.first() {
                if let Some(sender_str) = sender.as_str() {
                    return Some(sender_str.to_string());
                }
            }
        }
        
        None
    }

    /// 辅助函数：处理单个指令，检查是否为 SystemProgram::Transfer，
    /// 如果是，则解析 lamports，检查是否为 Jito Tip，并更新 max_non_tip_transfer_lamports
    fn process_system_transfer_instruction(
        &self,
        instruction: &Value,
        account_keys: &Vec<Value>,
        log_context: &str, // 用于日志，例如 "主指令[0]" 或 "内部指令组[1]指令[2]"
        max_non_tip_transfer_lamports: &mut u64,
    ) {
        if let Some(program_idx) = instruction.get("programIdIndex").and_then(|p| p.as_u64()) {
            if let Some(program_id) = account_keys.get(program_idx as usize).and_then(|k| k.as_str()) {
                if program_id == "11111111111111111111111111111111" { // System Program
                    
                    let data_bytes: Option<Vec<u8>> = 
                        if let Some(data_str) = instruction.get("data").and_then(|d| d.as_str()) {
                            bs58::decode(data_str).into_vec().ok()
                        } else if let Some(data_array) = instruction.get("data").and_then(|d| d.as_array()) {
                            Some(data_array.iter().filter_map(|v| v.as_u64().map(|u| u as u8)).collect())
                        } else {
                            None
                        };

                    if let Some(bytes) = data_bytes {
                        let transfer_lamports: Option<u64> = 
                            match bincode::deserialize::<SystemInstruction>(&bytes) {
                                Ok(SystemInstruction::Transfer { lamports, .. }) => Some(lamports),
                                _ => { // 尝试手动解析
                                    if bytes.len() >= 12 && bytes[0] == 2 {
                                        let mut lamports_bytes = [0u8; 8];
                                        lamports_bytes.copy_from_slice(&bytes[4..12]);
                                        Some(u64::from_le_bytes(lamports_bytes))
                                    } else {
                                        None
                                    }
                                }
                            };

                        if let Some(lamports) = transfer_lamports {
                            let sol = lamports as f64 / 1_000_000_000.0;
                            let mut is_jito_tip = false;
                            let mut log_prefix = format!("{} SystemProgram Transfer", log_context);

                            if let Some(account_indices) = instruction.get("accounts").and_then(|a| a.as_array()) {
                                if account_indices.len() > 1 {
                                    if let Some(to_idx) = account_indices.get(1).and_then(|idx| idx.as_u64()) {
                                        if let Some(to_addr) = account_keys.get(to_idx as usize).and_then(|k| k.as_str()) {
                                            if self.jito_tip_addresses.contains(to_addr) {
                                                is_jito_tip = true;
                                                log_prefix = format!("{} Jito Tip Transfer", log_context);
                                            }
                                        }
                                    }
                                }
                            }

                            self.logger.debug(format!("{}: {} lamports = {} SOL", log_prefix, lamports, sol));

                            // 如果不是 Jito Tip 转账，则更新最大非 Jito 转账金额
                            if !is_jito_tip {
                                if lamports > *max_non_tip_transfer_lamports {
                                    self.logger.debug(format!("更新最大非 Jito 转账金额: {} -> {}", *max_non_tip_transfer_lamports, lamports));
                                    *max_non_tip_transfer_lamports = lamports;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// 提取交易中的计算单元价格
    fn extract_compute_unit_price(&self, tx_data: &Value) -> Option<u64> {
        // 在计算预算指令中寻找设置计算单元价格的指令
        if let Some(instructions) = tx_data
            .get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("instructions"))
            .and_then(|i| i.as_array()) {

            for instruction in instructions {
                if let Some(program_id) = instruction.get("programId").and_then(|id| id.as_str()) {
                    // 计算预算程序的ID
                    if program_id == "ComputeBudget111111111111111111111111111111" {
                        if let Some(data) = instruction.get("data").and_then(|d| d.as_str()) {
                            // 尝试提取价格信息
                            // 设置计算单元价格的指令开头通常是这样的识别字节
                            if data.starts_with("0x03") && data.len() > 18 {
                                // 从data中尝试提取价格值
                                if let Ok(price_hex) = data[4..].parse::<String>() {
                                    if let Ok(price) = u64::from_str_radix(&price_hex, 16) {
                                        return Some(price);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 尝试从日志中提取
        if let Some(logs) = tx_data.get("meta")
            .and_then(|m| m.get("logMessages"))
            .and_then(|l| l.as_array()) {
            
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    // 检查日志中是否包含计算单元价格信息
                    if log_str.contains("compute unit price") {
                        // 尝试提取价格值
                        let parts: Vec<&str> = log_str.split("price").collect();
                        if parts.len() > 1 {
                            let price_part = parts[1].trim();
                            if let Some(price_str) = price_part.split_whitespace().next() {
                                if let Ok(price) = price_str.parse::<u64>() {
                                    return Some(price);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 如果无法提取到，返回默认值10000
        Some(10000)
    }

    /// 从交易指令中提取Swap参数
    fn extract_swap_instruction_params(&self, tx_data: &Value, tx_type: &TransactionType) -> Option<(u64, u64)> {
        // 添加性能计时
        let extract_start = std::time::Instant::now();
        
        let instructions = tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("instructions"))
            .and_then(|i| i.as_array())?;
            
        // 寻找Pump.fun程序的指令
        let pump_fun_program_id = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"; // Pump.fun程序ID
        
        let account_keys = match tx_data.get("transaction")
            .and_then(|t| t.get("message"))
            .and_then(|m| m.get("accountKeys"))
            .and_then(|a| a.as_array()) {
            Some(keys) => keys,
            None => {
                return None;
            }
        };
        
        // 从交易指令中提取参数
        for (idx, instruction) in instructions.iter().enumerate() {
            // 检查程序ID是否为Pump.fun程序
            let program_idx = match instruction.get("programIdIndex").and_then(|p| p.as_u64()) {
                Some(idx) => idx as usize,
                None => continue,
            };
            
            if program_idx >= account_keys.len() {
                continue;
            }
            
            let program_id = match account_keys[program_idx].as_str() {
                Some(id) => id,
                None => continue,
            };
            
            if program_id != pump_fun_program_id {
                continue;
            }
            
            // 获取指令数据
            let data = match instruction.get("data").and_then(|d| d.as_str()) {
                Some(d) => d,
                None => {
                    continue;
                }
            };
            
            // 尝试Base58解码
            let decoded_data = match bs58::decode(data).into_vec() {
                Ok(data) => data,
                Err(_) => {
                    continue;
                }
            };
            
            // 检查指令类型并提取参数
            if decoded_data.len() < 24 {
                continue;
            }
            
            // Pump.fun买入/卖出指令discriminator是前8字节
            let discriminator = &decoded_data[0..8];
            
            // 提取参数
            let mut params = &decoded_data[8..];
            if params.len() < 16 {
                continue;
            }
            
            let amount = u64::from_le_bytes(params[0..8].try_into().unwrap());
            let max_sol_cost = u64::from_le_bytes(params[8..16].try_into().unwrap());
            
            return Some((amount, max_sol_cost));
        }
        
        None
    }

    /// 解析交易消息
    fn parse_message(&self, message: &Value, result: &mut ParsedTransaction) -> Result<()> {
        // 解析账户列表
        if let Some(account_keys) = message.get("accountKeys").and_then(|a| a.as_array()) {
            // 只在关键操作时记录日志
            if account_keys.len() > 10 {
                debug!("大型交易: {} 个账户", account_keys.len());
            }
        }
        Ok(())
    }
}

// 添加到 mod.rs 文件
// pub mod transaction_parser; 