FROM rust:latest as builder

WORKDIR /app

# 安装构建依赖
RUN apt-get update && \
    apt-get install -y pkg-config libssl-dev build-essential git && \
    rm -rf /var/lib/apt/lists/*

# 复制Cargo配置文件
COPY Cargo.toml Cargo.lock ./

# 复制源代码
COPY src ./src

# 构建应用（生产模式）
RUN cargo build --release

# 第二阶段：运行环境
FROM debian:bookworm-slim

WORKDIR /app

# 安装运行时依赖
RUN apt-get update && \
    apt-get install -y libssl3 libssl-dev ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /app/target/release/copy-trading-bot /app/copy-trading-bot

# 创建数据目录
RUN mkdir -p /app/data/tx_history

# 设置环境变量
ENV REDIS_URL=redis://redis:6379/
ENV TRANSACTION_HISTORY_DIR=/app/data/tx_history

# 暴露API端口
EXPOSE 3000

# 设置入口点
ENTRYPOINT ["/app/copy-trading-bot"] 
