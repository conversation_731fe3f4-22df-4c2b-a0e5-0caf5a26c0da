use crate::common::logger::Logger;
use crate::common::utils::AppState;
use crate::core::priority_fees::MonitorConfigs;
use crate::services::yellowstone_grpc::TransactionData;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use std::sync::atomic::Ordering;
use std::sync::Arc;
use crate::engine::swap::{pump_swap, SwapDirection};
use std::env;
use crate::common::globals::{HAS_EXECUTED_BUY, HAS_EXECUTED_SELL};
use crate::services::transaction_parser::TokenInfo;
use log;
use crate::core::priority_fees::{
    get_wallet_price_range_emergency,
    get_wallet_price_range,
    get_wallet_follow_percentage
};
use crate::services::special_wallet_config;
use regex;
use serde_json::Value;

pub struct TradeHandler {
    app_state: Arc<AppState>,
    logger: Logger,
}

impl TradeHandler {
    pub fn new(app_state: Arc<AppState>) -> Self {
        Self {
            app_state,
            logger: Logger::new("TradeHandler".into()),
        }
    }

    async fn get_token_balance(&self, token_address: &str) -> Result<u64, String> {
        let cache = self.app_state.wallet_cache.lock().await;
        if let Some(token_info) = cache.tokens.get(token_address) {
            Ok(token_info.balance)
        } else {
            Err("Token not found in cache".to_string())
        }
    }

    async fn calculate_swap_limits(
        &self,
        direction: &str,
        amount: f64,
        decimals: u8,
        price: f64,
        slippage_bps: u64
    ) -> Result<(u64, u64), String> {
        // 记录输入参数
        self.logger.log(format!("计算交易参数 - 方向: {}, 代币UI数量: {}, 精度: {}, 价格: {}, 滑点: {} bps", 
            direction, amount, decimals, price, slippage_bps));
        
        let scale = 10u128.pow(decimals as u32);
        // 将UI数量转换为代币最小单位
        let amount_raw = (amount * scale as f64) as u128;
        
        self.logger.log(format!("代币最小单位: {}", amount_raw));
        
        let base_lamports = if direction == "buy" {
            // 买入: 计算需要花费的lamports(SOL的最小单位)
            // UI代币数量 * 每代币价格(SOL) * LAMPORTS_PER_SOL
            let sol_cost = amount * price;
            let lamports = (sol_cost * LAMPORTS_PER_SOL as f64) as u128;
            self.logger.log(format!("买入 {} 代币估计花费: {} SOL ({} lamports)", 
                amount, sol_cost, lamports));
            lamports
        } else {
            // 卖出: 计算预期获得的lamports
            // UI代币数量 * 每代币价格(SOL) * LAMPORTS_PER_SOL
            let sol_return = amount * price;
            let lamports = (sol_return * LAMPORTS_PER_SOL as f64) as u128;
            self.logger.log(format!("卖出 {} 代币估计获得: {} SOL ({} lamports)",
                amount, sol_return, lamports));
            lamports
        };
        
        // 应用滑点 - 对于卖出交易，滑点需要减少最小获得的SOL
        if direction == "buy" {
            // 买入: 增加最大SOL成本，减少最小代币获得量
            let slippage_factor = 1.0 + (slippage_bps as f64 / 10000.0);
            let max_lamports = (base_lamports as f64 * slippage_factor) as u64;
            let min_amount = (amount_raw as f64 * (1.0 - slippage_bps as f64 / 10000.0)) as u64;
            
            self.logger.log(format!(
                "买入参数 - 最少获得代币: {} (最小单位), 最多花费SOL: {} lamports ({} SOL)",
                min_amount, max_lamports, max_lamports as f64 / LAMPORTS_PER_SOL as f64
            ));
            
            Ok((min_amount, max_lamports))
        } else {
            // 卖出: 保持代币数量不变，减少最小SOL获得量
            let min_sol_out = (base_lamports as f64 * (1.0 - slippage_bps as f64 / 10000.0)) as u64;
            
            self.logger.log(format!(
                "卖出参数 - 卖出代币: {} (最小单位), 最少获得SOL: {} lamports ({} SOL)",
                amount_raw, min_sol_out, min_sol_out as f64 / LAMPORTS_PER_SOL as f64
            ));
            
            Ok((amount_raw as u64, min_sol_out))
        }
    }

    async fn execute_pump_swap(
        &self,
        token_address: &str,
        token_amount: u64,          // 买入时：最少获得的代币数量(最小单位)，卖出时：代币数量(最小单位)
        min_output: u64,            // 买入时：要花费的SOL数量(lamports)，卖出时：最少获得的SOL数量
        direction: SwapDirection,
        slippage_bps: u64,
        transaction_originator: Option<String>
    ) -> Result<String, String> {
        let use_jito = env::var("USE_JITO_FOR_SWAP").unwrap_or_default().eq_ignore_ascii_case("true");
        
        // 记录详细参数以便调试
        self.logger.log(format!(
            "【交易参数】direction: {:?}, token_address: {}, token_amount: {}, min_output: {}, slippage_bps: {}",
            direction, token_address, token_amount, min_output, slippage_bps
        ));
        
        if matches!(direction, SwapDirection::Buy) {
            self.logger.log(format!(
                "【买入详情】最少获得代币: {} (最小单位), 花费SOL: {} lamports (约 {} SOL)",
                token_amount, min_output, min_output as f64 / LAMPORTS_PER_SOL as f64
            ));
        } else {
            self.logger.log(format!(
                "【卖出详情】卖出代币: {} (最小单位), 最少获得SOL: {} lamports (约 {} SOL)",
                token_amount, min_output, min_output as f64 / LAMPORTS_PER_SOL as f64
            ));
        }
        
        match pump_swap(
            self.app_state.clone(),
            token_amount as f64,
            if direction == SwapDirection::Buy { "buy" } else { "sell" },
            "qty",
            slippage_bps,
            use_jito,
            token_address,
            None,
            None,
            Some(token_amount),  // token_amount_param_from_main
            Some(min_output),    // limit_amount_param_from_main
            None,
            None,
            transaction_originator,
        ).await {
            Ok(sigs) => {
                // 取第一个签名作为结果
                if let Some(sig) = sigs.first() {
                    Ok(sig.clone())
                } else {
                    Err("No signature returned".to_string())
                }
            },
            Err(e) => Err(e.to_string())
        }
    }

    async fn record_trade_to_monitor(
        &self,
        signature: &str,
        token_address: &str,
        amount: u64,
        price: f64,
        slot: u64,
        value: f64
    ) {
        log::info!(
            "记录交易: 签名={}, 代币={}, 数量={}, 价格={}, 价值={}",
            signature, token_address, amount, price, value
        );
    }

    // 从交易数据中提取创作者金库地址并设置环境变量
    fn extract_creator_vault(&self, tx_data: &TransactionData) -> Option<String> {
        // 尝试从原始数据中提取创作者金库地址
        if let Value::String(raw_data) = &tx_data.data["raw_data"] {
            // 使用正则表达式提取创作者金库地址，处理各种可能的格式
            let patterns = [
                r"创作者金库地址:\s*([A-Za-z0-9]{32,44})",       // 标准格式
                r"创作者金库地址:\s*\n\s*([A-Za-z0-9]{32,44})",  // 地址在下一行
                r"creator.vault:\s*([A-Za-z0-9]{32,44})",        // 英文格式
                r"creator_vault:\s*([A-Za-z0-9]{32,44})"         // 下划线格式
            ];
            
            let logger = Logger::new("CreatorVaultExtractor".to_string());
            
            for pattern in patterns {
                if let Ok(re) = regex::Regex::new(pattern) {
                    if let Some(caps) = re.captures(raw_data) {
                        if let Some(vault) = caps.get(1) {
                            let vault_str = vault.as_str().trim().to_string();
                            logger.log(format!("成功提取创作者金库地址: {}", vault_str));
                            // 设置环境变量
                            std::env::set_var("CURRENT_TRANSACTION_CREATOR_VAULT", &vault_str);
                            return Some(vault_str);
                        }
                    }
                }
            }
            
            // 如果上面的模式都没匹配到，记录原始数据的一部分用于调试
            let preview = if raw_data.len() > 200 {
                format!("{}...(截断)", &raw_data[..200])
            } else {
                raw_data.clone()
            };
            logger.error(format!("无法从交易数据中提取创作者金库地址，原始数据预览: {}", preview));
        }
        None
    }

    pub async fn handle_buy(
        &self,
        tx_data: &TransactionData,
        _amount_in: f64,
        price_opt: Option<f64>,
        token_info: &crate::services::transaction_parser::TokenInfo,
    ) {
        // 提取创作者金库地址
        if let Some(vault_address) = self.extract_creator_vault(tx_data) {
            let logger = Logger::new("TradeHandler".to_string());
            logger.log(format!("从交易数据中提取到创作者金库地址: {}", vault_address));
        } else {
            let logger = Logger::new("TradeHandler".to_string());
            logger.error("无法从交易数据中提取创作者金库地址，交易可能会失败");
            return;
        }

        let mint = &token_info.mint;

        // 1. 拿原始最小单位
        let raw_units: u128 = tx_data.amount.into();
        if raw_units == 0 {
            self.logger.warn("原始代币最小单位为 0，跳过跟单。");
            return;
        }

        // 2. 加载钱包配置
        let configs = match MonitorConfigs::load() {
            Ok(c) => c,
            Err(e) => { self.logger.error(format!("加载配置失败: {}", e)); return; }
        };
        let wallet_cfg = match configs.get_wallet_config(&tx_data.signer) {
            Some(c) => c,
            None    => { self.logger.error("未找到钱包配置"); return; }
        };
        let follow_bps = (wallet_cfg.follow_percentage * 100.0) as u128;
        let slippage_bps = (wallet_cfg.slippage_percentage * 100.0) as u128;

        // 3. 计算按比例后的原始单位
        let desired_raw = raw_units
            .saturating_mul(follow_bps)
            / 10_000u128;

        // 4. 拿价格并转 lamports_per_token
        let price = match price_opt {
            Some(p) if p > 0.0 => p,
            _ => { 
                self.logger.warn("价格无效或未提供，跳过跟单交易"); 
                return; 
            }
        };
        let lamports_per_token = (price * (LAMPORTS_PER_SOL as f64)).round() as u128;

        // 5. 获取 decimals
        let decimals = {
            let cache = self.app_state.wallet_cache.lock().await;
            cache.tokens.get(mint).map(|t| t.decimals).unwrap_or(6)
        };
        let scale = 10u128.pow(decimals as u32);

        // 6. 计算基础 lamports（不含滑点）
        let base_lamports = desired_raw
            .saturating_mul(lamports_per_token)
            / scale;

        // 7. 应用滑点
        let max_lamports = base_lamports
            .saturating_mul(10_000 + slippage_bps)
            / 10_000;
        let min_raw = desired_raw
            .saturating_mul(10_000 - slippage_bps)
            / 10_000;

        // 8. 收窄回 u64 并 swap
        let token_amount_param = min_raw.try_into().expect("min_raw <= u64_max");
        let limit_amount_param: u64 = max_lamports.try_into().expect("max_lamports <= u64_max");

        self.logger.log(format!(
            "【DEBUG】买入 - mint: {}, token_amount(代币最小单位): {}, max_lamports(SOL的lamports): {}",
            mint, token_amount_param, limit_amount_param
        ));

        // 设置环境变量以便小费计算可以获取交易发起者
        std::env::set_var("CURRENT_TRANSACTION_SIGNER", &tx_data.signer);
        self.logger.log(format!("设置交易发起者环境变量: {}", &tx_data.signer));
        
        let use_jito = env::var("USE_JITO_FOR_SWAP").unwrap_or_default().eq_ignore_ascii_case("true");
        let price_override = lamports_per_token as u64;

        let result = match self.execute_pump_swap(
            mint,                   // 代币地址 
            token_amount_param,     // 最少获得的代币数量(最小单位) - 交换了顺序
            limit_amount_param,     // 要花费的SOL数量(lamports) - 交换了顺序
            SwapDirection::Buy,
            slippage_bps as u64,
            Some(tx_data.signer.clone())
        ).await {
            Ok(sig) => {
                // 只保留一条关键日志，避免重复
                log::info!("买入交易完成: {}", sig);
                
                // 记录买入交易到监控服务
                if let Some(price) = price_opt {
                    // 计算交易盈亏
                    // 简单计算: 买入价值 = 数量 * 价格
                    let buy_value = max_lamports as f64 / (LAMPORTS_PER_SOL as f64);
                    
                    // 记录到监控服务
                    self.record_trade_to_monitor(
                        &tx_data.signature,
                        mint,
                        max_lamports as u64,
                        price,
                        0,
                        buy_value
                    ).await;
                }
                sig
            },
            Err(e) => {
                log::error!("买入交易失败: {}", e);
                return;
            }
        };
        
        // 移除重复日志
        // log::info!("买入交易完成: {:?}", result);
    }

    pub async fn handle_sell(
        &self,
        tx_data: &TransactionData,
        amount_in: f64,
        price: Option<f64>,
        token_info: &TokenInfo,
    ) {
        // 提取创作者金库地址
        if let Some(vault_address) = self.extract_creator_vault(tx_data) {
            let logger = Logger::new("TradeHandler".to_string());
            logger.log(format!("从交易数据中提取到创作者金库地址: {}", vault_address));
        } else {
            let logger = Logger::new("TradeHandler".to_string());
            logger.error("无法从交易数据中提取创作者金库地址，交易可能会失败");
            return;
        }

        // 从传入的交易中提取数据
        let signature = &tx_data.signature;
        let wallet_address = &tx_data.signer;
        let token_address = &tx_data.mint;
        
        // 移除异步获取槽位的代码，直接使用0
        let current_slot = 0;
        
        // 获取代币小数位数和代币余额 - 优化为一次获取缓存锁，同时读取decimals和balance
        let (token_decimals, token_amount_available) = {
            let cache = self.app_state.wallet_cache.lock().await;
            if let Some(token_info) = cache.tokens.get(token_address) {
                (token_info.decimals, token_info.balance)
            } else {
                (6, 0) // 默认值
            }
        };
        
        // 使用默认值如果无法获取
        let decimals = token_decimals;
        
        // 记录交易信息
        log::info!("卖出交易: 签名={}, 代币={}, 数量={}, 价格={:?}, slot={}", 
            signature, token_address, amount_in, price, current_slot);
        
        // 使用原始价格，不再使用紧急价格范围
        // 获取原始交易实际价格
        let original_price = match price {
            Some(p) if p > 0.0 => p,
            _ => {
                log::warn!("未提供有效价格，不进行卖出交易");
                return; // 如果没有价格，直接返回不执行
            }
        };
        
        // 直接使用原始价格，不再应用调整系数
        let final_price = original_price;
        
        log::info!("使用原始价格进行卖出: {}", final_price);
        
        // 检查是否有可卖余额 - 使用已获取的余额，不再查询
        if token_amount_available == 0 {
            log::warn!("没有可用的代币余额，无法执行卖出操作");
            return;
        }
        
        // 获取原始交易的代币数量（以最小单位计）
        let original_token_amount = tx_data.amount;
        
        // 获取跟随比例，但仅用于日志显示，实际卖出全部买入的代币
        let follow_percentage = match get_wallet_follow_percentage(wallet_address) {
            Ok(percentage) => percentage,
            Err(_) => 30.0, // 默认为30%
        };
        
        // 确定卖出数量 - 使用原始交易数量的100%，而不是钱包余额的一部分
        let amount_to_sell = if original_token_amount > 0 {
            // 确保卖出数量不超过可用余额
            std::cmp::min(original_token_amount as u64, token_amount_available)
        } else {
            log::warn!("原始交易数量为0，改为卖出账户中该代币的全部数量");
            token_amount_available
        };
        
        if amount_to_sell == 0 {
            log::warn!("计算出的卖出数量为0，无法执行卖出操作");
            return;
        }
        
        log::info!("准备卖出: {} 代币，原始交易数量: {}, 可用余额: {}", amount_to_sell, original_token_amount, token_amount_available);
        
        // 计算预期获得的SOL数量
        let token_amount_ui = amount_to_sell as f64 / 10f64.powi(decimals as i32);
        let expected_sol_amount = token_amount_ui * final_price;
        
        log::info!("预期卖出 {} 代币获得 {} SOL", token_amount_ui, expected_sol_amount);
        
        // 从钱包配置获取滑点设置
        let slippage_bps = match crate::core::priority_fees::MonitorConfigs::load() {
            Ok(configs) => {
                match configs.get_wallet_config(wallet_address) {
                    Some(wallet_config) => {
                        // 使用配置中的滑点
                        log::info!("使用钱包配置的滑点: {}%", wallet_config.slippage_percentage);
                        (wallet_config.slippage_percentage * 100.0) as u64
                    },
                    None => {
                        // 尝试从专用配置获取
                        if let Some(special_config) = special_wallet_config::get_special_wallet_config(wallet_address) {
                            log::info!("使用专用配置的滑点: {}%", special_config.slippage_percentage);
                            // 设置环境变量以配置默认小费百分比和优先费
                            std::env::set_var("DEFAULT_TIP_PERCENTAGE", special_config.tip_percentage.to_string());
                            std::env::set_var("DEFAULT_PRIORITY_FEE_MULTIPLIER", special_config.priority_fee_multiplier.to_string());
                            std::env::set_var("DEFAULT_COMPUTE_LIMIT", special_config.compute_limit.to_string());
                            (special_config.slippage_percentage * 100.0) as u64
                        } else {
                            // 专用配置也不存在，使用默认值
                            log::warn!("未找到钱包 {} 的任何配置，使用默认值10", wallet_address);
                            std::env::set_var("DEFAULT_TIP_PERCENTAGE", "1.0"); // 1%小费
                            std::env::set_var("DEFAULT_PRIORITY_FEE_MULTIPLIER", "6.0"); // 6倍优先费
                            std::env::set_var("DEFAULT_COMPUTE_LIMIT", "70000"); // 70000计算单元限制
                            10 // 0.1% = 10 BPS
                        }
                    }
                }
            },
            Err(e) => {
                // 尝试从专用配置获取
                if let Some(special_config) = special_wallet_config::get_special_wallet_config(wallet_address) {
                    log::info!("无法加载钱包配置，使用专用配置的滑点: {}%", special_config.slippage_percentage);
                    // 设置环境变量
                    std::env::set_var("DEFAULT_TIP_PERCENTAGE", special_config.tip_percentage.to_string());
                    std::env::set_var("DEFAULT_PRIORITY_FEE_MULTIPLIER", special_config.priority_fee_multiplier.to_string());
                    std::env::set_var("DEFAULT_COMPUTE_LIMIT", special_config.compute_limit.to_string());
                    (special_config.slippage_percentage * 100.0) as u64
                } else {
                    // 两种配置都无法加载，使用默认值
                    log::warn!("无法加载钱包配置，使用默认滑点 10%: {}", e);
                    std::env::set_var("DEFAULT_TIP_PERCENTAGE", "1.0"); // 1%小费
                    std::env::set_var("DEFAULT_PRIORITY_FEE_MULTIPLIER", "6.0"); // 6倍优先费
                    std::env::set_var("DEFAULT_COMPUTE_LIMIT", "70000"); // 70000计算单元限制
                    10 // 0.1% = 10 BPS
                }
            }
        };
        
        // 计算最小获得的SOL（应用滑点保护）
        // 注意：我们直接使用amount_to_sell作为卖出数量，而不是从calculate_swap_limits获取
        let min_sol_out = (expected_sol_amount * LAMPORTS_PER_SOL as f64 * (1.0 - slippage_bps as f64 / 10000.0)) as u64;
        
        log::info!("卖出交易参数 - 卖出代币: {} (最小单位), 最少获得SOL: {} lamports ({:.6} SOL)", 
            amount_to_sell, min_sol_out, min_sol_out as f64 / LAMPORTS_PER_SOL as f64);
        
        // 设置环境变量以便小费计算可以获取交易发起者
        std::env::set_var("CURRENT_TRANSACTION_SIGNER", wallet_address);
        log::info!("设置交易发起者环境变量: {}", wallet_address);
        
        // 执行卖出操作 - 直接使用amount_to_sell
        let result = match self.execute_pump_swap(
            token_address, amount_to_sell, min_sol_out, SwapDirection::Sell, slippage_bps, Some(wallet_address.clone())
        ).await {
            Ok(sig) => {
                // 只保留一条关键日志，避免重复
                log::info!("卖出交易完成: {}", sig);
                
                // 记录卖出交易到监控服务
                if let Some(price) = price {
                    // 计算交易盈亏
                    // 简单计算: 卖出价值 = 数量 * 价格
                    let sell_value = token_amount_ui * price;
                    
                    // 直接使用0作为槽位，不再尝试获取
                    let current_slot = 0;
                    
                    // 记录到监控服务
                    self.record_trade_to_monitor(
                        signature,
                        token_address,
                        amount_to_sell,
                        price,
                        current_slot,
                        sell_value
                    ).await;
                }
                sig
            },
            Err(e) => {
                log::error!("卖出交易失败: {}", e);
                return;
            }
        };
        
        // 移除重复日志
        // log::info!("卖出交易完成: {:?}", result);
    }
}
