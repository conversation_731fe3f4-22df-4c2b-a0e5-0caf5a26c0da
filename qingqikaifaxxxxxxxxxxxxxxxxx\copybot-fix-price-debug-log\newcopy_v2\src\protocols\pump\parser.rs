use crate::protocols::pump::{
    pda,
    types::{HotPathTrade, TradeType, PUMP_FUN_PROGRAM_ID},
};
use memchr::memchr;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use spl_associated_token_account::get_associated_token_address;
use uuid::Uuid;


// 解析单个 u64
#[inline]
fn fast_u64_parser(bytes: &[u8]) -> Option<u64> {
    lexical_core::parse(bytes).ok()
}

// 解析单个 f64
#[inline]
fn fast_f64_parser(bytes: &[u8]) -> Option<f64> {
    lexical_core::parse(bytes).ok()
}

// 一个内部帮助构建器，用于暂存单笔交易的数据
struct TradeBuilder {
    trade_type: Option<TradeType>,
    signature: Option<String>,
    sol_cost: Option<f64>,
    token_amount: Option<u64>,
    signer: Option<String>,
    price: Option<f64>,
    mint_pubkey: Option<Pubkey>,
    creator_vault_pubkey: Option<Pubkey>,
}

impl TradeBuilder {
    fn new() -> Self {
        Self {
            trade_type: None, signature: None, sol_cost: None, token_amount: None,
            signer: None, price: None, mint_pubkey: None, creator_vault_pubkey: None,
        }
    }

    fn build(&mut self, user_wallet_pubkey: &Pubkey) -> Option<HotPathTrade> {
        let trade_type = self.trade_type.take()?;
        if trade_type == TradeType::Unknown { return None; }
        
        let mint_pubkey = self.mint_pubkey.take()?;
        
        // 🚀 PDA计算是必需的，但优化计算方式
        let bonding_curve_pubkey = pda::find_bonding_curve_address(&mint_pubkey, &PUMP_FUN_PROGRAM_ID);
        let associated_bonding_curve = get_associated_token_address(&bonding_curve_pubkey, &mint_pubkey);
        let user_ata = get_associated_token_address(user_wallet_pubkey, &mint_pubkey);

        Some(HotPathTrade {
            trade_id: String::new(), // 🚀 跳过UUID生成，会在redis_subscriber中重新生成
            trade_type,
            signature: self.signature.take().unwrap_or_default(),
            sol_cost: self.sol_cost.take().unwrap_or(0.0),
            token_amount: self.token_amount.take().unwrap_or(0),
            signer: self.signer.take().unwrap_or_default(),
            price: self.price.take().unwrap_or(0.0),
            // 添加协议字段
            protocol: Some("pump".to_string()),
            mint_pubkey,
            creator_vault_pubkey: self.creator_vault_pubkey.take()?,
            bonding_curve_pubkey,
            associated_bonding_curve,
            user_ata,
            slippage_bps: 0,
        })
    }
}

/// 从一个可能包含多笔交易的 payload 中解析出所有交易
pub fn parse_trades_from_redis_bytes(payload: &[u8], user_wallet_pubkey: &Pubkey) -> Vec<HotPathTrade> {
    let mut trades = Vec::new();
    let mut builder = TradeBuilder::new();
    
    // 🚀 预定义字节常量，避免重复转换
    const SIGNER_KEY: &[u8] = "签名者地址".as_bytes();
    const PRICE_KEY: &[u8] = "当前价格".as_bytes();
    const CREATOR_VAULT_KEY: &[u8] = "创作者金库地址".as_bytes();
    const BUY_BYTES: &[u8] = b"Buy";
    const BUY_CN_BYTES: &[u8] = "买入".as_bytes();
    const SELL_BYTES: &[u8] = b"Sell";
    const SELL_CN_BYTES: &[u8] = "卖出".as_bytes();
    
    // 🚀 内联快速字符串转换
    #[inline]
    fn fast_string_from_bytes(bytes: &[u8]) -> Option<String> {
        std::str::from_utf8(bytes).ok().map(|s| s.to_string())
    }
    
    #[inline] 
    fn fast_pubkey_from_bytes(bytes: &[u8]) -> Option<Pubkey> {
        std::str::from_utf8(bytes).ok().and_then(|s| Pubkey::from_str(s).ok())
    }
    
    // 关键修正：使用 b'\n' (换行符字节) 而不是 b'\\n'
    for line in payload.split(|&b| b == b'\n') {
        if let Some(pos) = memchr(b':', line) {
            let key = &line[..pos];
            // 跳过 ': '
            let value_start = pos.saturating_add(2);
            if value_start >= line.len() { continue; }
            let value = &line[value_start..];

            // 当遇到TYPE时，意味着一笔新交易的开始
            if key == b"TYPE" {
                if let Some(trade) = builder.build(user_wallet_pubkey) {
                    trades.push(trade);
                }
                builder = TradeBuilder::new();
            }

            match key {
                b"TYPE" => {
                    if value == BUY_BYTES || value == BUY_CN_BYTES {
                        builder.trade_type = Some(TradeType::Buy);
                    } else if value == SELL_BYTES || value == SELL_CN_BYTES {
                        builder.trade_type = Some(TradeType::Sell);
                    } else {
                        builder.trade_type = Some(TradeType::Unknown);
                    }
                },
                b"SIGNATURE" => builder.signature = fast_string_from_bytes(value),
                b"MINT" => builder.mint_pubkey = fast_pubkey_from_bytes(value),
                b"TOKEN AMOUNT" => builder.token_amount = fast_u64_parser(value),
                b"SOL COST" | b"MIN SOL OUTPUT" => {
                     if let Some(pos_sol) = memchr(b' ', value) {
                        builder.sol_cost = fast_f64_parser(&value[..pos_sol]);
                    } else {
                        builder.sol_cost = fast_f64_parser(value);
                    }
                },
                _ if key == SIGNER_KEY => builder.signer = fast_string_from_bytes(value),
                _ if key == PRICE_KEY => {
                    if let Some(pos_price) = memchr(b' ', value) {
                        builder.price = fast_f64_parser(&value[..pos_price]);
                    } else {
                        builder.price = fast_f64_parser(value);
                    }
                },
                _ if key == CREATOR_VAULT_KEY => builder.creator_vault_pubkey = fast_pubkey_from_bytes(value),
                _ => {}
            }
        }
    }

    // 处理循环结束后的最后一笔交易
    if let Some(trade) = builder.build(user_wallet_pubkey) {
        trades.push(trade);
    }

    trades
}

