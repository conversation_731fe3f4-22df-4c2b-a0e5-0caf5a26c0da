use copy_trading_bot::common::utils::{
    AppState,
    TokenInfo, WalletCache, LatestBlockhash,
    pda_cache::PdaCacheService,
    HashWithTimestamp,
};
use copy_trading_bot::common::globals::PAUSE_FOLLOW;
use copy_trading_bot::core::priority_fees::{MonitorConfigs, get_wallet_follow_percentage};
use copy_trading_bot::common::logger::Logger;
use copy_trading_bot::services::yellowstone_grpc::TransactionData;
use copy_trading_bot::services::transaction_parser::TransactionParser;
use copy_trading_bot::services::trade_handler::TradeHandler;
use copy_trading_bot::services::wallet_monitor::WalletMonitor;
use copy_trading_bot::common::utils::config_monitor::{self, init_config_monitor, get_monitor_addresses};

use dotenv::dotenv;
use futures_util::StreamExt;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use solana_client::rpc_client::RpcClient;
use solana_client::nonblocking::rpc_client::RpcClient as NonblockingRpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use solana_sdk::hash::Hash;

use std::collections::{HashSet, HashMap, VecDeque};
use std::env;
use std::fs::File;
use std::io::BufReader;
use std::path::PathBuf;
use std::str::FromStr;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{Instant, Duration, SystemTime, UNIX_EPOCH};

use tokio::sync::{Mutex, mpsc};
use tokio::time::sleep;

use anyhow::{Result, anyhow};
use log::error;
use redis::Client as RedisClient;
use regex::Regex;

use copy_trading_bot::dex::pump_fun::Pump;
use copy_trading_bot::services::jito::init_jito_service;
use copy_trading_bot::services::transaction_parser::TokenInfo as ParserTokenInfo;
use solana_client::rpc_request::TokenAccountsFilter;

// --- 本地缓存定义已移至 src/common/utils/mod.rs ---


// 自定义跟单比例，默认为监控交易的30%
// static FOLLOW_PERCENTAGE: f64 = 30.0;

// 手续费增加比例，默认比原交易高10%
// static FEE_INCREASE_PERCENTAGE: f64 = 10.0;

// 定义监控地址配置结构
#[derive(Debug, Serialize, Deserialize)]
struct MonitorAddresses {
    sol_address: String,
    unwanted_key: String,
    targets: Vec<String>,
}

/// 从Redis中提取创作者金库地址，并缓存到Pump实例中
async fn extract_creator_vaults_from_redis(redis_client: &Option<RedisClient>, pump: &Pump) -> Result<()> {
    if let Some(client) = redis_client {
        let logger = Logger::new("RedisExtractor".to_string());
        logger.log("此功能已弃用：不再从Redis提取创作者金库地址，现在直接计算".to_string());
        logger.log("直接使用find_creator_vault_address方法计算金库地址".to_string());
        return Ok(());
    } else {
        return Err(anyhow!("Redis客户端不可用，无法提取创作者金库地址"));
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化环境
    dotenv().ok();
    let logger = Logger::new("Main".to_string());
    logger.log("程序启动，初始化环境变量".to_string());
    
    // 初始化 tracing 日志系统，用于性能埋点
    // 可以通过设置环境变量 RUST_LOG=copy_trading_bot=debug 来控制日志级别
    // 如果要关闭性能埋点的输出，可以设置 RUST_LOG=copy_trading_bot=info
    let log_level = env::var("RUST_LOG").unwrap_or_else(|_| "copy_trading_bot=debug".to_string());
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::new(log_level))
        .with_timer(tracing_subscriber::fmt::time::UtcTime::rfc_3339())
        .with_target(false)  // 不显示目标模块路径
        .with_file(false)    // 不显示文件名
        .with_line_number(false)  // 不显示行号
        .with_thread_ids(false)   // 不显示线程ID
        .with_thread_names(false) // 不显示线程名称
        .init();
    // logger.log("日志系统初始化完成，已启用性能埋点".to_string());
    // logger.log("可通过设置环境变量 RUST_LOG 控制日志级别".to_string());
    
    // 读取配置
    let rpc_https_url = env::var("RPC_URL").expect("RPC_URL not set");
    // logger.debug(format!("RPC URL: {}", rpc_https_url));
    
    // 读取跟单比例和手续费比例配置
    let follow_percentage_str = env::var("FOLLOW_PERCENTAGE").unwrap_or_else(|_| "30.0".to_string());
    let follow_percentage: f64 = follow_percentage_str.parse().unwrap_or_else(|e| {
        logger.warn(format!("无法解析 FOLLOW_PERCENTAGE ('{}'), 使用默认值 30.0: {}", follow_percentage_str, e));
        30.0
    });
    // logger.log(format!("跟单比例设置为: {}%", follow_percentage));

    let fee_increase_percentage_str = env::var("FEE_INCREASE_PERCENTAGE").unwrap_or_else(|_| "10.0".to_string());
    let fee_increase_percentage: f64 = fee_increase_percentage_str.parse().unwrap_or_else(|e| {
        logger.warn(format!("无法解析 FEE_INCREASE_PERCENTAGE ('{}'), 使用默认值 10.0: {}", fee_increase_percentage_str, e));
        10.0
    });
    // logger.log(format!("手续费增加比例设置为: {}%", fee_increase_percentage));

    // 检查0slot配置
    let use_zero_slot = env::var("USE_ZERO_SLOT").unwrap_or_else(|_| "false".to_string()) == "true";
    if use_zero_slot {
        let zero_slot_api_key = env::var("ZERO_SLOT_API_KEY").unwrap_or_default();
        if zero_slot_api_key.is_empty() {
            logger.warn("启用了0slot模式但未设置ZERO_SLOT_API_KEY，将无法提交交易到0slot");
        } else {
            logger.log("0slot模式已启用，将使用0slot提交交易");
            logger.log("已固定使用ny1.0slot.trade节点，跳过自动选择服务器");
            
            // 注释掉自动选择最快服务器的代码
            /*
            match tokio::task::block_in_place(|| {
                tokio::runtime::Handle::current().block_on(async {
                    copy_trading_bot::services::zero_slot::ZeroSlotService::get_fastest_server().await
                })
            }) {
                Ok(fastest_server) => {
                    logger.log(format!("已自动选择最快的0slot服务器节点: {}", fastest_server));
                    env::set_var("ZERO_SLOT_ENDPOINT", fastest_server);
                },
                Err(e) => {
                    logger.warn(format!("无法自动选择最快的0slot服务器，将使用默认节点: {}", e));
                }
            }
            */
        }
    } else {
        // logger.debug("0slot模式未启用，仅使用标准RPC提交交易");
    }

    // 使用固定私钥初始化钱包
    // logger.debug("正在初始化钱包...".to_string());
    let private_key_str = env::var("PRIVATE_KEY").expect("PRIVATE_KEY not set in .env");
    let keypair_bytes = solana_sdk::bs58::decode(&private_key_str)
        .into_vec()
        .expect("无法解码 PRIVATE_KEY");
    let wallet = Arc::new(Keypair::from_bytes(&keypair_bytes).expect("无法从 PRIVATE_KEY 创建钱包"));
    logger.log(format!("使用钱包地址: {}", wallet.pubkey()));
    
    // 创建RPC客户端
    // logger.debug("正在创建RPC客户端...".to_string());
    let rpc_client = Arc::new(RpcClient::new_with_commitment(rpc_https_url.clone(), CommitmentConfig::confirmed()));
    let nonblocking_client = Arc::new(NonblockingRpcClient::new(rpc_https_url));
    logger.debug("RPC客户端创建成功".to_string());

    // --- 初始化本地钱包缓存 --- 
    logger.log("初始化本地钱包余额缓存...".to_string());
    let wallet_cache = Arc::new(Mutex::new(WalletCache::new()));
    
    // --- 初始化 LatestBlockhash 缓存 --- 
    logger.log("初始化 LatestBlockhash 缓存...".to_string());
    let latest_blockhash_cache = Arc::new(tokio::sync::RwLock::new(VecDeque::with_capacity(3)));
    
    // 执行首次余额获取
    let initial_sol = fetch_sol_balance_rpc(&rpc_client, &wallet.pubkey()).await?;
    let initial_tokens = fetch_token_accounts_rpc(&rpc_client, &wallet.pubkey()).await?;
    let initial_timestamp = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
    
    {
        let mut cache = wallet_cache.lock().await;
        cache.sol_balance = initial_sol;
        cache.tokens = initial_tokens;
        cache.last_updated = initial_timestamp;
        logger.log(format!("首次缓存填充完成: SOL={}, Tokens={}", initial_sol, cache.tokens.len()));
    } // Mutex 锁在这里释放
    
    // --- 创建PDA缓存服务 ---
    logger.log("初始化PDA缓存服务...".to_string());
    let pda_cache_service = PdaCacheService::new(
        rpc_client.clone(),
        wallet.clone(),
        wallet_cache.clone(),
        Some(7200), // 2小时缓存有效期
    );
    
    // --- 预缓存所有代币的PDA/ATA地址 ---
    logger.log("开始预缓存钱包中所有代币的PDA/ATA地址...".to_string());
    match pda_cache_service.cache_all_wallet_tokens().await {
        Ok(_) => {
            logger.log("代币PDA/ATA地址预缓存完成".to_string());
        },
        Err(e) => {
            logger.warn(format!("代币PDA/ATA地址预缓存失败: {}", e));
        }
    }
    
    // 初始化钱包监控服务
    logger.log("初始化钱包监控服务...".to_string());
    let wallet_monitor = Arc::new(WalletMonitor::new());
    if let Err(e) = wallet_monitor.init().await {
        logger.error(format!("初始化钱包监控服务失败: {}", e));
        // 继续运行，但钱包监控功能可能不可用
    }
    
    // --- 创建 AppState (包含本地缓存引用) --- 
    let app_state = AppState {
        rpc_client: rpc_client.clone(),
        rpc_nonblocking_client: nonblocking_client.clone(),
        wallet: wallet.clone(),
        wallet_cache: wallet_cache.clone(),
        latest_blockhash: latest_blockhash_cache.clone(),
        pump_service: None,
        wallet_configs: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        config_file_path: std::env::var("WALLET_CONFIG_PATH")
            .unwrap_or_else(|_| "wallet_configs.json".to_string()),
        wallet_monitor: wallet_monitor.clone(),
    };
    logger.debug("局部应用程序状态已创建".to_string());
    
    // 加载钱包配置
    if let Err(e) = app_state.load_wallet_configs().await {
        eprintln!("加载钱包配置失败: {}", e);
    }
    
    // 初始化全局Pump服务实例
    logger.log("正在初始化全局Pump服务实例...".to_string());
    let pump_instance = Arc::new(Mutex::new(Pump::new(
        app_state.rpc_nonblocking_client.clone(),
        app_state.rpc_client.clone(),
        app_state.wallet.clone(),
        app_state.wallet_cache.clone(),
    )));
    
    // 从环境变量获取 Redis URL
    let redis_url = env::var("REDIS_URL").unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());
    logger.log(format!("使用 Redis 连接: {}", redis_url));
    
    // 初始化Redis客户端
    let redis_client = match RedisClient::open(redis_url.clone()) {
        Ok(client) => {
            logger.log("成功连接到Redis".to_string());
            Some(client)
        },
        Err(e) => {
            logger.error(format!("连接Redis失败: {}, 无法获取创作者金库地址", e));
            return Err(anyhow!("连接Redis失败，无法获取创作者金库地址: {}", e));
        }
    };
    
    // 从Redis提取创作者金库地址并缓存到Pump实例中
    if let Some(client) = &redis_client {
        // 不再需要从Redis提取金库地址，也不需要计算，现在直接从交易数据中提取
        logger.log("不再从Redis提取创作者金库地址，现在直接从交易数据中提取".to_string());
    }
    
    // 移除手动设置金库地址的部分，因为不再需要缓存
    {
        logger.log("现在直接从交易数据中提取金库地址，无需计算或缓存".to_string());
    }
    
    // 批量缓存代币PDA/ATA地址 (保留这部分，因为PDA/ATA缓存仍然有用)
    {
        let pump = pump_instance.lock().await;
        logger.log("执行初始代币PDA/ATA地址缓存...".to_string());
        // 使用PdaCacheService直接缓存
        use copy_trading_bot::common::utils::pda_cache::PdaCacheService;
        let pda_cache_service = PdaCacheService::new(
            pump.client.clone(),
            pump.wallet.clone(),
            pump.wallet_cache.clone(),
            None // 使用默认缓存时间
        );
        
        // 从钱包缓存中获取代币列表
        let tokens = {
            let wallet_lock = pump.wallet_cache.lock().await;
            let all_tokens: Vec<String> = wallet_lock.tokens.keys().cloned().collect();
            drop(wallet_lock); // 释放锁
            all_tokens
        };
        
        if !tokens.is_empty() {
            let limit = std::cmp::min(10, tokens.len());
            let selected_tokens = &tokens[0..limit];
            if let Err(e) = pda_cache_service.batch_cache_token_pdas(selected_tokens).await {
                logger.warn(format!("初始代币PDA/ATA缓存失败: {}", e));
            } else {
                logger.log(format!("成功缓存 {} 个代币的PDA/ATA地址", selected_tokens.len()));
            }
        } else {
            logger.warn("钱包缓存中没有代币，无法进行PDA/ATA缓存");
        }
    }
    
    // 更新AppState中的pump_service字段
    let app_state = AppState {
        rpc_client: app_state.rpc_client.clone(),
        rpc_nonblocking_client: app_state.rpc_nonblocking_client.clone(),
        wallet: app_state.wallet.clone(),
        wallet_cache: app_state.wallet_cache.clone(),
        latest_blockhash: app_state.latest_blockhash.clone(),
        pump_service: Some(pump_instance),
        wallet_configs: app_state.wallet_configs.clone(),
        config_file_path: app_state.config_file_path.clone(),
        wallet_monitor: app_state.wallet_monitor.clone(),
    };
    logger.log("全局Pump服务实例初始化完成".to_string());
    
    // 将AppState包装在Arc中，用于传递给pump_swap函数
    let app_state = Arc::new(app_state);
    logger.log("已将AppState包装在Arc中，准备用于交易处理".to_string());
    
    // --- 启动后台PDA缓存清理任务 ---
    let pda_cache_cleanup_service = pda_cache_service;
    let pda_cleanup_logger = logger.clone();
    tokio::spawn(async move {
        pda_cleanup_logger.log("启动PDA缓存清理后台任务，每小时清理一次过期缓存".to_string());
        loop {
            // 每小时清理一次过期缓存
            sleep(Duration::from_secs(3600)).await;
            
            match pda_cache_cleanup_service.clean_expired_caches().await {
                Ok(_) => {
                    // 注释掉PDA缓存清理成功的调试信息
                },
                Err(e) => {
                    pda_cleanup_logger.error(format!("PDA缓存清理失败: {}", e));
                }
            }
        }
    });
    logger.log("PDA缓存清理后台任务已启动".to_string());
    
    // --- 启动后台缓存刷新任务 --- 
    let refresh_wallet_cache = wallet_cache.clone();
    let refresh_rpc_client = rpc_client.clone();
    let refresh_wallet_pubkey = wallet.pubkey();
    let refresh_logger = logger.clone(); // 克隆 logger
    tokio::spawn(async move {
        refresh_logger.log("后台钱包缓存刷新任务启动，每 5 秒刷新一次".to_string());
        loop {
            sleep(Duration::from_secs(5)).await;
            let fetch_start = Instant::now();
            
            let sol_result = fetch_sol_balance_rpc(&refresh_rpc_client, &refresh_wallet_pubkey).await;
            let tokens_result = fetch_token_accounts_rpc(&refresh_rpc_client, &refresh_wallet_pubkey).await;
            
            match (sol_result, tokens_result) {
                (Ok(sol_balance), Ok(tokens)) => {
                    let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
                    let mut cache = refresh_wallet_cache.lock().await;
                    cache.sol_balance = sol_balance;
                    cache.tokens = tokens;
                    cache.last_updated = timestamp;
                    let duration = fetch_start.elapsed();
                }
                (Err(e), _) => {
                    refresh_logger.error(format!("后台任务：刷新 SOL 余额失败: {}", e));
                }
                (_, Err(e)) => {
                    refresh_logger.error(format!("后台任务：刷新代币余额失败: {}", e));
                }
            }
        }
    });
    logger.log("后台钱包缓存刷新任务已启动".to_string());
    
    // --- 禁用旧的RPC LatestBlockhash 刷新任务，替换为gRPC方式 --- 
    let refresh_blockhash_cache = latest_blockhash_cache.clone();
    
    // --- 使用新的gRPC区块哈希服务 ---
    let blockhash_logger = logger.clone();
    
    // 从环境变量获取gRPC端点配置
    let custom_endpoints = env::var("YELLOWSTONE_ENDPOINTS").ok().map(|endpoints| {
        endpoints.split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect::<Vec<String>>()
    });
    
    // 如果环境变量中有自定义端点，则使用这些端点
    let grpc_endpoints = if let Some(endpoints) = custom_endpoints {
        if !endpoints.is_empty() {
            blockhash_logger.log(format!("使用环境变量配置的gRPC端点: {}", endpoints.join(", ")));
            endpoints
        } else {
            vec![
                "solana-yellowstone-grpc.publicnode.com:443",
                "solana-mainnet.yellowstone-grpc.network:443",
                "mainnet.helius-rpc.com:443"
            ].into_iter().map(|s| s.to_string()).collect()
        }
    } else {
        vec![
            "solana-yellowstone-grpc.publicnode.com:443",
            "solana-mainnet.yellowstone-grpc.network:443",
            "mainnet.helius-rpc.com:443"
        ].into_iter().map(|s| s.to_string()).collect()
    };

    let grpc_endpoint = env::var("GRPC_ENDPOINT")
        .unwrap_or_else(|_| grpc_endpoints[0].to_string());
    
    // 初始化BlockHashService
    let block_hash_service = copy_trading_bot::services::block_hash_service::BlockHashService::new(&grpc_endpoint, Some(3));
    
    // 启动后台监控
    match block_hash_service.start_background_monitoring().await {
        Ok(_) => {
            blockhash_logger.log("gRPC区块哈希服务启动成功");
        },
        Err(e) => {
            blockhash_logger.error(format!("gRPC区块哈希服务启动失败: {}", e));
            
            // 尝试使用备用端点
            let mut success = false;
            for backup_endpoint in &grpc_endpoints[1..] {
                blockhash_logger.log(format!("尝试备用gRPC端点获取区块哈希: {}", backup_endpoint));
                let backup_service = copy_trading_bot::services::block_hash_service::BlockHashService::new(backup_endpoint, Some(3));
                
                if let Ok(_) = backup_service.start_background_monitoring().await {
                    blockhash_logger.log(format!("使用备用gRPC端点 {} 获取区块哈希成功", backup_endpoint));
                    success = true;
                    break;
                } else {
                    blockhash_logger.error(format!("备用gRPC端点 {} 获取区块哈希失败", backup_endpoint));
                }
            }
            
            if !success {
                return Err(anyhow!("所有gRPC端点获取区块哈希失败，无法启动服务"));
            }
        }
    };
    
    // 启动清理缓存的定时任务
    let blockhash_cache_for_cleaning = refresh_blockhash_cache.clone();
    let cleaning_logger = logger.clone();
    tokio::spawn(async move {
        let mut cleaning_interval = tokio::time::interval(Duration::from_secs(10)); // 10秒清理一次
        let mut cleaning_count = 0; // 清理计数器
        cleaning_logger.info("区块哈希缓存清理任务已启动，每10秒执行一次");
        
        loop {
            cleaning_interval.tick().await;
            
            // 执行清理操作
            let mut cache = blockhash_cache_for_cleaning.write().await;
            let before_size = cache.len();
            let mut removed = 0;
            
            while cache.len() > 3 {
                let _ = cache.pop_back();
                removed += 1;
            }
            
            cleaning_count += 1;
            
            // 只有在实际移除了哈希或每10次清理操作时才记录日志
            if removed > 0 || cleaning_count % 10 == 0 {
                // 注释掉缓存清理的调试信息
            }
        }
    });
    
    // 添加一个任务，将gRPC获取的BlockHashInfo转换为LatestBlockhash并存入缓存
    let blockhash_cache_for_updating = refresh_blockhash_cache.clone();
    let block_hash_service_clone = block_hash_service.clone();
    let update_logger = logger.clone();
    tokio::spawn(async move {
        update_logger.info("BlockHash 更新/缓存后台任务已启动 GGM"); // 保留确认任务启动
        let mut update_interval = tokio::time::interval(Duration::from_millis(100)); // 每100ms更新一次
        let mut hash_update_count = 0; // 新增：用于跟踪哈希更新次数
        let mut last_logged_hash = String::new(); // 新增：记录上次记录的哈希值
        
        loop {
            update_interval.tick().await;

            // 从BlockHashService获取最新哈希 (包含 hash: String)
            if let Some(hash_info) = block_hash_service_clone.get_latest_hash().await {
                // 只在日志级别为TRACE时才输出这些详细日志
                // update_logger.debug(format!("从 BlockHashService 获取到 hash_info (slot: {}) GGM", hash_info.slot));
                
                let hash_str_to_convert = &hash_info.hash;
                // 移除不必要的DEBUG日志
                // update_logger.debug(format!("准备转换字符串: '{}' GGM", hash_str_to_convert));

                // 尝试将 String 转换为 Hash
                if let Ok(hash) = Hash::from_str(hash_str_to_convert) {
                    // 移除不必要的DEBUG日志
                    // update_logger.debug(format!("字符串 '{}' 成功转换为 Hash GGM", hash_str_to_convert));
                    
                    // 更新到现有缓存
                    let mut cache = blockhash_cache_for_updating.write().await;
                    // 移除不必要的DEBUG日志
                    // update_logger.debug("获取到全局缓存锁 GGM");

                    // 检查缓存中是否已存在相同的哈希
                    if !cache.iter().any(|item| item.hash == hash) {
                        // 添加新的哈希到缓存
                        cache.push_back(HashWithTimestamp::new(hash));
                        
                        // 保持缓存大小不超过限制
                        while cache.len() > 3 {
                            cache.pop_front();
                        }
                        
                        // 注释掉区块哈希缓存相关的调试信息
                    } else {
                        // 注释掉哈希缓存相关的调试信息
                    }
                } else {
                    // 保留错误日志，这很重要
                    update_logger.error(format!("!!! 无法将字符串哈希转换为Hash类型: '{}' GGM", hash_str_to_convert));
                }
            } else {
                // 保留警告日志，这很重要
                // 修改：在预热阶段不需要频繁显示此警告
                // update_logger.warn("BlockHashService::get_latest_hash 返回 None GGM");
                
                // 使用静态变量跟踪是否已经在预热阶段显示过警告
                static HASH_NONE_WARNING_DISPLAYED: AtomicBool = AtomicBool::new(false);
                
                // 只在第一次或每隔较长时间才显示警告
                if !HASH_NONE_WARNING_DISPLAYED.load(Ordering::Relaxed) {
                    update_logger.warn("BlockHashService::get_latest_hash 首次返回 None，等待哈希数据...");
                    HASH_NONE_WARNING_DISPLAYED.store(true, Ordering::Relaxed);
                }
            }
        }
    });
    
    logger.log("后台 LatestBlockhash gRPC刷新任务已启动，缓存每10秒清理一次".to_string());
    
    // --- 新增: 区块哈希预热等待 ---
    logger.log("开始预热区块哈希缓存，等待至少获取一个哈希...".to_string());
    let max_wait_seconds = 30; // 最大等待30秒
    let mut waited = 0;
    let mut prewarmed = false;
    
    while waited < max_wait_seconds {
        // 检查缓存是否已有数据
        let cache_size = {
            let cache = refresh_blockhash_cache.read().await;
            cache.len()
        };
        
        if cache_size > 0 {
            logger.log(format!("区块哈希预热成功，缓存包含 {} 个哈希，耗时 {} 秒", cache_size, waited));
            prewarmed = true;
            break;
        }
        
        // 等待1秒再检查
        logger.debug(format!("等待区块哈希数据中... ({}/{}秒)", waited, max_wait_seconds));
        tokio::time::sleep(Duration::from_secs(1)).await;
        waited += 1;
    }
    
    if !prewarmed {
        logger.warn(format!("区块哈希预热超时 ({}秒)，将继续启动但可能在首次交易时需要强制刷新", max_wait_seconds));
    }
    
    // --- 初始化 LatestBlockhash 缓存 (局部) --- 
    logger.debug("初始化局部 LatestBlockhash 缓存...".to_string());
    let latest_blockhash_cache_local = Arc::new(Mutex::new(None::<LatestBlockhash>));

    // --- 初始化 Jito 服务 ---
    logger.log("\n===== 初始化Jito交易服务 =====");
    match init_jito_service().await {
        Ok(_) => logger.log("Jito服务初始化成功，已启用多节点支持"),
        Err(e) => logger.error(format!("Jito服务初始化失败: {}, 将使用默认配置", e)),
    };
    
    // 加载 Jito Tip 地址
    let mut jito_tip_addresses = HashSet::new();
    logger.log("开始加载 Jito Tip 地址...".to_string());
    for (key, value) in env::vars() {
        if key.starts_with("JITO_TIP_ACCOUNT_") {
            logger.debug(format!("加载 Jito Tip 地址: {} = {}", key, value));
            jito_tip_addresses.insert(value);
        }
    }
    logger.log(format!("加载了 {} 个 Jito Tip 地址", jito_tip_addresses.len()));

    // 创建交易解析服务, 传入 Jito 地址集合
    let parser_service = TransactionParser::new(jito_tip_addresses);
    
    // 4. 正常监听模式
    logger.log("\n===== 启动交易监听模式 =====");
    logger.log("程序启动成功，开始监听交易...");
    
    // 添加一个标志，用于控制是否已经打印了第一个完整的消息
    static FIRST_MESSAGE_PRINTED: AtomicBool = AtomicBool::new(false);
    
    // 初始化配置监控服务
    logger.log("初始化配置监控服务...".to_string());
    if let Err(e) = init_config_monitor().await {
        logger.error(format!("初始化配置监控服务失败: {}", e));
        return Err(anyhow!("初始化配置监控服务失败"));
    }
    logger.log("配置监控服务初始化成功，已开始监控配置文件变更".to_string());
    
    // 从配置读取监控地址
    logger.log("从配置读取监控地址...");
    let monitor_addrs = match get_monitor_addresses().await {
        monitor_config => {
            logger.log("成功读取监控地址配置");
            monitor_config
        }
    };
    
    // 使用配置中的地址
    let sol_address = monitor_addrs.sol_address;
    let unwanted_key = monitor_addrs.unwanted_key;
    
    // 如果targets为空，提示但不返回错误
    if monitor_addrs.targets.is_empty() {
        logger.warn("监控地址列表为空，请通过API添加监控地址或直接编辑配置文件");
    } else {
        // 使用第一个目标地址作为主要监控目标
        let target = monitor_addrs.targets[0].clone();
        
        // 如果有多个监控地址，打印出来
        if monitor_addrs.targets.len() > 1 {
            logger.log(format!("发现多个监控地址，将优先使用: {}", target));
            logger.log(format!("其他监控地址: {}", monitor_addrs.targets[1..].join(", ")));
        }
        
        logger.log(format!("目标钱包: {}", target));
    }
    
    logger.log(format!("SOL地址: {}", sol_address));
    logger.log(format!("排除地址: {}", unwanted_key));

    // 从环境变量获取gRPC端点配置
    let custom_endpoints = env::var("YELLOWSTONE_ENDPOINTS").ok().map(|endpoints| {
        endpoints.split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect::<Vec<String>>()
    });
    
    // 如果环境变量中有自定义端点，则使用这些端点
    let grpc_endpoints = if let Some(endpoints) = custom_endpoints {
        if !endpoints.is_empty() {
            logger.log(format!("使用环境变量配置的gRPC端点: {}", endpoints.join(", ")));
            endpoints
        } else {
            vec![
                "solana-yellowstone-grpc.publicnode.com:443",
                "solana-mainnet.yellowstone-grpc.network:443",
                "mainnet.helius-rpc.com:443"
            ].into_iter().map(|s| s.to_string()).collect()
        }
    } else {
        vec![
            "solana-yellowstone-grpc.publicnode.com:443",
            "solana-mainnet.yellowstone-grpc.network:443",
            "mainnet.helius-rpc.com:443"
        ].into_iter().map(|s| s.to_string()).collect()
    };

    let grpc_endpoint = env::var("GRPC_ENDPOINT")
        .unwrap_or_else(|_| grpc_endpoints[0].to_string());
    logger.log(format!("使用gRPC端点: {}", grpc_endpoint));
    logger.log(format!("可用的备用gRPC端点: {}", grpc_endpoints[1..].join(", ")));

    // 启动 Redis 交易监听 - 传递 "*" 作为通配符，实际监控地址从配置文件读取
    let mut transaction_receiver = match start_redis_transaction_monitor(&redis_url, "*", logger.clone()).await {
        Ok(rx) => {
            logger.log("Redis 交易监听服务启动成功，将监控配置文件中的所有地址");
            rx
        },
        Err(e) => {
            logger.error(format!("启动 Redis 交易监听服务失败: {}", e));
            return Err(anyhow!("启动 Redis 交易监听服务失败"));
        }
    };
    
    // 初始化WebSocket状态推送服务
    let websocket_port = std::env::var("WEBSOCKET_PORT")
        .unwrap_or_else(|_| "3001".to_string())
        .parse::<u16>()
        .unwrap_or(3001);
    
    
    /*
    // WebSocket服务初始化代码应该在这里
    */
    println!("WebSocket状态推送服务已临时禁用");
    
    // 初始化API服务器
    let api_port = std::env::var("API_PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()
        .unwrap_or(3000);
    
    // 启动API服务器
    let api_app_state_clone = app_state.clone();
    
    tokio::spawn(async move {
        if let Err(e) = copy_trading_bot::services::api::start_api_server(api_app_state_clone, api_port).await {
            println!("API服务器启动失败: {}", e);
        }
    });
    
    // 处理接收到的交易
    logger.log("开始处理接收到的交易...");

    // 监听交易数据
    while let Some(tx_data) = transaction_receiver.recv().await {
        // 检查是否处于暂停状态
        if PAUSE_FOLLOW.load(Ordering::Relaxed) {
            logger.log("跟单已暂停，跳过交易处理");
            continue;
        }
        
        // 克隆需要在新任务中使用的数据和引用
        let tx_data_clone = tx_data.clone();
        let app_state_clone = app_state.clone();
        let parser_service_clone = Arc::new(parser_service.clone());
        // 移除静态映射的克隆，改为在任务中动态获取
        let logger_clone = logger.clone();
        let follow_percentage_clone = follow_percentage;
        
        // 在新任务中异步处理交易，避免阻塞主循环
        tokio::spawn(async move {
            // 检查是否为目标钱包
            let tx_signer = &tx_data_clone.signer;
            
            // 动态获取最新的监控配置
            let monitor_addrs = config_monitor::get_monitor_addresses().await;
            
            // 判断是否为监控的钱包（使用字符串比较，避免Pubkey转换问题）
            let is_target_wallet = tx_signer == "*" || 
                monitor_addrs.targets.iter().any(|addr| addr == tx_signer);
            
            // 如果不是目标钱包的交易，直接返回
            if !is_target_wallet {
                return;
            }
            
            // 检查价格是否在合理范围内
            if let Some(price) = tx_data_clone.price {
                // 从钱包配置中获取价格范围
                if let Ok(configs) = MonitorConfigs::load() {
                    if let Some(wallet_config) = configs.get_wallet_config(&tx_data_clone.signer) {
                        let min_price = wallet_config.min_price_multiplier;
                        let max_price = wallet_config.max_price_multiplier;
                        
                        if price < min_price || price > max_price {
                            println!("【直接跳过】价格 {} 超出设定范围 [{}, {}]，不执行跟单交易", price, min_price, max_price);
                            return;
                        }
                    } else {
                        logger_clone.warn(format!("未找到钱包 {} 的配置，无法验证价格", tx_data_clone.signer));
                    }
                } else {
                    logger_clone.error("加载钱包配置失败，无法验证价格");
                }
            }
            
            // 设置当前交易发起者地址作为环境变量，方便后续计算小费
            std::env::set_var("CURRENT_TRANSACTION_SIGNER", tx_signer);
            logger_clone.log(format!("设置当前交易发起者: {}", tx_signer));
            
            // 获取钱包跟单比例配置
            let follow_percentage = match get_wallet_follow_percentage(tx_signer) {
                Ok(percentage) => {
                    logger_clone.log(format!("使用钱包配置的跟单比例: {}%", percentage));
                    percentage
                },
                Err(e) => {
                    logger_clone.warn(format!("获取钱包跟单比例失败: {}, 使用默认值", e));
                    30.0 // 默认30%
                }
            };
            
            // 构建交易日志
            let mut tx_logs = Vec::new();
            
            // 交易开始分隔线
            tx_logs.push("==================== 交易信息 ====================".to_string());
            
            // 基本交易信息
            tx_logs.push(format!("时间: {}", 
                SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()));
            tx_logs.push(format!("签名: {}", tx_data_clone.signature));
            tx_logs.push(format!("类型: {}", 
                match tx_data_clone.tx_type.as_str() {
                    "pumpfunbuy" => "买入",
                    "pumpfunsell" => "卖出",
                    _ => "未知"
                }));
            tx_logs.push(format!("代币: {}", tx_data_clone.mint));
            tx_logs.push(format!("数量: {}", tx_data_clone.amount));
            
            if let Some(price) = tx_data_clone.price {
                tx_logs.push(format!("价格: {}", price));
            }
            
            // 如果有警告信息，显示警告
            if let Some(warning) = check_transaction_warnings(&tx_data_clone) {
                tx_logs.push(format!("[警告] {}", warning));
            }
            
            // 交易结束分隔线
            tx_logs.push("==============================================".to_string());
            
            // 一次性输出所有日志
            for log in tx_logs {
                logger_clone.log(log);
            }
            
            // 保存原始交易数据为JS格式
            if let Err(e) = parser_service_clone.save_raw_transaction(&tx_data_clone.data, &tx_data_clone.signature) {
                logger_clone.error(format!("保存原始交易数据失败: {:?}", e));
            }
            
            // 将交易保存到历史记录 - 创建一个完整的JSON对象
            let transaction_json = serde_json::json!({
                "signature": tx_data_clone.signature,
                "tx_type": tx_data_clone.tx_type,
                "mint": tx_data_clone.mint,
                "amount": tx_data_clone.amount,
                "price": tx_data_clone.price,
                "signer": tx_data_clone.signer,
                "sol_value": tx_data_clone.sol_value,
                "data": tx_data_clone.data, // 仍然包含原始数据
                "token_info": tx_data_clone.token_info
            });
            
            if let Err(e) = parser_service_clone.append_to_transaction_history(&transaction_json, &tx_data_clone.signature).await {
                error!("保存交易历史失败: {}", e);
            }
            
            // 直接从结构体字段获取价格和数量信息
            let amount_in = tx_data_clone.amount as f64;
            let price = tx_data_clone.price;
            
            // 获取代币信息
            let token_info = ParserTokenInfo {
                mint: tx_data_clone.mint.clone(),
                decimals: 9, // 使用默认值9代替None
                name: None,
                symbol: None,
            };
            
            if let Some(price_estimate) = price {
                // 检查钱包配置并获取数据
                match MonitorConfigs::load() {
                    Ok(configs) => {
                        let wallet_address = &tx_data_clone.signer;
                        
                        // 查找钱包配置，可能是通配符匹配所有钱包
                        let wallet_config = if wallet_address == "*" {
                            // 如果是通配符，使用第一个有效配置
                            if !configs.wallets.is_empty() {
                                let first_key = configs.wallets.keys().next().unwrap().clone();
                                configs.get_wallet_config(&first_key)
                            } else {
                                None
                            }
                        } else {
                            // 否则查找特定钱包的配置
                            configs.get_wallet_config(wallet_address)
                        };
                        
                        match wallet_config {
                            Some(wallet_config) => {
                                // 计算跟单SOL金额（使用配置中的跟单比例）
                                let follow_sol_amount = amount_in * (wallet_config.follow_percentage / 100.0);
                                let sol_in_lamports = (follow_sol_amount * LAMPORTS_PER_SOL as f64) as u64;
                                
                                // 获取代币小数位
                                let token_decimals: Option<u8>;
                                { 
                                    let cache = app_state_clone.wallet_cache.lock().await;
                                    token_decimals = cache.tokens.get(&token_info.mint).map(|info| info.decimals);
                                } 
                                
                                // 根据SOL金额和价格估算获得的代币数量
                                let decimals_value = token_decimals.unwrap_or(6);
                                let expected_token_amount = (follow_sol_amount / price_estimate) * 10f64.powi(decimals_value as i32);
                                
                                // 使用配置中的滑点
                                let slippage_bps = (wallet_config.slippage_percentage * 100.0) as u64;
                                let min_token_out = (expected_token_amount * (1.0 - slippage_bps as f64 / 10000.0)) as u64;
                                
                                // 3. 直接调用pump_swap函数
                                let use_jito = env::var("USE_JITO_FOR_SWAP")
                                    .unwrap_or_else(|_| "false".to_string())
                                    .to_lowercase() == "true";
                                
                                // 原始计算单元价格
                                let original_compute_price = price_estimate;
                                
                                tokio::spawn(async move {
                                    let trade_handler = TradeHandler::new(app_state_clone.clone());
                                    
                                    match tx_data_clone.tx_type.as_str() {
                                        "pumpfunbuy" => {
                                            let token_info = ParserTokenInfo {
                                                mint: tx_data_clone.mint.clone(),
                                                decimals: 9,
                                                name: None,
                                                symbol: None,
                                            };
                                            trade_handler.handle_buy(&tx_data_clone, amount_in, price, &token_info).await;
                                        },
                                        "pumpfunsell" => {
                                            let token_info = ParserTokenInfo {
                                                mint: tx_data_clone.mint.clone(),
                                                decimals: 9,
                                                name: None,
                                                symbol: None,
                                            };
                                            trade_handler.handle_sell(&tx_data_clone, amount_in, price, &token_info).await;
                                        },
                                        _ => {}
                                    }
                                });
                            }
                            None => {
                                logger_clone.error(format!("未找到钱包 {} 的配置", wallet_address));
                            }
                        }
                    }
                    Err(e) => {
                        logger_clone.error(format!("无法加载钱包配置: {}", e));
                    }
                }
            }
        });
    }
    
    logger.error("监控服务已停止");

    Ok(())
}

/// 获取监控地址配置文件路径
fn get_monitor_config_path() -> PathBuf {
    // 首先尝试当前工作目录
    let current_dir_path = PathBuf::from("monitor_addrs.json");
    if current_dir_path.exists() {
        return current_dir_path;
    }
    
    // 其次尝试获取可执行文件所在目录
    match std::env::current_exe() {
        Ok(exe_path) => {
            if let Some(exe_dir) = exe_path.parent() {
                let exe_dir_path = exe_dir.join("monitor_addrs.json");
                if exe_dir_path.exists() {
                    return exe_dir_path;
                }
            }
        },
        Err(e) => {
            eprintln!("无法获取可执行文件路径: {}", e);
        }
    }
    
    // 如果以上路径都不存在，返回当前目录下的配置文件路径
    // 这会导致后续创建新文件
    PathBuf::from("monitor_addrs.json")
}

/// 添加一个函数，用于检查配置文件是否存在，如果不存在则创建默认配置
fn ensure_monitor_config_exists() -> Result<()> {
    let config_path = get_monitor_config_path();
    
    // 如果配置文件不存在，创建默认配置
    if !config_path.exists() {
        let default_config = MonitorAddresses {
            sol_address: "So11111111111111111111111111111111111111112".to_string(),
            unwanted_key: "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN".to_string(),
            targets: vec!["在这里填写要监控的钱包地址".to_string()],
        };
        
        // 创建JSON字符串
        let config_json = serde_json::to_string_pretty(&default_config)?;
        
        // 写入文件
        std::fs::write(&config_path, config_json)?;
        
        println!("已创建默认监控地址配置文件: {}", config_path.display());
        println!("请编辑该文件，添加您要监控的钱包地址。");
    }
    
    Ok(())
}

/// 读取监控地址配置文件
fn read_monitor_addresses() -> Result<MonitorAddresses> {
    // 确保配置文件存在
    ensure_monitor_config_exists()?;
    
    // 获取配置文件路径
    let config_path = get_monitor_config_path();
    
    // 打印实际使用的配置文件路径
    println!("使用配置文件: {}", config_path.display());
    
    // 打开并读取配置文件
    let file = File::open(&config_path)?;
    let reader = BufReader::new(file);
    
    // 解析JSON
    let monitor_addrs: MonitorAddresses = serde_json::from_reader(reader)?;
    
    // 打印读取到的监控地址
    println!("读取到的监控地址: {:?}", monitor_addrs.targets);
    
    // 验证配置
    if monitor_addrs.sol_address.is_empty() {
        return Err(anyhow!("SOL地址不能为空"));
    }
    
    if monitor_addrs.unwanted_key.is_empty() {
        return Err(anyhow!("排除地址不能为空"));
    }
    
    // 不再要求监控地址列表不能为空
    if monitor_addrs.targets.is_empty() {
        println!("警告: 监控地址列表为空，请通过API添加监控地址");
    }
    
    Ok(monitor_addrs)
}

// 实现缺失的函数
async fn fetch_sol_balance_rpc(rpc_client: &Arc<RpcClient>, pubkey: &Pubkey) -> Result<u64> {
    match rpc_client.get_balance(pubkey) {
        Ok(balance) => Ok(balance),
        Err(e) => Err(anyhow!("获取SOL余额失败: {}", e)),
    }
}

async fn fetch_token_accounts_rpc(rpc_client: &Arc<RpcClient>, pubkey: &Pubkey) -> Result<HashMap<String, TokenInfo>> {
    // 直接使用简化的实现
    let mut token_map = HashMap::new();
    
    // 获取所有包含代币余额的账户
    let accounts = rpc_client.get_token_accounts_by_owner(
        pubkey,
        TokenAccountsFilter::ProgramId(spl_token::id()),
    )?;
    
    // 处理每个账户
    for account_data in accounts {
        // 获取账户公钥
        let pubkey_str = account_data.pubkey;
        
        // 获取实际的账户数据
        match &account_data.account.data {
            solana_account_decoder::UiAccountData::Json(parsed_data) => {
                // 从JSON中提取mint地址
                if let Some(mint) = parsed_data.parsed.get("info")
                    .and_then(|info| info.get("mint"))
                    .and_then(|mint| mint.as_str()) {
                    
                    // 从JSON中提取余额和小数位数
                    let balance = parsed_data.parsed.get("info")
                        .and_then(|info| info.get("tokenAmount"))
                        .and_then(|token_amount| token_amount.get("amount"))
                        .and_then(|amount| amount.as_str())
                        .and_then(|amount_str| amount_str.parse::<u64>().ok())
                        .unwrap_or(0);
                    
                    let decimals = parsed_data.parsed.get("info")
                        .and_then(|info| info.get("tokenAmount"))
                        .and_then(|token_amount| token_amount.get("decimals"))
                        .and_then(|decimals| decimals.as_u64())
                        .map(|d| d as u8)
                        .unwrap_or(9);
                    
                    // 移除余额检查条件，缓存所有代币账户（包括余额为0的）
                    token_map.insert(mint.to_string(), copy_trading_bot::common::utils::TokenInfo {
                        mint: mint.to_string(),
                        balance,
                        decimals,
                        token_account_pubkey: pubkey_str.clone(),
                        last_price: None, // 添加价格字段，初始为None
                    });
                }
            },
            _ => {
                // 不支持的数据格式，跳过
                continue;
            }
        }
    }
    
    Ok(token_map)
}

/// 启动 Redis 交易监听，返回交易数据接收通道
async fn start_redis_transaction_monitor(
    redis_url: &str, 
    target_wallet: &str,
    logger: Logger
) -> Result<mpsc::Receiver<TransactionData>> {
    // 创建通道 - 增大缓冲区减轻背压
    let (tx, rx) = mpsc::channel::<TransactionData>(500);
    
    // 克隆变量供异步任务使用
    let redis_url = redis_url.to_string();
    let target_wallet = target_wallet.to_string();
    let logger_clone = logger.clone();
    
    // 尝试读取配置文件，但不要求必须有监控地址
    match read_monitor_addresses() {
        Ok(_) => {
            logger.log("成功读取监控地址配置");
            
            // 获取最新的监控地址并检查是否为空
            let monitor_addrs = config_monitor::get_monitor_addresses().await;
            if monitor_addrs.targets.is_empty() {
                logger.warn("监控地址列表为空，请通过API添加监控地址或直接编辑配置文件");
            }
        },
        Err(e) => {
            logger.warn(format!("读取监控地址配置出现问题，但将继续启动: {}", e));
        }
    }
    
    // 不再创建静态的地址集合，而是在事件处理时动态获取配置
    
    // 启动监听任务
    tokio::spawn(async move {
        // 预编译正则表达式
        let tx_type_regex = Regex::new(r"TYPE:\s*(Buy|Sell)").unwrap();
        let signer_regex = Regex::new(r"签名者地址:\s*([^\s\r\n]+)").unwrap();
        let mint_regex = Regex::new(r"MINT:\s*([^\s\r\n]+)").unwrap();
        let token_amount_regex = Regex::new(r"TOKEN AMOUNT:\s*(\d+)").unwrap();
        let sol_cost_regex = Regex::new(r"SOL COST:\s*([\d\.]+)\s*SOL").unwrap(); 
        let min_sol_output_regex = Regex::new(r"MIN SOL OUTPUT:\s*([\d\.]+)\s*SOL").unwrap();
        let price_regex = Regex::new(r"当前价格:\s*([\d\.e\-\+]+)(?:\s*SOL)?").unwrap();
        let signature_regex = Regex::new(r"SIGNATURE:\s*([^\s\r\n]+)").unwrap();
        
        // 记录已处理的交易，避免重复处理
        let mut processed_transactions = HashSet::new();
        let mut redis_client: Option<RedisClient> = None;
        let max_processed_txs = 5000; // 最大记录数，防止内存无限增长
        
        // 创建共享数据连接 - 用于查询键值
        let mut _shared_data_conn: Option<Arc<Mutex<redis::aio::Connection>>> = None;
        
        loop {
            // Redis连接和配置逻辑...
            if redis_client.is_none() {
                logger_clone.log(format!("连接到 Redis: {}", redis_url));
                match RedisClient::open(redis_url.clone()) {
                    Ok(client) => {
                        redis_client = Some(client);
                    },
                    Err(e) => {
                        logger_clone.error(format!("连接Redis失败: {}", e));
                        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                        continue;
                    }
                }
            }
            
            // 使用已创建的客户端
            if let Some(client) = &redis_client {
                // 创建主连接
                match client.get_async_connection().await {
                    Ok(conn) => {
                        let mut conn_clone = conn;
                        
                        // 创建共享数据连接
                        match client.get_async_connection().await {
                            Ok(data_conn) => {
                                _shared_data_conn = Some(Arc::new(Mutex::new(data_conn)));
                                logger_clone.log("创建Redis共享数据连接成功");
                            },
                            Err(e) => {
                                logger_clone.error(format!("创建Redis共享数据连接失败: {}", e));
                                redis_client = None; // 重置客户端
                                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                                continue;
                            }
                        }
                        
                        // 配置Redis键空间通知
                        if let Err(e) = redis::cmd("CONFIG")
                            .arg("SET")
                            .arg("notify-keyspace-events")
                            .arg("KEA")
                            .query_async::<_, ()>(&mut conn_clone)
                            .await 
                        {
                            logger_clone.error(format!("配置Redis键空间通知失败: {}", e));
                            redis_client = None; // 重置客户端
                            _shared_data_conn = None; // 重置数据连接
                            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                            continue;
                        }
                        
                        // 创建单独的PubSub连接
                        match client.get_async_connection().await {
                            Ok(pubsub_conn) => {
                                let mut pubsub = pubsub_conn.into_pubsub();
                                
                                // 订阅所有键空间事件
                                if let Err(e) = pubsub.psubscribe("__keyspace@*__:*").await {
                                    logger_clone.error(format!("订阅Redis键空间事件失败: {}", e));
                                    redis_client = None; // 重置客户端
                                    _shared_data_conn = None; // 重置数据连接
                                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                                    continue;
                                }
                                
                                logger_clone.log("Redis 键空间通知订阅成功，开始监听交易...");
                                let mut stream = pubsub.on_message();
                                
                                // 处理消息
                                while let Some(msg) = stream.next().await {
                                    // 获取通道名称和操作类型
                                    let channel: String = match msg.get_channel() {
                                        Ok(ch) => ch,
                                        Err(e) => {
                                            logger_clone.error(format!("获取通道名称失败: {:?}", e));
                                            continue;
                                        }
                                    };
                                    
                                    let operation: String = match msg.get_payload() {
                                        Ok(op) => op,
                                        Err(e) => {
                                            logger_clone.error(format!("获取操作类型失败: {:?}", e));
                                            continue;
                                        }
                                    };
                                    
                                    // 提取实际的键名
                                    let key = match channel.split(':').nth(1) {
                                        Some(key_part) => key_part,
                                        None => {
                                            logger_clone.error(format!("无法解析键名: {}", channel));
                                            continue;
                                        }
                                    };
                                    
                                    // 检查是否已处理过此交易
                                    if processed_transactions.contains(key) {
                                        continue;
                                    }
                                    
                                    // 只关注 "set" 操作的键，且键名看起来像交易签名
                                    if operation == "set" && key.len() > 20 && !key.contains(":") {
                                        // 使用共享数据连接获取键值，而不是每次创建新连接
                                        if let Some(data_conn) = &_shared_data_conn {
                                            let mut value_conn = match data_conn.lock().await {
                                                conn => conn
                                            };
                                            
                                            if let Ok(Some(value)) = redis::cmd("GET").arg(key).query_async::<_, Option<String>>(&mut *value_conn).await {
                                                // 释放连接锁
                                                drop(value_conn);
                                                
                                                // 确认是否为我们需要的交易数据格式
                                                if tx_type_regex.is_match(&value) && signature_regex.is_match(&value) {
                                                    // 提取数据
                                                    let tx_type = tx_type_regex.captures(&value)
                                                        .and_then(|cap| cap.get(1))
                                                        .map(|m| m.as_str())
                                                        .unwrap_or("未知");
                                                    
                                                    let signature = signature_regex.captures(&value)
                                                        .and_then(|cap| cap.get(1))
                                                        .map(|m| m.as_str())
                                                        .unwrap_or(key);  // 如果没提取到就用键名作为签名
                                                    
                                                    let signer = signer_regex.captures(&value)
                                                        .and_then(|cap| cap.get(1))
                                                        .map(|m| m.as_str())
                                                        .unwrap_or("未知");
                                                    
                                                    let mint = mint_regex.captures(&value)
                                                        .and_then(|cap| cap.get(1))
                                                        .map(|m| m.as_str())
                                                        .unwrap_or("未知");
                                                    
                                                    let token_amount = token_amount_regex.captures(&value)
                                                        .and_then(|cap| cap.get(1))
                                                        .map(|m| m.as_str())
                                                        .unwrap_or("0");
                                                    
                                                    // 根据交易类型提取不同的价格信息
                                                    let price = {
                                                        // 对于所有交易类型，都从"当前价格"字段提取真实价格
                                                        price_regex.captures(&value)
                                                            .and_then(|cap| cap.get(1))
                                                            .map(|m| m.as_str())
                                                    };
                                                    
                                                    // 提取SOL相关的信息（根据交易类型）
                                                    let sol_value = if tx_type == "Buy" {
                                                        sol_cost_regex.captures(&value)
                                                            .and_then(|cap| cap.get(1))
                                                            .map(|m| m.as_str())
                                                    } else {
                                                        // 对于Sell类型，从MIN SOL OUTPUT提取
                                                        min_sol_output_regex.captures(&value)
                                                            .and_then(|cap| cap.get(1))
                                                            .map(|m| m.as_str())
                                                    };
                                                    
                                                    // 关键修改：每次处理事件时，动态从全局配置获取最新的监控地址列表
                                                    let monitor_addrs = match config_monitor::get_monitor_addresses().await {
                                                        config => config
                                                    };
                                                    
                                                    // 创建监控地址集合用于当前事件处理
                                                    let target_addresses: HashSet<String> = monitor_addrs.targets.into_iter().collect();
                                                    
                                                    // 使用最新的监控地址列表进行判断
                                                    let is_target_wallet = target_addresses.contains(signer) || target_wallet == "*";
                                                    
                                                    // 只处理目标钱包相关的交易或全部交易（如果 target_wallet 是 "*"）
                                                    if is_target_wallet {
                                                        // 构造 TransactionData
                                                        let transaction_data = build_transaction_data(
                                                            signature.to_string(),
                                                            signer.to_string(),
                                                            mint.to_string(),
                                                            token_amount.to_string(),
                                                            price,
                                                            sol_value,
                                                            tx_type.to_string(),
                                                            &value,  // 传递原始数据用于调试
                                                            Some(json!({  // 添加 token_info
                                                                "mint": mint,
                                                                "decimals": 6,  // 默认值
                                                                "name": null,
                                                                "symbol": null
                                                            }))
                                                        );
                                                        
                                                        // 在发送前制作一个克隆，以防发送失败后需要重试
                                                        let transaction_data_clone = transaction_data.clone();

                                                        // 添加到已处理列表
                                                        processed_transactions.insert(key.to_string());
                                                        
                                                        // 清理过多的缓存记录
                                                        if processed_transactions.len() > max_processed_txs {
                                                            // 移除最早的1000条记录
                                                            let to_remove: Vec<String> = processed_transactions
                                                                .iter()
                                                                .take(1000)
                                                                .cloned()
                                                                .collect();
                                                            
                                                            for old_key in to_remove {
                                                                processed_transactions.remove(&old_key);
                                                            }
                                                        }
                                                        
                                                        // 尝试发送到通道, 忽略"receiver gone"错误
                                                        if let Err(e) = tx.try_send(transaction_data) {
                                                            match e {
                                                                tokio::sync::mpsc::error::TrySendError::Closed(_) => {
                                                                    logger_clone.error("交易监控错误: 接收方已关闭，停止监控");
                                                                    return;
                                                                },
                                                                tokio::sync::mpsc::error::TrySendError::Full(_) => {
                                                                    // 通道已满，丢弃最早的消息
                                                                    logger_clone.warn("通道已满，尝试移除最早的消息");
                                                                    
                                                                    // 使用克隆的数据重试发送
                                                                    match tx.try_send(transaction_data_clone) {
                                                                        Ok(_) => {},
                                                                        Err(send_err) => {
                                                                            match send_err {
                                                                                tokio::sync::mpsc::error::TrySendError::Closed(_) => {
                                                                                    logger_clone.error("交易监控错误: 接收方已关闭，停止监控");
                                                                                    return;
                                                                                },
                                                                                _ => {
                                                                                    logger_clone.error(format!("无法发送交易数据: {}", send_err));
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            } else {
                                                // 释放连接锁
                                                drop(value_conn);
                                                
                                                // 只记录错误而不中断处理
                                                logger_clone.error(format!("无法获取键 {} 的值", key));
                                            }
                                        } else {
                                            logger_clone.error("共享数据连接不可用");
                                        }
                                    }
                                }
                                
                                // 如果从消息循环中退出，表示连接可能已断开
                                logger_clone.warn("Redis消息流已关闭，尝试重新连接");
                            },
                            Err(e) => {
                                logger_clone.error(format!("创建PubSub连接失败: {}", e));
                                redis_client = None;
                            }
                        }
                    },
                    Err(e) => {
                        logger_clone.error(format!("创建Redis连接失败: {}", e));
                        redis_client = None;
                    }
                }
            }
            
            // 连接断开或出错，等待后重试
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
            logger_clone.log("Redis连接断开，5秒后重试...");
        }
    });
    
    Ok(rx)
}

/// 将 Redis 数据转换为 TransactionData
fn build_transaction_data(
    signature: String,
    signer: String,
    mint: String,
    amount: String,
    price: Option<&str>,
    sol_value: Option<&str>,
    tx_type: String,
    original_data: &str,
    token_info: Option<Value>,
) -> TransactionData {
    // 解析字段
    let tx_type_value = match tx_type.as_str() {
        "Buy" => "pumpfunbuy",
        "Sell" => "pumpfunsell",
        _ => "unknown"
    };
    let amount_parsed = amount.parse::<u64>().unwrap_or(0);
    let price_float = price.and_then(|p| p.parse::<f64>().ok());
    let sol_value_float = sol_value.and_then(|s| s.parse::<f64>().ok());
    
    // 如果没有直接提取到价格，但有SOL值和代币数量，则计算价格
    let calculated_price_float = if price_float.is_none() && sol_value_float.is_some() && amount_parsed > 0 {
        let sol_val = sol_value_float.unwrap();
        let token_amount_ui = amount_parsed as f64 / 1_000_000.0; // 6位精度
        
        if tx_type == "Buy" {
            // Buy: 价格 = SOL输入 / 代币输出
            Some(sol_val / token_amount_ui)
        } else if tx_type == "Sell" {
            // Sell: 价格 = SOL输出 / 代币输入
            Some(sol_val / token_amount_ui)
        } else {
            None
        }
    } else {
        None
    };
    
    // 使用计算的价格或原始价格
    let final_price = price_float.or(calculated_price_float);
    
    // 构造JSON数据 (为了兼容性)
    let data = serde_json::json!({
        "signature": signature.clone(),
        "type": tx_type_value,
        "accounts": [
            {
                "pubkey": signer.clone(),
                "signer": true
            }
        ],
        "transaction": {
            "signatures": [signature.clone()]
        },
        "tokenInfo": token_info,
        "price": final_price,
        "amount": amount_parsed,
        "sol_value": sol_value_float,
        "raw_data": original_data
    });

    // 创建结构体，直接填充字段
    TransactionData {
        signature,
        data,
        tx_type: tx_type_value.to_string(),
        mint,
        signer,
        amount: amount_parsed,
        price: final_price,
        sol_value: sol_value_float,
        token_info,
        slot: 0, // 始终使用0作为槽位，不再尝试获取真实槽位
    }
}

// 新增：计算 Pump.fun Swap 交易限制的函数
// 返回: Ok((instruction_token_amount, instruction_limit_amount))
// instruction_token_amount: 对于 Buy 是 min_token_out_base，对于 Sell 是 token_amount_to_sell_base_units
// instruction_limit_amount: 对于 Buy 是 max_sol_cost_lamports，对于 Sell 是 min_sol_out_lamports
async fn calculate_pump_swap_limits(
    direction: &str, // "buy" or "sell"
    input_amount_ui: f64, // 对于 Buy 是 SOL 数量, 对于 Sell 是 Token UI 数量
    decimals: u8,
    price_estimate: f64,
    slippage_bps: u64,
    logger: &Logger, // 传入 logger 用于记录日志
) -> Result<(u64, u64), anyhow::Error> {
    if price_estimate <= 0.0 {
        return Err(anyhow!("价格估算无效 (<= 0)"));
    }
    if decimals == 0 {
        // 通常代币小数位不会是0，这可能表示获取错误
        logger.warn("代币小数位为 0, 计算可能不准确");
    }

    match direction {
        "buy" => {
            let sol_in_ui = input_amount_ui;
            let max_sol_cost_lamports = (sol_in_ui * 10f64.powi(9)) as u64;

            let expected_token_out_ui = sol_in_ui / price_estimate;
            let expected_token_out_base = (expected_token_out_ui * 10f64.powi(decimals as i32)) as u64;
            let min_token_out_base = (expected_token_out_base as f64 * (1.0 - slippage_bps as f64 / 10000.0)).floor() as u64; // 使用 floor 避免精度问题

            if max_sol_cost_lamports == 0 {
                 return Err(anyhow!("计算出的最大 SOL 成本为 0"));
            }
            if min_token_out_base == 0 {
                 // 允许最小输出为0吗？根据业务逻辑决定，这里先打印警告
                 logger.warn("计算出的最小代币输出为 0");
            }
            
            // 临时修复逻辑：特殊处理出现u64::MAX的情况
            // 仅当值等于u64::MAX时进行处理，否则保持原值不变
            let final_token_out_base = if min_token_out_base == u64::MAX {
                logger.warn("检测到参数为u64::MAX，这可能会导致交易失败，进行安全调整");
                u64::MAX / 2 // 使用一半的u64::MAX作为安全值
            } else {
                min_token_out_base
            };
            
            let final_sol_cost = if max_sol_cost_lamports == u64::MAX {
                logger.warn("检测到参数为u64::MAX，这可能会导致交易失败，进行安全调整");
                u64::MAX / 2 // 使用一半的u64::MAX作为安全值
            } else {
                max_sol_cost_lamports
            };

            logger.debug(format!(
                "[calculate_pump_swap_limits - BUY] Input SOL: {}, Price: {}, Decimals: {}, Slippage BPS: {}",
                sol_in_ui, price_estimate, decimals, slippage_bps
            ));
            logger.debug(format!(
                "[calculate_pump_swap_limits - BUY] Calculated Limits: max_sol_cost_lamports={}, min_token_out_base={}",
                final_sol_cost, final_token_out_base
            ));

            // 对于 Buy 指令: token_amount 是 min_token_out_base, limit_amount 是 max_sol_cost_lamports
            Ok((final_token_out_base, final_sol_cost))
        }
        "sell" => {
            let token_in_ui = input_amount_ui;
            let token_amount_to_sell_base_units = (token_in_ui * 10f64.powi(decimals as i32)).floor() as u64; // 使用 floor

            if token_amount_to_sell_base_units == 0 {
                return Err(anyhow!("根据输入UI数量计算出的卖出基准单位为 0"));
            }

            let expected_sol_out_ui = token_in_ui * price_estimate;
            let expected_sol_out_lamports = (expected_sol_out_ui * 10f64.powi(9)) as u64;
            let min_sol_out_lamports = (expected_sol_out_lamports as f64 * (1.0 - slippage_bps as f64 / 10000.0)).floor() as u64; // 使用 floor

             if min_sol_out_lamports == 0 {
                 // 允许最小 SOL 输出为 0 吗？可能意味着滑点设置过高或价格波动大
                 logger.warn("计算出的最小 SOL 输出为 0 lamports");
             }
             
            // 临时修复逻辑：特殊处理出现u64::MAX的情况
            // 仅当值等于u64::MAX时进行处理，否则保持原值不变
            let final_token_amount = if token_amount_to_sell_base_units == u64::MAX {
                logger.warn("检测到参数为u64::MAX，这可能会导致交易失败，进行安全调整");
                u64::MAX / 2 // 使用一半的u64::MAX作为安全值
            } else {
                token_amount_to_sell_base_units
            };
            
            let final_sol_out = if min_sol_out_lamports == u64::MAX {
                logger.warn("检测到参数为u64::MAX，这可能会导致交易失败，进行安全调整");
                u64::MAX / 2 // 使用一半的u64::MAX作为安全值
            } else {
                min_sol_out_lamports
            };

            logger.debug(format!(
                "[calculate_pump_swap_limits - SELL] Input Token UI: {}, Price: {}, Decimals: {}, Slippage BPS: {}",
                token_in_ui, price_estimate, decimals, slippage_bps
            ));
             logger.debug(format!(
                "[calculate_pump_swap_limits - SELL] Calculated Limits: token_amount_to_sell_base_units={}, min_sol_out_lamports={}",
                final_token_amount, final_sol_out
            ));

            // 对于 Sell 指令: token_amount 是 token_amount_to_sell_base_units, limit_amount 是 min_sol_out_lamports
            Ok((final_token_amount, final_sol_out))
        }
        _ => Err(anyhow!("无效的交易方向: {}", direction)),
    }
}

/// 检查交易警告信息
fn check_transaction_warnings(tx_data: &TransactionData) -> Option<String> {
    // 检查价格是否在合理范围内
    if let Some(price) = tx_data.price {
        // 从钱包配置中获取价格范围
        if let Ok(configs) = MonitorConfigs::load() {
            if let Some(wallet_config) = configs.get_wallet_config(&tx_data.signer) {
                let min_price = wallet_config.min_price_multiplier;
                let max_price = wallet_config.max_price_multiplier;
                
                if price < min_price || price > max_price {
                    return Some(format!("价格 {} 超出设定范围 [{}, {}]", price, min_price, max_price));
                }
            } else {
                // 找不到钱包配置时的警告
                return Some(format!("未找到钱包 {} 的配置，无法验证价格 {}", tx_data.signer, price));
            }
        } else {
            // 加载配置失败时的警告
            return Some(format!("无法加载钱包配置，无法验证价格 {}", price));
        }
    }
    
    // 检查代币信息是否存在
    if tx_data.token_info.is_none() {
        return Some("缓存检查失败: 未找到代币信息".to_string());
    }
    
    None
}
