[package]
name = "copy-trading-bot"
version = "0.1.0"
edition = "2021"

[dependencies]
dotenv = "0.15"
chrono = "0.4.38"
tokio = { version = "1.38.0", features = ["full"] }
solana-sdk = "=1.16.27"
solana-client = "=1.16.27"
solana-account-decoder = "=1.16.27"
solana-transaction-status = "=1.16.27"
spl-token = { version = "4.0.0", features = ["no-entrypoint"] }
spl-token-2022 = { version = "0.9.0", features = ["no-entrypoint"] }
spl-associated-token-account = { version = "2.2.0", features = [
    "no-entrypoint",
] }
spl-token-client = "=0.7.1"
amm-cli = { git = "https://github.com/raydium-io/raydium-library" }
common = { git = "https://github.com/raydium-io/raydium-library" }
anyhow = "1.0.53"
serde = "1.0.203"
serde_json = "1.0.117"
clap = { version = "4.5.7", features = ["derive"] }
# 暂时注释掉 raydium_amm 依赖
# raydium_amm = { git = "https://github.com/raydium-io/raydium-amm", default-features = false, features = [
#     "client",
# ] }
jito-json-rpc-client = { git = "https://github.com/wisarmy/jito-block-engine-json-rpc-client.git", package = "jito-block-engine-json-rpc-client" }
anchor-lang = "=0.29.0"
bytemuck = "1.21.0"
reqwest = { version = "0.11.27", features = ["json", "socks", "native-tls"] }
indicatif = "0.17.8"
rand = "0.8.5"
tracing = "0.1.40"
tracing-subscriber = { version = "0.3", features = ["env-filter", "time"] }
futures-util = "0.3.30"
tokio-tungstenite = { version = "0.26.1", features = ["native-tls"] }
tokio-stream = "0.1.17"
borsh = { version = "1.5.3" }
borsh-derive = "1.5.3"
solana-transaction-status-client-types = "=2.1.0"
url = "2.3.1"
bincode = "1.3.3"
hex = "0.4.3"
lazy_static = "1.4.0"
once_cell = "1.19.0"
bs58 = "0.5.0"
base58 = "0.2.0"
log = "0.4.20"
base64 = "0.13" # 添加 base64 依赖用于主代码
futures = "0.3"
uuid = { version = "1.7.0", features = ["v4", "serde"] } # 添加uuid依赖，包含所有需要的features
# API服务器相关依赖
axum = "0.7.4"
tower-http = { version = "0.5.2", features = ["cors", "trace", "fs"] }
hyper = "1.1.0"
validator = { version = "0.16", features = ["derive"] }
regex = "1.10.3" # 添加正则表达式库
sha2 = "0.10.8" # 添加SHA256哈希支持

# Yellowstone依赖 - 使用GitHub仓库特定分支解决兼容性问题
yellowstone-grpc-client = { git = "https://github.com/rpcpool/yellowstone-grpc-mango.git", branch = "v1.16-mango" }
yellowstone-grpc-proto = { git = "https://github.com/rpcpool/yellowstone-grpc-mango.git", branch = "v1.16-mango" }

# 统一 tonic 版本，强制依赖此版本
tonic = { version = "=0.10.2", features = ["tls"] }
tonic-build = "=0.10.2"
tower = { version = "=0.4.13", features = ["filter"] }
prost = "=0.12.1"
prost-build = "=0.12.1"
prost-types = "=0.12.1"
# proto-conv = "=0.3.0"  # 注释掉，这个包不存在

redis = { version = "0.22.0", features = ["tokio-comp", "connection-manager"] }
rust_decimal = "1.28.0"
rust_decimal_macros = "1.28.0"

notify = "6.1.1"

[dev-dependencies]
base64 = "0.13" # 添加 base64 依赖用于测试

[profile.release]
# ... existing code ...

[[example]]
name = "block_hash_example"
path = "examples/block_hash_example.rs"

[[example]]
name = "zero_slot_example"
path = "examples/zero_slot_example.rs"
