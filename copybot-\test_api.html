<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钱包监控管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #3f51b5;
            --secondary-color: #f50057;
            --success-color: #4caf50;
            --bg-color: rgba(248, 249, 250, 0.6);  /* 背景透明度修改 */
            --card-bg: rgba(255, 255, 255, 0.8);   /* 卡片透明度修改 */
            --text-color: #333333;
        }
        
        /* 添加视频背景样式 */
        #video-background {
            position: fixed;
            right: 0;
            bottom: 0;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            z-index: -1000;
            object-fit: cover;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            padding-bottom: 2rem;
            background-color: transparent; /* 背景透明 */
        }
        /* 调整整体页面布局 */
        .container {
            max-width: 98%;
            padding-left: 5px;
            padding-right: 15px;
        }
        /* 调整所有列宽度 */
        @media (min-width: 992px) {
            .wallet-column {
                flex: 0 0 23%;
                max-width: 23%;
                padding-left: 0;
                padding-right: 10px;
            }
            .form-column {
                flex: 0 0 35%;
                max-width: 35%;
                padding-left: 10px;
                padding-right: 10px;
            }
            .info-column {
                flex: 0 0 42%;
                max-width: 42%;
                padding-left: 10px;
                padding-right: 0;
            }
        }
        .navbar {
            background-color: rgba(63, 81, 181, 0.9); /* 导航栏半透明 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px); /* 添加模糊效果 */
        }
        .navbar-brand {
            font-weight: 600;
            color: white;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: none;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            background-color: var(--card-bg);
            backdrop-filter: blur(10px); /* 添加模糊效果增强可读性 */
        }
        .card-header {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            background-color: transparent;
            font-weight: 600;
            padding: 1rem 1.25rem;
        }
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        .btn-primary:hover {
            background-color: #303f9f;
            border-color: #303f9f;
        }
        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        .btn-danger {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        .address-badge {
            background-color: rgba(227, 242, 253, 0.8);
            color: #2196f3;
            font-family: monospace;
            letter-spacing: 0.5px;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .template-btn {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            background-color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.2s;
        }
        .template-btn:hover {
            background-color: rgba(240, 240, 240, 0.9);
        }
        .template-btn.low {
            border-left: 4px solid #4caf50;
        }
        .template-btn.medium {
            border-left: 4px solid #ff9800;
        }
        .template-btn.high {
            border-left: 4px solid #f44336;
        }
        .table td, .table th {
            vertical-align: middle;
        }
        .wallet-table {
            min-height: 100px;
        }
        #output-box {
            height: 200px;
            overflow: auto;
            font-family: monospace;
            font-size: 0.85rem;
            background-color: rgba(248, 249, 250, 0.7);
            border-radius: 0.25rem;
            border: 1px solid #e9ecef;
            padding: 1rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .wallet-count-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            background-color: rgba(227, 242, 253, 0.8);
            color: #0d6efd;
            border-radius: 50rem;
            font-weight: normal;
        }
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .pulse {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(63, 81, 181, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(63, 81, 181, 0); }
            100% { box-shadow: 0 0 0 0 rgba(63, 81, 181, 0); }
        }
        /* 增加左侧卡片的样式 */
        .wallet-balance-card {
            border-left: 4px solid var(--primary-color);
            margin-bottom: 1.5rem;
        }
        .wallet-balance-card .card-body {
            border-radius: 0 0 10px 10px;
            padding: 0;
        }
        .wallet-balance-card .table {
            margin-bottom: 0;
        }
        .wallet-balance-card .table td {
            padding: 0.85rem 1rem;
            vertical-align: middle;
            border-color: rgba(0,0,0,0.05);
        }
        .wallet-balance-card .table thead th {
            padding: 0.75rem 1rem;
            font-weight: 600;
            background-color: rgba(0,0,0,0.02);
            border-color: rgba(0,0,0,0.05);
        }
        /* 调整按钮组样式，使其更紧凑 */
        .wallet-balance-card .btn-group-sm > .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
            border-radius: 3px;
        }
        .wallet-balance-card .btn-group-sm > .btn:hover {
            z-index: 1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        /* 美化代币行 */
        .wallet-balance-card .token-row:hover {
            background-color: rgba(63, 81, 181, 0.05);
        }
        /* 调整代币余额显示 */
        .token-balance {
            font-weight: 600;
            text-align: right !important;
        }
        /* 美化原生代币标签 */
        .native-token-badge {
            background-color: rgba(227, 242, 253, 0.8);
            color: #0d6efd;
            font-size: 0.75rem;
            padding: 0.15rem 0.5rem;
            border-radius: 50rem;
        }
        /* 货币符号 */
        .token-symbol {
            font-weight: 500;
            display: inline-block;
            padding: 2px 5px;
            border-radius: 4px;
            background-color: rgba(248, 249, 250, 0.7);
        }
        
        /* 交易历史按钮样式 */
        .view-history-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(227, 242, 253, 0.8);
            border-color: rgba(227, 242, 253, 0.8);
            color: #0d6efd;
        }
        .view-history-btn:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            transform: rotate(15deg);
            transition: all 0.2s ease;
        }
        
        /* 交易历史表格样式 */
        #transactionHistoryModal .table {
            border-collapse: separate;
            border-spacing: 0;
        }
        #transactionHistoryModal .table thead th {
            background-color: rgba(248, 249, 250, 0.7);
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
        }
        #transactionHistoryModal .table td,
        #transactionHistoryModal .table th {
            padding: 0.75rem 1rem;
        }
        #transactionHistoryModal .badge {
            font-weight: 500;
            padding: 0.35em 0.65em;
        }
        
        /* 操作列按钮 */
        #transactionHistoryModal .btn-outline-secondary {
            border-color: #e9ecef;
            color: #6c757d;
        }
        #transactionHistoryModal .btn-outline-secondary:hover {
            background-color: rgba(248, 249, 250, 0.7);
            color: #0d6efd;
        }

        /* 添加以下可点击样式 */
        .token-symbol, .address-badge {
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
        }

        .token-symbol:hover, .address-badge:hover {
            background-color: rgba(227, 242, 253, 0.9);
        }

        .token-symbol:after, .address-badge:after {
            content: "📋";
            font-size: 0.7em;
            position: relative;
            top: -1px;
            margin-left: 3px;
            opacity: 0.5;
        }
        
        /* 调整模态框透明度 */
        .modal-content {
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <!-- 添加视频背景 -->
    <video id="video-background" autoplay loop muted playsinline>
        <source src="10.8.mp4" type="video/mp4">
        您的浏览器不支持视频背景
    </video>

    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-wallet2"></i> 钱包监控管理系统
            </a>
            <div class="ms-auto">
                <button id="healthCheck" class="btn btn-outline-light btn-sm">
                    <i class="bi bi-heart-pulse"></i> 健康检查
                </button>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <!-- 左侧 - 钱包余额列表 -->
            <div class="col-lg-3 wallet-column">
                <div class="card mb-4 wallet-balance-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-wallet2"></i> 钱包余额</span>
                        <button id="refreshBalances" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>代币</th>
                                        <th class="text-end">余额</th>
                                        <th class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="balanceTableBody">
                                    <tr>
                                        <td colspan="3" class="text-center py-3 text-muted">
                                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            正在加载数据...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 新增：专用钱包设置卡片 -->
                <div class="card mb-4" style="border-left: 4px solid #9c27b0;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-gear-fill"></i> 专用钱包设置</span>
                        <small class="text-muted">单独交易配置</small>
                    </div>
                    <div class="card-body">
                        <form id="specialWalletForm">
                            <div class="mb-3">
                                <label for="special-wallet-address" class="form-label">钱包地址</label>
                                <input type="text" class="form-control" id="special-wallet-address" placeholder="输入专用钱包地址">
                                <div class="form-text">设置单独的交易参数</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-2">
                                        <label class="form-label small">滑点 (%)</label>
                                        <input type="number" class="form-control form-control-sm" id="special-wallet-slippage" value="0.1" step="0.1" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <label class="form-label small">小费 (%)</label>
                                        <input type="number" class="form-control form-control-sm" id="special-wallet-tip" value="1.0" step="0.1" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-2">
                                        <label class="form-label small">优先费倍数</label>
                                        <input type="number" class="form-control form-control-sm" id="special-wallet-fee" value="6.0" step="0.1" min="0" max="20">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <label class="form-label small">计算限制</label>
                                        <input type="number" class="form-control form-control-sm" id="special-wallet-limit" value="70000" min="1000" max="1400000">
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid mt-3">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-save"></i> 保存专用配置
                                </button>
                            </div>
                        </form>
                        
                        <!-- 添加专用钱包配置列表 -->
                        <div class="mt-4">
                            <h6 class="text-muted mb-3">当前专用钱包配置</h6>
                            <div id="specialWalletList" class="list-group">
                                <div class="text-center py-3 text-muted">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    正在加载数据...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中部 - 添加钱包配置表单 -->
            <div class="col-lg-4 form-column">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-plus-circle"></i> 添加钱包监控</span>
                        <small class="text-muted">自动热更新配置</small>
                    </div>
                    <div class="card-body">
                        <form id="addWalletForm">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="address" name="address" required 
                                       placeholder="输入Solana钱包地址">
                                <label for="address"><i class="bi bi-wallet2"></i> 钱包地址</label>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="follow_percentage" 
                                               name="follow_percentage" min="0" max="100" step="0.1" value="30" required>
                                        <label for="follow_percentage">跟单比例 (%)</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="slippage_percentage" 
                                               name="slippage_percentage" min="0" max="100" step="0.1" value="2" required>
                                        <label for="slippage_percentage">滑点百分比 (%)</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="tip_percentage" 
                                               name="tip_percentage" min="0" max="100" step="0.1" value="1" required>
                                        <label for="tip_percentage">小费百分比 (%)</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="priority_fee" 
                                               name="priority_fee" min="0" value="50000">
                                        <label for="priority_fee">优先费</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="min_price" 
                                               name="min_price_multiplier" min="0" step="0.00000000001" value="0.00000001" required>
                                        <label for="min_price">最低买入价格 (SOL)</label>
                                        <small class="form-text text-muted">设置愿意买入的最低价格，支持极小值如0.00000000001</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="max_price" 
                                               name="max_price_multiplier" min="0" step="0.00000000001" value="999999999" required>
                                        <label for="max_price">最高买入价格 (SOL)</label>
                                        <small class="form-text text-muted">设置愿意买入的最高价格，可以设置任意大的值表示不限制</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="compute_unit_limit" 
                                       name="compute_unit_limit" min="0" value="200000">
                                <label for="compute_unit_limit">计算单元限制</label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> 添加监控钱包
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightning-charge"></i> 快速配置模板
                    </div>
                    <div class="card-body d-flex flex-wrap">
                        <button id="templateLow" class="template-btn low">
                            <i class="bi bi-shield-check"></i> 稳健策略
                            <span class="badge bg-success text-white ms-1">20%</span>
                        </button>
                        <button id="templateMedium" class="template-btn medium">
                            <i class="bi bi-shield-check"></i> 平衡策略
                            <span class="badge bg-warning text-dark ms-1">30%</span>
                        </button>
                        <button id="templateHigh" class="template-btn high">
                            <i class="bi bi-shield-check"></i> 激进策略
                            <span class="badge bg-danger text-white ms-1">50%</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧 - 钱包列表和结果显示 -->
            <div class="col-lg-5 info-column">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>
                            <i class="bi bi-list-ul"></i> 监控钱包列表
                            <span id="wallet-count" class="wallet-count-badge ms-2">0</span>
                        </span>
                        <div>
                            <!-- 添加全局跟单状态和控制按钮 -->
                            <span id="follow-status-badge" class="badge bg-success me-2">跟单启用中</span>
                            <button id="toggleFollowStatus" class="btn btn-sm btn-warning me-2">
                                <i class="bi bi-pause-fill"></i> 暂停跟单
                            </button>
                            <button id="refreshWalletList" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-clockwise"></i> 刷新列表
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive wallet-table">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">钱包地址</th>
                                        <th scope="col">备注</th>
                                        <th scope="col" class="text-center">跟单比例</th>
                                        <th scope="col" class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="walletTableBody">
                                    <tr>
                                        <td colspan="3" class="text-center py-3 text-muted">
                                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            正在加载数据...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-terminal"></i> 操作结果</span>
                        <button id="clearOutput" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="output-box">等待操作...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹出提示框 -->
    <div class="toast-container">
        <div id="toast" class="toast align-items-center text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-info-circle me-2"></i>
                    <span id="toast-message"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="walletDetailModal" tabindex="-1" aria-labelledby="walletDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="walletDetailModalLabel">钱包详细配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">地址</label>
                        <div id="detail-address" class="address-badge"></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">跟单比例</label>
                            <div id="detail-follow" class="badge bg-primary"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">滑点百分比</label>
                            <div id="detail-slippage" class="badge bg-info text-dark"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">小费百分比</label>
                            <div id="detail-tip" class="badge bg-info text-dark"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">优先费</label>
                            <div id="detail-priority" class="badge bg-secondary"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">最低买入价格</label>
                            <div id="detail-min-price" class="badge bg-light text-dark"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">最高买入价格</label>
                            <div id="detail-max-price" class="badge bg-light text-dark"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">计算单元限制</label>
                        <div id="detail-compute" class="badge bg-secondary"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加全局跟单状态模态框 -->
    <div class="modal fade" id="followStatusModal" tabindex="-1" aria-labelledby="followStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="followStatusModalLabel">确认更改跟单状态</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body" id="follow-status-modal-body">
                    您确定要暂停全局跟单功能吗？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" id="confirmToggleFollow">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增：卖出确认模态框 -->
    <div class="modal fade" id="sellConfirmModal" tabindex="-1" aria-labelledby="sellConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sellConfirmModalLabel">确认卖出代币</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>确认要卖出 <span id="sell-token-symbol" class="fw-bold"></span> 代币吗？</p>
                    <div class="mb-3">
                        <label class="form-label fw-bold">代币地址</label>
                        <div id="sell-token-address" class="address-badge"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">卖出百分比</label>
                        <div id="sell-percentage" class="badge bg-primary"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">卖出数量</label>
                        <div id="sell-amount" class="badge bg-info text-dark"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">滑点设置</label>
                        <select class="form-select" id="sell-slippage">
                            <option value="100">1% (默认)</option>
                            <option value="200">2%</option>
                            <option value="500">5%</option>
                            <option value="1000">10%</option>
                            <option value="2000">20%</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmSell">确认卖出</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增：交易历史模态框 -->
    <div class="modal fade" id="transactionHistoryModal" tabindex="-1" aria-labelledby="transactionHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="transactionHistoryModalLabel">交易历史</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">代币地址</label>
                        <div id="history-token-address" class="address-badge"></div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>类型</th>
                                    <th>数量</th>
                                    <th>价格</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                    <th>盈亏</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="transactionHistoryBody">
                                <tr>
                                    <td colspan="7" class="text-center py-3 text-muted">
                                        <div class="spinner-border spinner-border-sm me-2" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        正在加载交易历史...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化 Bootstrap 工具提示
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化时直接调用原函数，确保首次加载时获取数据
            refreshWalletList();
            getFollowStatus();
            refreshBalances(); // 刷新余额列表

            // 专用钱包设置表单提交处理
            document.getElementById('specialWalletForm').addEventListener('submit', function(event) {
                event.preventDefault();
                
                const address = document.getElementById('special-wallet-address').value.trim();
                if (!address) {
                    showToast('请输入有效的钱包地址', 'error');
                    return;
                }
                
                const formData = {
                    wallet_address: address,
                    slippage_percentage: parseFloat(document.getElementById('special-wallet-slippage').value),
                    tip_percentage: parseFloat(document.getElementById('special-wallet-tip').value),
                    priority_fee_multiplier: parseFloat(document.getElementById('special-wallet-fee').value),
                    compute_limit: parseInt(document.getElementById('special-wallet-limit').value),
                    note: "专用钱包"
                };
                
                fetch('/api/special-wallets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('专用钱包设置成功', 'success');
                        // 更新输出信息
                        const outputBox = document.getElementById('output-box');
                        outputBox.innerHTML = `<div>[${new Date().toLocaleTimeString()}] 已设置专用钱包: ${address}</div>` + outputBox.innerHTML;
                        // 清空表单
                        document.getElementById('special-wallet-address').value = '';
                        // 刷新专用钱包列表
                        loadSpecialWallets();
                    } else {
                        showToast('设置专用钱包失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('设置专用钱包错误:', error);
                    showToast('设置专用钱包请求失败', 'error');
                });
            });
            
            // 加载专用钱包列表
            function loadSpecialWallets() {
                const listContainer = document.getElementById('specialWalletList');
                listContainer.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        正在加载数据...
                    </div>
                `;
                
                fetch('/api/special-wallets')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data && data.data.wallets) {
                            const wallets = data.data.wallets;
                            const walletAddresses = Object.keys(wallets);
                            
                            if (walletAddresses.length === 0) {
                                listContainer.innerHTML = `
                                    <div class="text-center py-3 text-muted">
                                        暂无专用钱包配置
                                    </div>
                                `;
                                return;
                            }
                            
                            // 清空容器
                            listContainer.innerHTML = '';
                            
                            // 添加每个钱包的配置信息
                            walletAddresses.forEach(address => {
                                const config = wallets[address];
                                const item = document.createElement('div');
                                item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
                                item.innerHTML = `
                                    <div>
                                        <h6 class="mb-0 address-badge" title="${address}">${address.substring(0, 6)}...${address.substring(address.length - 4)}</h6>
                                        <small class="text-muted d-block">
                                            滑点: ${config.slippage_percentage}% | 
                                            小费: ${config.tip_percentage}% | 
                                            优先费: ${config.priority_fee_multiplier}x
                                        </small>
                                    </div>
                                    <button class="btn btn-sm btn-danger delete-special-wallet" data-address="${address}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                `;
                                listContainer.appendChild(item);
                            });
                            
                            // 添加删除按钮事件监听
                            document.querySelectorAll('.delete-special-wallet').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    const walletAddress = this.getAttribute('data-address');
                                    
                                    if (confirm(`确定要删除专用钱包 ${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)} 的配置吗？`)) {
                                        deleteSpecialWallet(walletAddress);
                                    }
                                });
                            });
                        } else {
                            listContainer.innerHTML = `
                                <div class="text-center py-3 text-danger">
                                    <i class="bi bi-exclamation-circle me-2"></i>
                                    加载专用钱包配置失败
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('加载专用钱包列表失败:', error);
                        listContainer.innerHTML = `
                            <div class="text-center py-3 text-danger">
                                <i class="bi bi-exclamation-circle me-2"></i>
                                加载专用钱包列表失败: ${error.message}
                            </div>
                        `;
                    });
            }
            
            // 删除专用钱包配置
            function deleteSpecialWallet(address) {
                fetch(`/api/special-wallets/${address}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('专用钱包配置已删除', 'success');
                        // 更新输出信息
                        const outputBox = document.getElementById('output-box');
                        outputBox.innerHTML = `<div>[${new Date().toLocaleTimeString()}] 已删除专用钱包配置: ${address}</div>` + outputBox.innerHTML;
                        // 刷新专用钱包列表
                        loadSpecialWallets();
                    } else {
                        showToast('删除专用钱包配置失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除专用钱包配置错误:', error);
                    showToast('删除专用钱包配置请求失败', 'error');
                });
            }
            
            // 页面加载时获取专用钱包列表
            loadSpecialWallets();
        });

        // 添加防抖动函数，避免短时间内多次调用
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }
        
        // 创建一个防抖版本的刷新函数，500毫秒内的多次调用会合并为一次
        const debouncedRefresh = debounce(refreshWalletList, 500);
        const debouncedRefreshBalances = debounce(refreshBalances, 500);

        // 格式化数字为K/M表示法
        function formatBalance(balance) {
            if (balance >= 1000000) {
                return (balance / 1000000).toFixed(2) + 'M';
            } else if (balance >= 1000) {
                return (balance / 1000).toFixed(2) + 'K';
            } else {
                return balance.toFixed(2);
            }
        }

        // 每次刷新后为按钮添加事件监听
        function setupButtonListeners() {
            // 添加卖出按钮事件监听
            document.querySelectorAll('.sell-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const address = this.getAttribute('data-address');
                    const percentage = this.getAttribute('data-percentage');
                    const balance = parseFloat(this.getAttribute('data-balance'));
                    const decimals = parseInt(this.getAttribute('data-decimals'));
                    
                    showSellConfirmDialog(address, percentage, balance, decimals);
                });
            });
            
            // 添加查看历史按钮事件监听
            document.querySelectorAll('.view-history-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const address = this.getAttribute('data-address');
                    showTransactionHistory(address);
                });
            });
        }

        // 刷新余额列表
        function refreshBalances() {
            fetch('/api/wallet_balance')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const balanceTableBody = document.getElementById('balanceTableBody');
                        balanceTableBody.innerHTML = '';

                        // 添加SOL余额行
                        const solRow = document.createElement('tr');
                        solRow.className = 'token-row';
                        solRow.innerHTML = `
                            <td><span class="token-symbol"><strong>SOL</strong></span></td>
                            <td class="token-balance">${formatBalance(data.data.sol_balance)}</td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-sm btn-outline-info view-history-btn" data-address="SOL">
                                        <i class="bi bi-clock-history"></i>
                                    </button>
                                </div>
                            </td>
                        `;
                        balanceTableBody.appendChild(solRow);

                        // 遍历所有代币
                        const tokens = data.data.tokens;
                        for (const [address, token] of Object.entries(tokens)) {
                            // 跳过余额为0的代币
                            const balance = token.balance / Math.pow(10, token.decimals);
                            if (balance <= 0) continue;

                            const row = document.createElement('tr');
                            row.className = 'token-row';
                            row.innerHTML = `
                                <td><span class="token-symbol" title="${address}">${address.substr(0, 4)}...${address.substr(-4)}</span></td>
                                <td class="token-balance">${formatBalance(balance)}</td>
                                <td class="text-center">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <button class="btn btn-sm btn-outline-info view-history-btn me-2" 
                                                data-address="${address}">
                                            <i class="bi bi-clock-history"></i>
                                        </button>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary sell-btn" 
                                                    data-address="${address}" 
                                                    data-percentage="25"
                                                    data-balance="${balance}"
                                                    data-decimals="${token.decimals}">
                                                25%
                                            </button>
                                            <button class="btn btn-outline-primary sell-btn" 
                                                    data-address="${address}" 
                                                    data-percentage="50"
                                                    data-balance="${balance}"
                                                    data-decimals="${token.decimals}">
                                                50%
                                            </button>
                                            <button class="btn btn-outline-primary sell-btn" 
                                                    data-address="${address}" 
                                                    data-percentage="75"
                                                    data-balance="${balance}"
                                                    data-decimals="${token.decimals}">
                                                75%
                                            </button>
                                            <button class="btn btn-danger sell-btn" 
                                                    data-address="${address}" 
                                                    data-percentage="100"
                                                    data-balance="${balance}"
                                                    data-decimals="${token.decimals}">
                                                全部
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            `;
                            balanceTableBody.appendChild(row);
                        }

                        // 如果没有代币，显示提示
                        if (Object.keys(tokens).length === 0) {
                            const emptyRow = document.createElement('tr');
                            emptyRow.innerHTML = `
                                <td colspan="3" class="text-center py-3 text-muted">
                                    没有找到代币
                                </td>
                            `;
                            balanceTableBody.appendChild(emptyRow);
                        }

                        // 设置所有按钮的事件监听器
                        setupButtonListeners();
                    } else {
                        showToast('获取余额失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('获取余额失败:', error);
                    showToast('获取余额失败: ' + error.message, 'danger');
                });
        }

        // 显示卖出确认对话框
        function showSellConfirmDialog(address, percentage, balance, decimals) {
            const modal = new bootstrap.Modal(document.getElementById('sellConfirmModal'));
            
            // 设置模态框内容
            document.getElementById('sell-token-address').textContent = address;
            document.getElementById('sell-token-symbol').textContent = address.substr(0, 4) + '...' + address.substr(-4);
            document.getElementById('sell-percentage').textContent = percentage + '%';
            
            // 计算卖出数量
            const sellAmount = (balance * (parseInt(percentage) / 100)).toFixed(6);
            document.getElementById('sell-amount').textContent = sellAmount;
            
            // 绑定确认卖出按钮事件
            document.getElementById('confirmSell').onclick = function() {
                // 获取选择的滑点
                const slippage = document.getElementById('sell-slippage').value;
                
                // 执行卖出
                sellToken(address, parseInt(percentage), parseInt(slippage));
                
                // 关闭模态框
                modal.hide();
            };
            
            // 显示模态框
            modal.show();
        }

        // 执行代币卖出
        function sellToken(tokenAddress, percentage, slippageBps) {
            // 创建卖出请求
            const sellRequest = {
                token_address: tokenAddress,
                percentage: percentage,
                slippage_bps: slippageBps
            };
            
            // 显示加载中提示
            showToast('正在执行卖出操作...', 'warning');
            
            // 发送卖出请求
            fetch('/api/sell', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sellRequest)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`卖出请求已提交！签名: ${data.signatures ? data.signatures[0] : '处理中'}`, 'success');
                    
                    // 更新输出框
                    const outputBox = document.getElementById('output-box');
                    outputBox.innerHTML += `<div>[${new Date().toLocaleTimeString()}] 卖出 ${percentage}% ${tokenAddress} 请求已提交，签名: ${data.signatures ? data.signatures[0] : '处理中'}</div>`;
                    outputBox.scrollTop = outputBox.scrollHeight;
                    
                    // 1秒后刷新余额
                    setTimeout(refreshBalances, 1000);
                } else {
                    showToast(`卖出失败: ${data.error || data.message}`, 'danger');
                }
            })
            .catch(error => {
                console.error('卖出请求失败:', error);
                showToast('卖出请求失败: ' + error.message, 'danger');
            });
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            
            toast.classList.remove('bg-success', 'bg-danger', 'bg-warning');
            
            if (type === 'success') {
                toast.classList.add('bg-success');
            } else if (type === 'danger') {
                toast.classList.add('bg-danger');
            } else if (type === 'warning') {
                toast.classList.add('bg-warning');
            }
            
            toastMessage.textContent = message;
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 刷新余额按钮事件处理
        document.getElementById('refreshBalances').addEventListener('click', function() {
            refreshBalances();
            showToast('正在刷新余额...', 'success');
        });

        // 自动刷新余额 - 每30秒刷新一次
        setInterval(debouncedRefreshBalances, 30000);
        
        // 显示加载中的动画
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center py-3 text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        正在加载数据...
                    </td>
                </tr>
            `;
        }

        // 处理卖出请求的响应
        function handleSellResponse(data) {
            if (data.success) {
                showToast('卖出请求已提交！', 'success');
                // 1秒后刷新余额列表
                setTimeout(refreshBalances, 1000);
                
                // 更新输出框显示交易详情
                const outputBox = document.getElementById('output-box');
                let output = `<div>[${new Date().toLocaleTimeString()}] 卖出 ${data.token_address} 请求已提交</div>`;
                if (data.signatures && data.signatures.length > 0) {
                    output += `<div>交易签名: ${data.signatures[0]}</div>`;
                }
                outputBox.innerHTML = output + outputBox.innerHTML;
                outputBox.scrollTop = 0;
            } else {
                showToast(`卖出失败: ${data.error || data.message}`, 'danger');
            }
        }

        // 显示钱包详情
        function showWalletDetails(address, wallet) {
            document.getElementById('detail-address').textContent = address;
            document.getElementById('detail-follow').textContent = (wallet.follow_percentage || 'N/A') + '%';
            document.getElementById('detail-slippage').textContent = (wallet.slippage_percentage || 'N/A') + '%';
            document.getElementById('detail-tip').textContent = (wallet.tip_percentage || 'N/A') + '%';
            document.getElementById('detail-min-price').textContent = wallet.min_price_multiplier || 'N/A';
            document.getElementById('detail-max-price').textContent = wallet.max_price_multiplier || 'N/A';
            document.getElementById('detail-priority').textContent = wallet.priority_fee || 'N/A';
            document.getElementById('detail-compute').textContent = wallet.compute_unit_limit || 'N/A';
            
            const modal = new bootstrap.Modal(document.getElementById('walletDetailModal'));
            modal.show();
        }

        // 设置输出内容
        function setOutput(content, isJson = false) {
            const outputBox = document.getElementById('output-box');
            
            if (isJson && typeof content === 'object') {
                outputBox.textContent = JSON.stringify(content, null, 2);
            } else {
                outputBox.textContent = content;
            }
            
            // 添加滚动到底部和高亮效果
            outputBox.scrollTop = outputBox.scrollHeight;
            outputBox.classList.add('fade-in');
            setTimeout(() => {
                outputBox.classList.remove('fade-in');
            }, 300);
        }

        // 清空输出内容
        document.getElementById('clearOutput').addEventListener('click', () => {
            document.getElementById('output-box').textContent = '等待操作...';
        });

        // 健康检查
        document.getElementById('healthCheck').addEventListener('click', async () => {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                setOutput(data, true);
                
                if (data.message && data.message.includes('正常')) {
                    showToast('系统运行正常', 'success');
                } else {
                    showToast('系统可能存在问题', 'warning');
                }
            } catch (error) {
                setOutput('错误: ' + error.message);
                showToast('无法连接到服务器', 'error');
            }
        });

        // 刷新钱包列表
        async function refreshWalletList() {
            try {
                // 添加锁定机制，防止重复刷新
                if (window.isRefreshing) {
                    console.log('钱包列表正在刷新中，跳过本次刷新');
                    return;
                }
                
                window.isRefreshing = true;
                
                const tableBody = document.getElementById('walletTableBody');
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            正在加载数据...
                        </td>
                    </tr>
                `;
                
                // 先获取监控地址列表
                const response = await fetch('/api/monitor-addresses');
                const data = await response.json();
                
                if (data.success && data.data) {
                    tableBody.innerHTML = ''; // 清空表格
                    
                    // 获取targets数组和wallets对象
                    const targets = data.data.targets || [];
                    const wallets = data.data.wallets || {};
                    
                    // 更新钱包计数
                    document.getElementById('wallet-count').textContent = targets.length;
                    
                    if (targets.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="3" class="text-center py-4 text-muted">
                                    <i class="bi bi-inbox fs-4 d-block mb-2"></i>
                                    暂无监控钱包
                                </td>
                            </tr>
                        `;
                        return;
                    }
                    
                    // 再获取钱包配置列表，以获取每个钱包的激活状态
                    let walletConfigs = {};
                    try {
                        const configResponse = await fetch('/api/wallet_configs');
                        const configData = await configResponse.json();
                        
                        if (configData.success && configData.data) {
                            walletConfigs = configData.data;
                        }
                    } catch (error) {
                        console.error('获取钱包配置失败:', error);
                    }
                    
                    // 为每个地址创建表格行
                    targets.forEach(address => {
                        const wallet = wallets[address] || {};
                        // 获取激活状态，如果钱包配置中不存在该地址，则默认为激活状态
                        const walletConfig = walletConfigs[address] || {};
                        const isActive = walletConfig.is_active !== false; // 默认为true
                        
                        // 将激活状态添加到wallet对象中，以便在UI中使用
                        wallet.is_active = isActive;
                        
                        const row = document.createElement('tr');
                        row.className = 'fade-in';
                        
                        // 地址单元格
                        const addressCell = document.createElement('td');
                        
                        // 截断长地址显示
                        const shortAddress = address.length > 15
                            ? address.substring(0, 8) + '...' + address.substring(address.length - 4)
                            : address;
                        
                        addressCell.innerHTML = `<span class="address-badge copy-address" title="点击复制地址" data-address="${address}">${shortAddress}</span>`;
                        row.appendChild(addressCell);

                        // 添加备注单元格 - 新增
                        const noteCell = document.createElement('td');
                        noteCell.className = 'wallet-note-cell';
                        // 从wallet对象中获取备注信息，如果不存在则显示空
                        const walletNote = wallet.note || '';
                        noteCell.innerHTML = walletNote ? `<div class="wallet-note">${walletNote}</div>` : '-';
                        row.appendChild(noteCell);
                        
                        // 跟单比例单元格
                        const followCell = document.createElement('td');
                        followCell.className = 'text-center';
                        
                        // 根据比例显示不同颜色的标记
                        const followPercent = wallet.follow_percentage || 0;
                        let badgeClass = 'bg-success';
                        
                        if (followPercent > 40) {
                            badgeClass = 'bg-danger';
                        } else if (followPercent > 25) {
                            badgeClass = 'bg-warning text-dark';
                        }
                        
                        // 如果钱包已暂停，显示不同的样式
                        if (!isActive) {
                            badgeClass = 'bg-secondary';
                        }
                        
                        followCell.innerHTML = `<span class="badge ${badgeClass}">${followPercent}%</span>`;
                        if (!isActive) {
                            followCell.innerHTML += ' <span class="badge bg-secondary">已暂停</span>';
                        }
                        
                        row.appendChild(followCell);
                        
                        // 操作单元格
                        const actionCell = document.createElement('td');
                        actionCell.className = 'text-center';
                        
                        // 编辑备注按钮 - 新增
                        const noteButton = document.createElement('button');
                        noteButton.className = 'btn btn-sm btn-outline-secondary me-1';
                        noteButton.innerHTML = '<i class="bi bi-pencil-square"></i>';
                        noteButton.title = '编辑备注';
                        
                        // 编辑备注功能
                        noteButton.addEventListener('click', () => {
                            const currentNote = wallet.note || '';
                            const newNote = prompt('请输入备注:', currentNote);
                            if (newNote !== null) {
                                // 调用API更新备注
                                updateWalletNote(address, newNote);
                            }
                        });

                        // 查看详情按钮
                        const viewButton = document.createElement('button');
                        viewButton.className = 'btn btn-sm btn-outline-primary me-1';
                        viewButton.innerHTML = '<i class="bi bi-eye"></i>';
                        viewButton.title = '查看详情';
                        
                        // 查看详情功能
                        viewButton.addEventListener('click', () => {
                            showWalletDetails(address, wallet);
                        });
                        
                        // 添加单独的暂停/恢复按钮
                        const toggleButton = document.createElement('button');
                        toggleButton.className = wallet.is_active !== false ? 'btn btn-sm btn-outline-warning me-1' : 'btn btn-sm btn-outline-success me-1';
                        toggleButton.innerHTML = wallet.is_active !== false ? '<i class="bi bi-pause-fill"></i>' : '<i class="bi bi-play-fill"></i>';
                        toggleButton.title = wallet.is_active !== false ? '暂停此钱包' : '恢复此钱包';
                        
                        // 暂停/恢复单个钱包功能
                        toggleButton.addEventListener('click', async () => {
                            const action = wallet.is_active !== false ? 'pause' : 'resume';
                            const confirmMsg = wallet.is_active !== false 
                                ? `确定要暂停钱包 ${shortAddress} 的跟单吗？` 
                                : `确定要恢复钱包 ${shortAddress} 的跟单吗？`;
                                
                            if (confirm(confirmMsg)) {
                                try {
                                    const response = await fetch(`/api/wallet_configs/${action}`, {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({ wallet_address: address }),
                                    });
                                    
                                    const data = await response.json();
                                    setOutput(data, true);
                                    
                                    if (data.success) {
                                        showToast(wallet.is_active !== false ? `已暂停钱包 ${shortAddress}` : `已恢复钱包 ${shortAddress}`, 'success');
                                        // 使用防抖版本的刷新函数
                                        debouncedRefresh();
                                    } else {
                                        showToast(wallet.is_active !== false ? '暂停失败' : '恢复失败' + data.message, 'error');
                                    }
                                } catch (error) {
                                    setOutput('操作错误: ' + error.message);
                                    showToast('请求失败', 'error');
                                }
                            }
                        });
                        
                        // 删除按钮
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'btn btn-sm btn-outline-danger';
                        deleteButton.innerHTML = '<i class="bi bi-trash"></i>';
                        deleteButton.title = '删除';
                        
                        // 删除功能
                        deleteButton.addEventListener('click', async () => {
                            if (confirm(`确定要删除钱包 ${shortAddress} 的监控配置吗？`)) {
                                try {
                                    const deleteResponse = await fetch('/api/monitor-addresses/delete', {
                                        method: 'DELETE',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({ address }),
                                    });
                                    
                                    const deleteData = await deleteResponse.json();
                                    setOutput(deleteData, true);
                                    
                                    if (deleteData.success) {
                                        showToast(`已删除钱包 ${shortAddress}`, 'success');
                                        // 使用防抖版本的刷新函数
                                        debouncedRefresh();
                                    } else {
                                        showToast('删除失败: ' + deleteData.message, 'error');
                                    }
                                } catch (error) {
                                    setOutput('删除错误: ' + error.message);
                                    showToast('删除请求失败', 'error');
                                }
                            }
                        });
                        
                        actionCell.appendChild(noteButton);
                        actionCell.appendChild(viewButton);
                        actionCell.appendChild(toggleButton);
                        actionCell.appendChild(deleteButton);
                        row.appendChild(actionCell);
                        
                        tableBody.appendChild(row);
                    });
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="3" class="text-center py-3 text-muted">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                获取钱包列表失败
                            </td>
                        </tr>
                    `;
                    showToast('获取钱包列表失败', 'error');
                }
            } catch (error) {
                document.getElementById('walletTableBody').innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center py-3 text-muted">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            连接服务器失败
                        </td>
                    </tr>
                `;
                setOutput('刷新列表错误: ' + error.message);
                showToast('连接服务器失败', 'error');
            } finally {
                // 重置锁定状态
                window.isRefreshing = false;
            }
        }

        // 点击刷新按钮时更新列表
        document.getElementById('refreshWalletList').addEventListener('click', () => {
            // 手动点击刷新按钮时直接调用刷新函数
            refreshWalletList();
            showToast('已刷新钱包列表', 'success');
        });

        // 处理表单提交
        document.getElementById('addWalletForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // 获取提交按钮并显示加载状态
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>添加中...';
            
            // 收集表单数据
            const formData = {
                address: document.getElementById('address').value,
                follow_percentage: parseFloat(document.getElementById('follow_percentage').value),
                slippage_percentage: parseFloat(document.getElementById('slippage_percentage').value),
                tip_percentage: parseFloat(document.getElementById('tip_percentage').value),
                min_price_multiplier: parseFloat(document.getElementById('min_price').value),
                max_price_multiplier: parseFloat(document.getElementById('max_price').value),
                priority_fee: parseInt(document.getElementById('priority_fee').value) || undefined,
                compute_unit_limit: parseInt(document.getElementById('compute_unit_limit').value) || undefined
            };
            
            try {
                // 1. 添加监控地址
                const response = await fetch('/api/monitor-addresses/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });
                
                const data = await response.json();
                setOutput(data, true);
                
                if (data.success) {
                    showToast('成功添加监控钱包', 'success');
                    
                    // 2. 无论钱包配置是否存在，都尝试创建或更新
                    try {
                        // 先检查配置是否已存在
                        const checkResponse = await fetch(`/api/wallet_configs/${formData.address}`);
                        let walletConfigExists = false;
                        
                        if (checkResponse.ok) {
                            const checkData = await checkResponse.json();
                            walletConfigExists = checkData.success && checkData.data;
                        }
                        
                        let walletConfigResponse;
                        if (walletConfigExists) {
                            // 如果存在则更新
                            walletConfigResponse = await fetch('/api/wallet_configs', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    wallet_address: formData.address,
                                    config: {
                                        is_active: true,
                                        follow_percentage: formData.follow_percentage,
                                        fee_increase_percentage: 1.0
                                    }
                                }),
                            });
                        } else {
                            // 如果不存在则创建新配置
                            walletConfigResponse = await fetch('/api/wallet_configs', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    wallet_address: formData.address,
                                    config: {
                                        is_active: true,
                                        follow_percentage: formData.follow_percentage,
                                        fee_increase_percentage: 1.0
                                    }
                                }),
                            });
                        }
                        
                        const walletConfigData = await walletConfigResponse.json();
                        
                        // 如果操作成功，记录到日志
                        if (walletConfigData.success) {
                            setOutput({
                                ...data,
                                additional_info: walletConfigExists 
                                    ? "钱包配置已更新，可以使用暂停/恢复功能" 
                                    : "钱包配置已同步创建，可以使用暂停/恢复功能"
                            }, true);
                        } else {
                            // 如果操作失败，也记录到日志，但不影响主流程
                            setOutput({
                                ...data,
                                wallet_config_error: walletConfigData.message || "操作钱包配置失败" 
                            }, true);
                        }
                    } catch (configError) {
                        // 创建钱包配置时出错，记录到日志但不影响主流程
                        console.error("操作钱包配置失败:", configError);
                        setOutput({
                            ...data,
                            wallet_config_error: configError.message || "操作钱包配置失败"
                        }, true);
                    }
                    
                    document.getElementById('address').value = ''; // 清空地址输入框
                    // 使用防抖版本的刷新函数
                    debouncedRefresh();
                } else {
                    showToast('添加失败: ' + data.message, 'error');
                }
            } catch (error) {
                setOutput('错误: ' + error.message);
                showToast('请求失败，请检查服务器连接', 'error');
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }
        });

        // 快速配置模板 - 稳健策略
        document.getElementById('templateLow').addEventListener('click', () => {
            document.getElementById('follow_percentage').value = 20;
            document.getElementById('slippage_percentage').value = 1.0;
            document.getElementById('tip_percentage').value = 0.5;
            document.getElementById('min_price').value = 0.00000002;
            document.getElementById('max_price').value = 999999;
            document.getElementById('priority_fee').value = 40000;
            document.getElementById('compute_unit_limit').value = 150000;
            showToast('已应用稳健策略配置', 'success');
        });

        // 快速配置模板 - 平衡策略
        document.getElementById('templateMedium').addEventListener('click', () => {
            document.getElementById('follow_percentage').value = 30;
            document.getElementById('slippage_percentage').value = 2.0;
            document.getElementById('tip_percentage').value = 1.0;
            document.getElementById('min_price').value = 0.00000005;
            document.getElementById('max_price').value = 999999;
            document.getElementById('priority_fee').value = 50000;
            document.getElementById('compute_unit_limit').value = 200000;
            showToast('已应用平衡策略配置', 'success');
        });

        // 快速配置模板 - 激进策略
        document.getElementById('templateHigh').addEventListener('click', () => {
            document.getElementById('follow_percentage').value = 50;
            document.getElementById('slippage_percentage').value = 3.0;
            document.getElementById('tip_percentage').value = 1.5;
            document.getElementById('min_price').value = 0.00000009;
            document.getElementById('max_price').value = 999999;
            document.getElementById('priority_fee').value = 60000;
            document.getElementById('compute_unit_limit').value = 250000;
            showToast('已应用激进策略配置', 'success');
        });

        // 跟单状态控制
        let isFollowPaused = false;
        const followStatusBadge = document.getElementById('follow-status-badge');
        const toggleFollowBtn = document.getElementById('toggleFollowStatus');
        const confirmToggleBtn = document.getElementById('confirmToggleFollow');
        const followStatusModal = new bootstrap.Modal(document.getElementById('followStatusModal'));
        const followStatusModalBody = document.getElementById('follow-status-modal-body');

        // 页面加载时获取当前跟单状态
        async function getFollowStatus() {
            try {
                const response = await fetch('/api/follow/status');
                const data = await response.json();
                
                if (data.success) {
                    updateFollowStatusUI(data.status === 'paused');
                    setOutput(data, true);
                } else {
                    showToast('获取跟单状态失败', 'error');
                }
            } catch (error) {
                setOutput('获取跟单状态错误: ' + error.message);
                showToast('无法连接到服务器', 'error');
            }
        }

        // 更新UI显示跟单状态
        function updateFollowStatusUI(isPaused) {
            isFollowPaused = isPaused;
            
            if (isPaused) {
                followStatusBadge.className = 'badge bg-danger me-2';
                followStatusBadge.textContent = '跟单已暂停';
                toggleFollowBtn.className = 'btn btn-sm btn-success me-2';
                toggleFollowBtn.innerHTML = '<i class="bi bi-play-fill"></i> 恢复跟单';
            } else {
                followStatusBadge.className = 'badge bg-success me-2';
                followStatusBadge.textContent = '跟单启用中';
                toggleFollowBtn.className = 'btn btn-sm btn-warning me-2';
                toggleFollowBtn.innerHTML = '<i class="bi bi-pause-fill"></i> 暂停跟单';
            }
        }

        // 点击切换跟单状态按钮
        toggleFollowBtn.addEventListener('click', () => {
            followStatusModalBody.textContent = isFollowPaused 
                ? '您确定要恢复全局跟单功能吗？' 
                : '您确定要暂停全局跟单功能吗？这将停止所有新交易的跟单。';
            
            confirmToggleBtn.className = isFollowPaused 
                ? 'btn btn-success' 
                : 'btn btn-warning';
            
            confirmToggleBtn.textContent = isFollowPaused ? '确认恢复' : '确认暂停';
            
            followStatusModal.show();
        });

        // 确认切换跟单状态
        confirmToggleBtn.addEventListener('click', async () => {
            followStatusModal.hide();
            
            try {
                const url = isFollowPaused 
                    ? '/api/follow/resume' 
                    : '/api/follow/pause';
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateFollowStatusUI(!isFollowPaused);
                    showToast(isFollowPaused ? '已恢复跟单' : '已暂停跟单', 'success');
                    setOutput(data, true);
                } else {
                    showToast(isFollowPaused ? '恢复跟单失败' : '暂停跟单失败', 'error');
                }
            } catch (error) {
                setOutput('操作失败: ' + error.message);
                showToast('无法连接到服务器', 'error');
            }
        });

        // 显示交易历史
        function showTransactionHistory(tokenAddress) {
            // 设置模态框标题和代币地址
            const shortAddress = tokenAddress === 'SOL' ? 'SOL' : 
                tokenAddress.length > 15 ? tokenAddress.substring(0, 8) + '...' + tokenAddress.substring(tokenAddress.length - 4) : tokenAddress;
            
            document.getElementById('transactionHistoryModalLabel').textContent = 
                `${shortAddress} 交易历史`;
            document.getElementById('history-token-address').textContent = tokenAddress;
            
            // 显示加载状态
            document.getElementById('transactionHistoryBody').innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-3 text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        正在加载交易历史...
                    </td>
                </tr>
            `;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('transactionHistoryModal'));
            modal.show();
            
            // 请求交易历史数据
            const url = `/api/transaction_history?token=${encodeURIComponent(tokenAddress)}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateTransactionHistoryTable(data.data.transactions, tokenAddress);
                    } else {
                        showTransactionHistoryError('获取交易历史失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取交易历史失败:', error);
                    showTransactionHistoryError('获取交易历史失败: ' + error.message);
                });
        }
        
        // 更新交易历史表格
        function updateTransactionHistoryTable(transactions, tokenAddress) {
            const tbody = document.getElementById('transactionHistoryBody');
            
            // 如果没有交易历史记录
            if (!transactions || transactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-3 text-muted">
                            <i class="bi bi-inbox"></i> 暂无交易记录
                        </td>
                    </tr>
                `;
                return;
            }
            
            // 清空表格
            tbody.innerHTML = '';
            
            // 填充交易记录
            transactions.forEach(tx => {
                // 格式化时间
                const txDate = new Date(tx.timestamp);
                const formattedDate = `${txDate.getFullYear()}-${(txDate.getMonth()+1).toString().padStart(2, '0')}-${txDate.getDate().toString().padStart(2, '0')} ${txDate.getHours().toString().padStart(2, '0')}:${txDate.getMinutes().toString().padStart(2, '0')}:${txDate.getSeconds().toString().padStart(2, '0')}`;
                
                // 根据交易类型设置样式
                let typeClass = '';
                let typeIcon = '';
                
                if (tx.type === 'buy') {
                    typeClass = 'text-success';
                    typeIcon = 'bi-arrow-down-circle-fill';
                } else if (tx.type === 'sell') {
                    typeClass = 'text-danger';
                    typeIcon = 'bi-arrow-up-circle-fill';
                }

                // 格式化数量为K/M表示法
                let formattedAmount = '';
                if (tx.formatted_amount) {
                    // 优先使用后端返回的格式化字段
                    formattedAmount = tx.formatted_amount;
                } else if (typeof tx.amount === 'string') {
                    // 如果已经是格式化的字符串，直接使用
                    formattedAmount = tx.amount;
                } else {
                    // 否则进行格式化
                    const amount = parseFloat(tx.amount || tx.raw_amount || 0);
                    if (amount >= 1000000) {
                        formattedAmount = (amount / 1000000).toFixed(2) + 'M';
                    } else if (amount >= 1000) {
                        formattedAmount = (amount / 1000).toFixed(2) + 'K';
                    } else {
                        formattedAmount = amount.toFixed(2);
                    }
                }

                // 格式化价格
                let formattedPrice = '';
                if (tx.formatted_price) {
                    // 优先使用后端返回的格式化字段
                    formattedPrice = tx.formatted_price + ' SOL';
                } else if (typeof tx.price === 'string') {
                    // 如果已经是格式化的字符串，直接使用
                    formattedPrice = tx.price;
                    if (!formattedPrice.includes('SOL')) {
                        formattedPrice += ' SOL';
                    }
                } else {
                    // 否则进行格式化
                    const price = parseFloat(tx.price || tx.raw_price || 0);
                    if (price === 0) {
                        formattedPrice = '0.00 SOL';
                    } else if (price < 0.0001) {
                        // 显示前导0的个数和有效数字，使用新格式 0.(n)xxx
                        const priceStr = price.toFixed(10);
                        const dotIndex = priceStr.indexOf('.');
                        const decimals = priceStr.substring(dotIndex + 1);
                        let zeroCount = 0;
                        
                        // 计算前导0的数量
                        for (let i = 0; i < decimals.length; i++) {
                            if (decimals[i] === '0') {
                                zeroCount++;
                            } else {
                                break;
                            }
                        }
                        
                        // 取非零部分
                        const nonZeroPart = decimals.substring(zeroCount).replace(/0+$/, '');
                        formattedPrice = `0.(${zeroCount})${nonZeroPart} SOL`;
                    } else {
                        // 根据价格大小使用不同精度
                        if (price >= 1.0) {
                            formattedPrice = price.toFixed(2) + ' SOL';
                        } else if (price >= 0.1) {
                            formattedPrice = price.toFixed(3) + ' SOL';
                        } else if (price >= 0.01) {
                            formattedPrice = price.toFixed(4) + ' SOL';
                        } else if (price >= 0.001) {
                            formattedPrice = price.toFixed(5) + ' SOL';
                        } else if (price >= 0.0001) {
                            formattedPrice = price.toFixed(6) + ' SOL';
                        } else {
                            formattedPrice = price.toFixed(8) + ' SOL';
                        }
                    }
                }
                
                // 创建行
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="${typeClass}">
                        <i class="bi ${typeIcon} me-1"></i>
                        ${tx.type === 'buy' ? '买入' : '卖出'}
                    </td>
                    <td>${formattedAmount}</td>
                    <td>${formattedPrice}</td>
                    <td>${formattedDate}</td>
                    <td>
                        <span class="badge ${tx.status === 'success' ? 'bg-success' : 'bg-danger'}">
                            ${tx.status === 'success' ? '成功' : '失败'}
                        </span>
                    </td>
                    <td>
                        ${formatProfitLoss(tx)}
                    </td>
                    <td>
                        <a href="https://solscan.io/tx/${tx.signature}" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-box-arrow-up-right"></i> 查看
                        </a>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        // 格式化盈亏信息
        function formatProfitLoss(tx) {
            // 如果是买入交易，显示为0
            if (tx.type === 'buy') {
                return '<span class="text-muted">0.00 SOL (0.00%)</span>';
            }
            
            // 如果没有盈亏数据，显示为N/A
            if (tx.profit_loss === null || tx.profit_loss === undefined) {
                return '<span class="text-muted">N/A</span>';
            }
            
            const profit = parseFloat(tx.profit_loss);
            const profitPercentage = parseFloat(tx.profit_loss_percentage);
            
            // 根据盈亏情况显示不同颜色
            let badgeClass = '';
            if (profit > 0) {
                badgeClass = 'text-success';
            } else if (profit < 0) {
                badgeClass = 'text-danger';
            } else {
                badgeClass = 'text-muted';
            }
            
            // 格式化盈亏金额和百分比
            const formattedProfit = profit.toFixed(4);
            const formattedPercentage = profitPercentage.toFixed(2);
            const sign = profit > 0 ? '+' : '';
            
            return `<span class="${badgeClass}">${sign}${formattedProfit} SOL (${sign}${formattedPercentage}%)</span>`;
        }
        
        // 显示交易历史错误
        function showTransactionHistoryError(errorMessage) {
            document.getElementById('transactionHistoryBody').innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-3 text-danger">
                        <i class="bi bi-exclamation-triangle-fill me-1"></i>
                        ${errorMessage}
                    </td>
                </tr>
            `;
        }

        // 添加更新备注函数
        function updateWalletNote(address, note) {
            fetch('/api/monitor-addresses/update-note', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ address, note }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('备注已更新', 'success');
                    // 使用防抖版本的刷新函数
                    debouncedRefresh();
                } else {
                    showToast('更新备注失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新备注失败:', error);
                showToast('更新备注失败', 'error');
            });
        }

        // 添加复制功能 - 全局点击事件处理
        document.addEventListener('click', function(event) {
            // 检查是否点击的是带有地址信息的元素
            let targetElement = null;
            
            // 检查是否点击的是copy-address类的元素
            if (event.target.classList.contains('copy-address')) {
                targetElement = event.target;
            } 
            // 检查是否点击的是token-symbol类的元素(代币地址)
            else if (event.target.classList.contains('token-symbol')) {
                targetElement = event.target;
                // 如果元素有title属性且长度符合地址特征，则视为可复制地址
                if (targetElement.title && targetElement.title.length > 30) {
                    targetElement.dataset.address = targetElement.title;
                }
            }
            // 检查是否点击的是address-badge类的元素(钱包地址)
            else if (event.target.classList.contains('address-badge')) {
                targetElement = event.target;
                // 获取显示的文本作为地址
                if (!targetElement.dataset.address && targetElement.textContent) {
                    const text = targetElement.textContent.trim();
                    // 如果文本包含省略号(...)，可能是简短版本的地址，使用title属性
                    if (text.includes('...') && targetElement.title) {
                        targetElement.dataset.address = targetElement.title;
                    } else if (text.length >= 32) {
                        // 否则如果文本长度符合Solana地址特征
                        targetElement.dataset.address = text;
                    }
                }
            }
            
            // 如果找到了可复制的元素且有地址数据
            if (targetElement && targetElement.dataset.address) {
                // 复制地址到剪贴板
                navigator.clipboard.writeText(targetElement.dataset.address)
                    .then(() => {
                        showToast('地址已复制: ' + targetElement.dataset.address.substring(0, 8) + '...', 'success');
                        
                        // 添加视觉反馈效果
                        const originalBg = targetElement.style.backgroundColor;
                        targetElement.style.backgroundColor = '#d6eaff';
                        setTimeout(() => {
                            targetElement.style.backgroundColor = originalBg;
                        }, 500);
                    })
                    .catch(err => {
                        console.error('复制失败:', err);
                        showToast('复制失败', 'error');
                    });
            }
        });
    </script>
</body>
</html> 