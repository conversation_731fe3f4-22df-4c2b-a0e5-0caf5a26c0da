{"name": "错误处理优先级规则", "description": "定义项目中错误和警告的处理优先级", "rules": {"priority": {"critical_errors": 1, "runtime_errors": 2, "compile_errors": 3, "type_errors": 4, "breaking_changes": 5, "critical_warnings": 6, "other_warnings": "ignore"}, "warning_handling": {"only_fix_if": ["affects_functionality", "causes_test_failures", "specifically_requested", "prevents_compilation"], "ignore_categories": ["unused_imports", "dead_code", "unused_variables"]}, "error_retry": {"rpc_errors": {"retry": true, "max_attempts": 3, "backoff_ms": 1000}, "grpc_errors": {"fallback_to_rpc": true, "log_level": "warn"}}, "error_analysis": {"analyze_before_fix": true, "required_steps": ["read_file_with_error", "identify_error_context", "find_root_cause", "review_related_code"], "avoid_behaviors": ["blind_fixes", "guessing_solutions", "partial_file_reading"], "documentation": "遇到错误时，必须先完整阅读相关文件内容，分析错误上下文，找到根本原因，再进行修复，避免盲目猜测或部分理解后的草率修复"}}}